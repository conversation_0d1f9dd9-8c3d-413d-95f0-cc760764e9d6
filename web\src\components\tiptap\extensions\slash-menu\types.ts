import type { Editor } from '@tiptap/core'

export interface SlashMenuItemConfig {
  id: string
  name: string
  icon: string
  keywords: string
  shortcut?: string
  action: (editor: Editor) => void
}

export type SlashMenuItem = SlashMenuItemConfig | '|'

export interface SlashMenuViewOptions {
  editor: Editor
  dictionary?: {
    empty?: string
  }
  classes?: string[]
  attributes?: Record<string, string>
}
