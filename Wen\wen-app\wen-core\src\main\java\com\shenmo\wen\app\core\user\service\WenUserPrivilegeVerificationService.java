package com.shenmo.wen.app.core.user.service;

import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeVerificationStartReq;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeVerificationSubmitReq;
import com.shenmo.wen.app.core.user.pojo.resp.WenVerificationTimeInfoResp;
import com.shenmo.wen.app.core.user.pojo.resp.WenUserPrivilegeVerificationResp;

/**
 * 用户特权验证服务接口
 * 
 * <AUTHOR>
 */
public interface WenUserPrivilegeVerificationService {

    /**
     * 启动特权验证流程
     * 
     * @param param 启动参数
     * @return 验证流程信息
     */
    WenUserPrivilegeVerificationResp startVerification(WenUserPrivilegeVerificationStartReq req);

    /**
     * 查询验证流程状态
     *
     * @param verificationId 验证流程ID
     * @return 验证流程信息
     */
    WenUserPrivilegeVerificationResp verificationStatus(Long verificationId);

    /**
     * 提交验证内容（步骤二）
     *
     * @param req 提交参数
     * @return 是否成功
     */
    Boolean submitVerificationContent(Long id, WenUserPrivilegeVerificationSubmitReq req);

    /**
     * 触发步骤三（用户B访问验证页面时调用）
     * 
     * @param verificationId 验证流程ID
     * @return 是否成功
     */
    Boolean triggerStepThree(Long verificationId);

    /**
     * 处理过期的验证流程
     */
    void handleExpiredVerifications();

    /**
     * 清理MinIO验证页面资源
     *
     * @param minioPath MinIO资源路径
     */
    void cleanupVerificationPage(String minioPath);

    /**
     * 启动验证计时器（用户点击邮件链接访问页面时调用）
     *
     * @param verificationId 验证流程ID
     * @return 时间信息
     */
    WenVerificationTimeInfoResp startVerificationTimer(Long verificationId);

    /**
     * 获取验证剩余时间
     *
     * @param verificationId 验证流程ID
     * @return 时间信息
     */
    WenVerificationTimeInfoResp remainingTime(Long verificationId);
}
