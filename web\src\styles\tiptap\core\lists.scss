// 列表样式
.ProseMirror {
  // 普通列表样式
  ul,
  ol {
    margin: 0.5rem;

    li {
      transition: background-color 0.2s ease;

      &:focus-within {
        background-color: rgba(90, 214, 150, 5%);
        border-radius: 4px;
      }

      p {
        margin-top: 0.15em;
        margin-bottom: 0.15em;
        position: relative;
      }
    }
  }

  // 无序列表样式 - 多层级符号
  ul {
    padding: 0 1rem;
    list-style-position: outside;
    list-style-type: disc; // 第一层：实心圆点

    // 第二层：空心圆圈
    ul {
      list-style-type: circle;

      // 第三层：实心方块
      ul {
        list-style-type: square;

        // 第四层及以后：短横线
        ul {
          list-style-type: disc;

          ul {
            list-style-type: circle;

            ul {
              list-style-type: square;
            }
          }
        }
      }
    }
  }

  // 有序列表样式 - 多层级序号
  ol {
    padding-left: 1.25rem; // 嵌套列表保持相同的左边距
    list-style-position: outside; // 确保序号显示在内容区域外
    list-style-type: decimal; // 第一层：阿拉伯数字 (1, 2, 3...)

    // 第二层：小写字母
    ol {
      padding-left: 1.25rem;
      list-style-type: lower-alpha; // (a, b, c...)

      // 第三层：小写罗马数字
      ol {
        list-style-type: lower-roman; // (i, ii, iii...)

        // 第四层：大写字母
        ol {
          list-style-type: upper-alpha; // (A, B, C...)

          // 第五层：大写罗马数字
          ol {
            list-style-type: upper-roman; // (I, II, III...)

            // 第六层及以后：回到阿拉伯数字
            ol {
              list-style-type: decimal;
            }
          }
        }
      }
    }
  }

  // 任务列表样式
  ul[data-type='taskList'] {
    list-style: none;
    margin-left: 0;
    padding: 0;

    li {
      align-items: flex-start;
      display: flex;
      position: relative;
      padding: 2px 4px;
      border-radius: 4px;
      transition:
        background-color 0.2s ease,
        box-shadow 0.2s ease;

      &:focus-within {
        background-color: rgba(90, 214, 150, 5%);
        box-shadow: 0 0 0 1px rgba(90, 214, 150, 20%);
      }

      > label {
        flex: 0 0 auto;
        user-select: none;
      }

      > div {
        flex: 1 1 auto;
        position: relative;
      }
    }

    input[type='checkbox'] {
      cursor: pointer;
      position: relative;

      &:checked {
        accent-color: #2d8cf0;
      }
    }

    ul[data-type='taskList'] {
      margin: 0;
    }
  }
}

// 只读模式下的任务列表样式
.editor-readonly {
  .ProseMirrorInput,
  .ProseMirror {
    ul[data-type='taskList'] {
      input[type='checkbox'] {
        pointer-events: none;
        box-shadow: none;
        outline: none;
      }

      // 添加自定义禁用样式
      .cst-task-checkbox-wrapper {
        pointer-events: none;
        cursor: not-allowed;
        transition: none !important; // 禁用所有过渡动画
        transform: none !important; // 禁用任何变换

        &:hover {
          transform: none !important;
        }
      }

      // 禁用标签悬浮效果
      > label {
        transition: none !important;

        &:hover {
          transform: none !important;
        }
      }
    }
  }
}
