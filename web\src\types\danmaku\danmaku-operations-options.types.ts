/**
 * 弹幕操作选项类型定义
 */

import type { CustomDanmu } from './custom-danmu.types'
import type { DanmakuChannelItem } from './danmaku-channel-item.types'
import type { DanmakuEmitFunction } from './danmaku-emit-function.types'
import type { DanmakuFullConfig } from './danmaku-full-config.types'
import type { DanmakuStyleConfig } from './danmaku-style.types'
import type { Danmu } from './danmu.types'
import type { ComputedRef, Ref, VNode } from 'vue'

/**
 * 弹幕操作选项接口
 */
export interface DanmakuOperationsOptions {
  /** 弹幕列表 */
  danmuList: Danmu[]
  /** 事件发射器 */
  emit: DanmakuEmitFunction
  /** 弹幕容器元素 */
  dmContainer: HTMLElement | null
  /** 弹幕配置选项 */
  danmuOptions: DanmakuFullConfig
}

/**
 * 弹幕操作组合式函数配置选项
 */
export interface DanmakuOperationsComposableOptions {
  /** 组件属性 */
  props: DanmakuComponentProps
  /** 事件发射器 */
  emit: DanmakuEmitFunction
  /** 插槽配置 */
  slots: { dm?: (props: { danmu: CustomDanmu; index: number }) => VNode }
  /** 弹幕配置 */
  danmakuConfig: DanmakuFullConfig
  /** 弹幕样式 */
  danmuStyle: DanmakuStyleConfig
  /** 弹幕列表 */
  danmuList: ComputedRef<Danmu[]>
  /** 容器元素引用 */
  container: Ref<HTMLDivElement>
  /** 弹幕容器元素引用 */
  dmContainer: Ref<HTMLDivElement>
  /** 容器宽度 */
  containerWidth: Ref<number>
  /** 容器高度 */
  containerHeight: Ref<number>
  /** 计算的轨道数 */
  calcChannels: Ref<number>
  /** 弹幕高度 */
  danmuHeight: Ref<number>
  /** 当前索引 */
  index: Ref<number>
  /** 是否隐藏 */
  hidden: Ref<boolean>
  /** 是否暂停 */
  paused: Ref<boolean>
  /** 获取轨道索引函数 */
  getChannelIndex: (el: HTMLDivElement, dmContainer: HTMLDivElement) => number
  /** 弹幕轨道映射 */
  danChannel: DanmakuChannelMap
  /** 定时器引用 */
  timer: Ref<number>
}

/**
 * 弹幕组件属性接口
 */
export interface DanmakuComponentProps {
  /** 弹幕数据数组 */
  danmus: Danmu[]
  /** 弹幕轨道数量 */
  channels: number
  /** 是否自动播放 */
  autoplay: boolean
  /** 是否循环播放 */
  loop: boolean
  /** 是否使用插槽 */
  useSlot: boolean
  /** 防抖延迟时间 */
  debounce: number
  /** 弹幕移动速度 */
  speeds: number
  /** 弹幕字体大小 */
  fontSize: number
  /** 弹幕顶部偏移量 */
  top: number
  /** 弹幕右侧偏移量 */
  right: number
  /** 是否暂停弹幕 */
  isSuspend: boolean
  /** 额外的CSS样式 */
  extraStyle: string
  /** 索引签名，允许动态属性访问 */
  [key: string]: unknown
}

/**
 * 弹幕轨道映射类型
 */
export interface DanmakuChannelMap {
  [key: string]: DanmakuChannelItem
}
