import { ref, computed, watch, onMounted, onUnmounted, readonly } from 'vue'

import logger from '@/utils/log/log'

import type { Ref } from 'vue'

/**
 * 评论动态加载组合式函数
 * 参考文章列表的动态计算逻辑，实现评论列表的动态高度计算和加载量优化
 */
export function useCommentDynamicLoad(
  commentList: Ref<unknown[]>,
  containerRef: Ref<HTMLElement | null>,
) {
  // 评论项的平均高度（像素）- 基于实际测量的评论项高度
  // 包括：用户信息行(~60px) + 评论内容(~100px) + 交互栏(~50px) + 间距和边框(~40px) = ~250px
  const COMMENT_HEIGHT = 250

  // 响应式状态
  const containerHeight = ref(0)
  const commentsPerScreen = ref(5) // 默认一屏显示5条评论
  const isInitialLoad = ref(true)

  // 更新容器高度
  const updateContainerHeight = () => {
    if (containerRef.value) {
      const newHeight = containerRef.value.clientHeight
      const newCommentsPerScreen = Math.ceil(newHeight / COMMENT_HEIGHT)
      const finalCommentsPerScreen = Math.max(3, newCommentsPerScreen) // 最少3条评论

      // 只有当值发生变化时才更新，避免不必要的重新计算
      if (
        containerHeight.value !== newHeight ||
        commentsPerScreen.value !== finalCommentsPerScreen
      ) {
        containerHeight.value = newHeight
        commentsPerScreen.value = finalCommentsPerScreen

        logger.debug('评论容器高度更新:', {
          containerHeight: containerHeight.value,
          commentsPerScreen: commentsPerScreen.value,
          commentHeight: COMMENT_HEIGHT,
          calculatedFromHeight: newCommentsPerScreen,
        })
      }
    }
  }

  // 计算动态加载量
  const calculatedLoadSize = computed(() => {
    let size: number

    if (isInitialLoad.value || commentList.value.length === 0) {
      // 首次加载：计算铺满屏幕所需的评论数量，并增加一些缓冲
      size = Math.ceil(commentsPerScreen.value * 1.2) // 增加20%的缓冲
      // 确保至少加载一屏幕的内容，最少5条评论
      size = Math.max(5, size)
    } else {
      // 后续加载：根据屏幕大小采用不同的加载策略
      if (commentsPerScreen.value <= 4) {
        // 小屏幕时，每次加载较少评论
        size = Math.max(3, Math.ceil(commentsPerScreen.value * 0.8))
      } else if (commentsPerScreen.value <= 8) {
        // 中等屏幕时，每次加载适中数量的评论
        size = Math.max(4, Math.ceil(commentsPerScreen.value * 0.6))
      } else {
        // 大屏幕时，每次加载更多评论
        size = Math.max(6, Math.ceil(commentsPerScreen.value * 0.5))
      }
    }

    logger.debug('计算评论加载量:', {
      size,
      isInitialLoad: isInitialLoad.value,
      commentsPerScreen: commentsPerScreen.value,
      currentCommentsCount: commentList.value.length,
      containerHeight: containerHeight.value,
    })

    return size
  })

  // 标记首次加载完成
  const markInitialLoadComplete = () => {
    if (isInitialLoad.value) {
      logger.debug('评论首次加载完成，切换到后续加载模式')
      isInitialLoad.value = false
    }
  }

  // 重置为初始加载状态
  const resetToInitialLoad = () => {
    logger.debug('重置评论加载状态为初始加载模式')
    isInitialLoad.value = true
  }

  // 监听容器尺寸变化
  const resizeObserver = ref<ResizeObserver | null>(null)

  // 设置容器监听
  const setupContainerObserver = () => {
    if (containerRef.value && window.ResizeObserver) {
      resizeObserver.value = new ResizeObserver(() => {
        updateContainerHeight()
      })
      resizeObserver.value.observe(containerRef.value)
    }
  }

  // 清理容器监听
  const cleanupContainerObserver = () => {
    if (resizeObserver.value) {
      resizeObserver.value.disconnect()
      resizeObserver.value = null
    }
  }

  // 窗口尺寸变化处理
  const handleWindowResize = () => {
    updateContainerHeight()
  }

  // 监听容器引用变化
  watch(
    containerRef,
    (newContainer) => {
      cleanupContainerObserver()
      if (newContainer) {
        updateContainerHeight()
        setupContainerObserver()
      }
    },
    { immediate: true },
  )

  // 组件挂载时设置监听
  onMounted(() => {
    window.addEventListener('resize', handleWindowResize)
    updateContainerHeight()
  })

  // 组件卸载时清理监听
  onUnmounted(() => {
    window.removeEventListener('resize', handleWindowResize)
    cleanupContainerObserver()
  })

  return {
    // 状态
    containerHeight: readonly(containerHeight),
    commentsPerScreen: readonly(commentsPerScreen),
    isInitialLoad: readonly(isInitialLoad),

    // 计算属性
    calculatedLoadSize,

    // 方法
    updateContainerHeight,
    markInitialLoadComplete,
    resetToInitialLoad,

    // 常量
    COMMENT_HEIGHT,
  }
}
