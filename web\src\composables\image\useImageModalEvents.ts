import { addImageZoomWheelListener } from '@/components/tiptap/events/PassiveEventHandlers'

/**
 * 图片模态框事件处理组合式函数
 * 提供模态框的通用事件处理功能
 */

/**
 * 设置模态框通用事件处理
 * @param modal 模态框元素
 * @param closeModal 关闭模态框的函数
 * @param wheelZoomHandler 滚轮缩放处理函数
 * @returns 清理函数
 */
export function setupModalEvents(
  modal: HTMLElement,
  closeModal: () => void,
  wheelZoomHandler: (e: WheelEvent) => void,
): () => void {
  // 添加滚轮缩放监听器
  const removeWheelListener = addImageZoomWheelListener(modal, wheelZoomHandler)

  // 点击模态框背景关闭
  const handleModalClick = (e: MouseEvent): void => {
    if (e.target === modal) {
      closeModal()
    }
  }
  modal.addEventListener('click', handleModalClick)

  // ESC键关闭
  const handleEscKey = (e: KeyboardEvent): void => {
    if (e.key === 'Escape') {
      closeModal()
    }
  }
  document.addEventListener('keydown', handleEscKey)

  // 返回清理函数
  return (): void => {
    removeWheelListener()
    modal.removeEventListener('click', handleModalClick)
    document.removeEventListener('keydown', handleEscKey)
  }
}
