/*
 * ToggleButton 组件样式
 * 切换按钮组件的样式定义，包括3D翻转效果和悬停动画
 */

.toggle-button-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 0 0.5rem;
  perspective: 1000px;
  width: 3rem;
  height: 3rem;

  .toggle-card {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
    transform-style: preserve-3d;
    cursor: pointer;

    .toggle-card-front,
    .toggle-card-back {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%; /* Safari */
      backface-visibility: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .toggle-card-front {
      z-index: 2;
    }

    .toggle-card-back {
      transform: rotateY(180deg);
    }

    &.is-flipping {
      transform: rotateY(180deg) scale(1.2);
    }

    &:hover {
      transform: scale(1.05);
    }

    &.is-flipping:hover {
      transform: rotateY(180deg) scale(1.2);
    }
  }
}
