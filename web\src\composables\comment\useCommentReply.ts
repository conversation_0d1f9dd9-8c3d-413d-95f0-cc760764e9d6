/**
 * 评论回复相关的组合式函数
 * 处理快捷回复、发送评论等操作
 */
import { ref, nextTick } from 'vue'

import commentApi from '@/api/comment'
import { COMMENT_QUICK_REPLY, COMMENT_SEND } from '@/constants/comment/frequency-key.constants'
import type { Comment } from '@/types/comment/comment.types'
import type { EditorInstance } from '@/types/editor/editor-instance.types'
import { type EditorContent } from '@/types/editor/editor-validation.types'
import {
  validateEditorContent,
  checkLoadingState,
  setLoadingState,
} from '@/utils/editor/editor-validation'
import frequencyLimit from '@/utils/performance/frequency-limit'
import tiptap from '@/utils/tiptap/tiptap'
import message from '@/utils/ui/message'

import type { Editor } from '@tiptap/vue-3'
import type { JSONContent } from '@tiptap/vue-3'

// 定义获取文章ID的函数类型
type GetArticleIdFunction = () => string

// 定义评论输入引用类型
interface CommentMainInputRef {
  sendTiptapEditorRef?: Editor & {
    clearContent: () => void
  }
}

export function useCommentReply(getArticleId: GetArticleIdFunction) {
  // 评论回复状态
  const quickReplyLoading = ref<Map<string, boolean>>(new Map())
  const sendCommentLoading = ref(false)
  const commentInputVisible = ref('-1')
  const commentReply = ref<JSONContent | undefined>(undefined)
  const quickReplyTiptapEditorMap = ref<Map<string, EditorInstance>>(new Map())

  // 更新编辑器引用
  const updateEditor = (commentId: string, editor: EditorInstance) => {
    quickReplyTiptapEditorMap.value.set(commentId, editor)
  }

  // 清空所有快捷回复框内容
  const clearAllQuickReplyContent = () => {
    // 关闭所有回复框
    commentInputVisible.value = '-1'

    return ({ commentList }: { commentList: Comment[] }) => {
      // 清空所有评论的快捷回复内容
      commentList.forEach((comment) => {
        if (comment.quickCommentReply) {
          comment.quickCommentReply = undefined
        }
        const editor = quickReplyTiptapEditorMap.value.get(comment.id)
        if (editor && (editor as EditorInstance).commands) {
          ;(editor as EditorInstance).commands.clearContent()
        }
      })
    }
  }

  // 处理点击回复按钮
  const handleCommentReplyClick = (
    comment: Comment,
    { isLastBreadcrumb }: { isLastBreadcrumb: boolean },
  ) => {
    if (isLastBreadcrumb) {
      // 如果当前评论输入框已经显示，则隐藏
      if (commentInputVisible.value === comment.id) {
        commentInputVisible.value = '-1'
      } else {
        // 显示当前评论的输入框
        commentInputVisible.value = comment.id
        // 设置回复内容
        commentReply.value = comment.quickCommentReply
        // 聚焦到输入框
        nextTick(() => {
          const editor = quickReplyTiptapEditorMap.value.get(comment.id) as
            | EditorInstance
            | undefined
          if (editor?.commands && typeof editor.commands.focus === 'function') {
            editor.commands.focus()
          }
        })
      }
    }
  }

  // 快捷回复评论（防抖）
  const debouncedQuickReplyComment = (
    comment: Comment,
    options: { isLastBreadcrumb: boolean; onSuccess?: (commentId: string) => void },
  ) => {
    const { isLastBreadcrumb, onSuccess } = options

    if (!isLastBreadcrumb) {
      return
    }

    // 频率限制检查 - 使用节流控制
    let canProceed = true
    frequencyLimit.throttle(
      COMMENT_QUICK_REPLY,
      () => {
        canProceed = false
      },
      1000,
    )

    if (!canProceed) {
      return
    }

    // 检查是否正在加载中
    if (
      !checkLoadingState(
        quickReplyLoading.value.get(comment.id) ? { value: true } : { value: false },
      )
    ) {
      return
    }

    // 验证编辑器内容
    const editor = quickReplyTiptapEditorMap.value.get(comment.id) as EditorInstance
    const contentRef = { value: comment.quickCommentReply as EditorContent }
    const validationResult = validateEditorContent(editor as any, contentRef, '回复内容不能为空哦~')

    if (!validationResult.isValid) {
      return
    }

    // 设置loading状态
    quickReplyLoading.value.set(comment.id, true)
    const loadingRef = { value: true }
    setLoadingState(loadingRef, true)

    // 获取最新内容
    const latestContent = editor && typeof editor.getJSON === 'function' ? editor.getJSON() : {}

    // 构建请求参数
    const params = {
      articleId: getArticleId(),
      parentCommentId: comment.id,
      content: tiptap.toJsonString(latestContent as JSONContent),
    }

    // 发送请求
    commentApi
      .save(params)
      .then((res) => {
        if (res.success) {
          message.success('回复成功')

          // 清空编辑器内容
          editor.commands.clearContent()
          comment.quickCommentReply = undefined

          // 隐藏输入框
          commentInputVisible.value = '-1'

          // 调用成功回调
          if (onSuccess) {
            onSuccess(comment.id)
          }
        }
      })
      .catch((error) => {
        console.error('快捷回复失败:', error)
        message.error('回复失败，请重试')
      })
      .finally(() => {
        // 重置loading状态
        quickReplyLoading.value.set(comment.id, false)
        setLoadingState(loadingRef, false)
      })
  }

  // 发送评论（防抖）
  const debouncedSendComment = (
    commentMainInputRef: CommentMainInputRef,
    options: { lastBreadcrumbComment: Comment; onSuccess?: (commentId: string) => void },
  ) => {
    const { lastBreadcrumbComment, onSuccess } = options

    // 频率限制检查 - 使用节流控制
    let canProceed = true
    frequencyLimit.throttle(
      COMMENT_SEND,
      () => {
        canProceed = false
      },
      1000,
    )

    if (!canProceed) {
      return
    }

    // 检查是否正在加载中
    if (!checkLoadingState(sendCommentLoading)) {
      return
    }

    // 验证编辑器内容
    const editor = commentMainInputRef.sendTiptapEditorRef
    const contentRef = { value: commentReply.value as EditorContent }
    const validationResult = validateEditorContent(editor, contentRef, '评论内容不能为空哦~')

    if (!validationResult.isValid) {
      return
    }

    // 设置loading状态
    setLoadingState(sendCommentLoading, true)

    // 获取最新内容
    const latestContent = editor!.getJSON()

    // 构建请求参数
    const params = {
      articleId: getArticleId(),
      parentCommentId: lastBreadcrumbComment.id,
      content: tiptap.toJsonString(latestContent),
    }

    // 发送请求
    commentApi
      .save(params)
      .then((res) => {
        if (res.success) {
          message.success('评论成功')

          // 清空编辑器内容
          if (commentMainInputRef.sendTiptapEditorRef) {
            commentMainInputRef.sendTiptapEditorRef.clearContent()
          }
          commentReply.value = undefined

          // 调用成功回调
          if (onSuccess) {
            onSuccess(lastBreadcrumbComment.id)
          }
        }
      })
      .catch((error) => {
        console.error('发送评论失败:', error)
        message.error('评论失败，请重试')
      })
      .finally(() => {
        // 重置loading状态
        setLoadingState(sendCommentLoading, false)
      })
  }

  return {
    // 状态
    quickReplyLoading,
    sendCommentLoading,
    commentInputVisible,
    commentReply,
    quickReplyTiptapEditorMap,

    // 方法
    updateEditor,
    clearAllQuickReplyContent,
    handleCommentReplyClick,
    debouncedQuickReplyComment,
    debouncedSendComment,
  }
}
