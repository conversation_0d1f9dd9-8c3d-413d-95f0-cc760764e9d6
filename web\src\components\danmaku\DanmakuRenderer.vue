<!-- 
  DanmakuRenderer.vue - 组件将TipTap的JSON内容转换为HTML并保留富文本格式
  同时确保所有内容显示在一行，不依赖TipTap编辑器渲染
  
  重构说明：
  1. 将复杂的渲染逻辑提取到独立的工具函数
  2. 将样式提取到单独的样式文件
  3. 组织更加清晰的代码结构
-->
<template>
  <div class="danmaku-renderer" ref="rendererRef" v-html="renderContent"></div>
</template>

<script lang="ts" setup>
import { computed, ref, toRefs, onMounted, onUnmounted } from 'vue'

import type { ExtendedHTMLElement } from '@/types/dom/extended-html-element.types'
import { openDanmuImageViewer } from '@/utils/danmaku/media/imageViewer'

import { renderDanmuContent } from '../danmaku-renderer/content/richTextRenderer'

import type { JSONContent } from '@tiptap/vue-3'
import type { PropType } from 'vue'

// 定义组件的属性
const props = defineProps({
  content: {
    type: Object as PropType<JSONContent>,
    required: true,
  },
})

// 添加emit用于组件通信
const emit = defineEmits<{
  // 图片预览开始事件，请求父组件暂停弹幕
  (e: 'image-preview-open'): void
  // 图片预览结束事件，请求父组件恢复弹幕
  (e: 'image-preview-close'): void
}>()

const { content } = toRefs(props)

// 创建一个模板引用来获取组件的DOM元素
const rendererRef = ref<ExtendedHTMLElement | null>(null)

// 将富文本内容转换为HTML
const renderContent = computed(() => {
  return renderDanmuContent(content.value)
})

// 监听图片点击事件
onMounted(() => {
  // 使用一个具名函数而不是匿名函数，以便能正确地移除事件监听
  const handleClick = (e: Event) => {
    const target = e.target as HTMLElement

    // 检查是否是图片元素且具有正确的类名
    if (target.tagName === 'IMG' && target.classList.contains('danmaku-image')) {
      const img = target as HTMLImageElement
      const originalSrc = img.dataset.originalSrc
      if (originalSrc) {
        // 阻止事件冒泡和默认行为
        e.stopPropagation()
        e.preventDefault()
        openDanmuImageViewer(
          img,
          originalSrc,
          emit as (e: 'image-preview-open' | 'image-preview-close') => void,
        )
      }
    }
  }

  // 将事件监听器直接添加到组件的根元素上
  if (rendererRef.value) {
    rendererRef.value.addEventListener('click', handleClick)
    // 保存事件处理器引用，以便后续可以移除
    rendererRef.value._danmakuImageClickHandler = handleClick
  }
})

// 清理事件监听
onUnmounted(() => {
  // 正确地移除事件监听器
  if (rendererRef.value && rendererRef.value._danmakuImageClickHandler) {
    rendererRef.value.removeEventListener('click', rendererRef.value._danmakuImageClickHandler)
    delete rendererRef.value._danmakuImageClickHandler
  }
})
</script>

<style lang="scss">
@use '@/styles/danmaku-renderer';
</style>
