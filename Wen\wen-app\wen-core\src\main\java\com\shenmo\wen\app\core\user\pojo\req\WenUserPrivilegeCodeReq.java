package com.shenmo.wen.app.core.user.pojo.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 用户特权激活码生成参数
 * 
 * <AUTHOR>
 */
@Data
public class WenUserPrivilegeCodeReq {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 特权模板ID（纯付费特权时为NULL）
     */
    private Long templateId;

    /**
     * 付费面额（纯付费特权时必须>0）
     */
    private Integer paidDenomination;

    /**
     * 过期时间（时间戳）
     */
    @NotNull(message = "过期时间不能为空")
    private Long expireTime;

    /**
     * 验证类型：0-短信验证，1-二维码验证
     */
    private Integer verificationType;
}
