<template>
  <NModal
    v-model:show="modal.visible"
    :title="modal.title"
    preset="dialog"
    positive-text="确认"
    negative-text="取消"
    @positive-click="handlePositiveClick"
    @negative-click="handleNegativeClick"
    :auto-focus="false"
  >
    <div class="flex-column-gap12">
      <NInput
        v-if="!modal.onlyInputValue"
        v-model:value="modal.inputTitle"
        placeholder="请输入标题"
      />
      <NInput v-model:value="modal.inputValue" placeholder="请输入链接" />
    </div>
  </NModal>
</template>

<script setup lang="ts">
import { NModal, NInput } from 'naive-ui'

import type { Editor } from '@tiptap/vue-3'

interface ModalState {
  visible: boolean
  title: string
  inputValue: string
  inputTitle: string
  onlyInputValue: boolean
  trigger: () => void
}

interface Props {
  modal: ModalState
  editor?: Editor
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modal': [modal: ModalState]
}>()

// 处理模态框确认按钮点击
const handlePositiveClick = () => {
  // 检查是否是链接相关的模态框
  if (props.editor) {
    if (props.modal.title === '插入链接') {
      // 如果是插入链接
      const selection = props.editor.state.selection
      const hasSelection = !selection.empty

      if (props.modal.inputValue) {
        if (hasSelection) {
          // 如果有选中文本，将选中文本设置为链接
          props.editor.chain().focus().setLink({ href: props.modal.inputValue }).run()
        } else if (props.modal.inputTitle) {
          // 如果没有选中文本但有输入标题，使用inputTitle和inputValue创建链接
          props.editor
            .chain()
            .focus()
            .insertContent(`<a href="${props.modal.inputValue}">${props.modal.inputTitle}</a>`)
            .run()
        } else {
          // 如果既没有选中文本也没有输入标题，使用URL作为显示文本
          props.editor
            .chain()
            .focus()
            .insertContent(`<a href="${props.modal.inputValue}">${props.modal.inputValue}</a>`)
            .run()
        }
      }
    } else if (props.modal.title === '设置链接') {
      // 如果是设置链接（从气泡菜单触发）
      if (props.modal.inputValue) {
        // 设置选中文本的链接
        props.editor
          .chain()
          .focus()
          .extendMarkRange('link')
          .setLink({ href: props.modal.inputValue })
          .run()
      }
    } else if (props.modal.title === '插入bilibili视频链接') {
      // 如果是插入B站视频
      if (props.modal.inputValue) {
        // @ts-expect-error - setBilibiliVideo is a custom command that may not be typed
        props.editor.commands.setBilibiliVideo({
          src: props.modal.inputValue,
        })
      }
    } else {
      // 其他类型的模态框，执行原来的trigger函数
      props.modal.trigger()
    }
  } else {
    // 如果编辑器不存在，执行原来的trigger函数
    props.modal.trigger()
  }

  // 关闭模态框
  const updatedModal = { ...props.modal, visible: false }
  emit('update:modal', updatedModal)
}

// 处理模态框取消按钮点击
const handleNegativeClick = () => {
  const updatedModal = { ...props.modal, visible: false }
  emit('update:modal', updatedModal)
}
</script>

<style lang="scss" scoped>
@use '@/styles/tiptap/tiptap-editor';
</style>
