import axios from 'axios'

import config from '@/config'
import { REQUEST } from '@/constants/api/frequency-key.constants'
import router from '@/router'
import frequencyLimit from '@/utils/performance/frequency-limit'
import localStorage from '@/utils/storage/local-storage'
import message from '@/utils/ui/message'

import type { AxiosInstance, AxiosResponse } from 'axios'
const TIMEOUT = 10000 // 超时时间
// 创建 Axios 实例
const service: AxiosInstance = axios.create({
  baseURL: config.backend.baseURL,
  timeout: TIMEOUT,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
service.interceptors.request.use()
// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    // 正常响应直接返回
    return response
  },
  (error) => {
    const { response } = error
    if (response) {
      const status = response.status
      // 如果是 401 状态码，表示未认证或会话过期
      if (status === 401) {
        localStorage.removeLoginUser()
        frequencyLimit.debounce(REQUEST + 401, () => {
          message.warning('登录已过期，请重新登录')
        })
        router.push('/login')
        deleteAllCookies()
        return Promise.reject(null)
      } else if (status === 403) {
        message.warning(response.data.message)
        return Promise.reject(null)
      }
    } else {
      // 如果没有响应，则是网络错误等，提示用户
      frequencyLimit.debounce(REQUEST + 'net', () => {
        message.warning('网络错误，请稍后重试')
      })
      return Promise.reject(null)
    }
    return Promise.reject(error)
  },
)
function deleteAllCookies() {
  // 获取所有的 cookie
  const cookies = document.cookie.split(';')
  // 遍历所有 cookies，并删除它们
  cookies.forEach(function (cookie) {
    const cookieName = cookie.split('=')[0].trim()
    // 删除 cookie，通过设置过期时间为过去的时间
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`
  })
}
export default service
