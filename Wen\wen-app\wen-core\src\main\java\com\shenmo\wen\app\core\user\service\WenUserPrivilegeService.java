package com.shenmo.wen.app.core.user.service;

import java.util.List;

import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeActivateReq;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeCodeReq;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeSearchReq;
import com.shenmo.wen.app.core.user.pojo.resp.WenUserPrivilegeResp;

public interface WenUserPrivilegeService {

    String code(WenUserPrivilegeCodeReq req);

    Boolean activate(WenUserPrivilegeActivateReq req);

    List<WenUserPrivilegeResp> search(WenUserPrivilegeSearchReq req);
}
