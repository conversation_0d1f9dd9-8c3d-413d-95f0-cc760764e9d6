package com.shenmo.wen.app.core.article.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shenmo.wen.common.mybatis.handler.TimestampToLongTypeHandler;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "wen_article_share", autoResultMap = true)
public class WenArticleShare {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 文章ID
     */
    private Long articleId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 创建时间
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long ctTm;
}