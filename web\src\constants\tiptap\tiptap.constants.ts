/**
 * TipTap 编辑器相关常量
 */

/**
 * TipTap 编辑器扩展枚举
 */
export enum TipTapExtension {
  IMAGE = 'image',
  DROP_CURSOR = 'dropcursor',
  PLACEHOLDER = 'placeholder',
  LINK = 'link',
  TEXT_STYLE = 'textStyle',
  COLOR = 'color',
  BACKGROUND_COLOR = 'backgroundColor',
  MENTION = 'mention',
  BILIBILI = 'bilibili',
  ALIGN = 'align',
  HISTORY = 'history',
}

/** 评论编辑器扩展列表 - 定义评论编辑器支持的功能扩展 */
export const COMMENT_EXTENSIONS: readonly TipTapExtension[] = [
  TipTapExtension.IMAGE,
  TipTapExtension.DROP_CURSOR,
  TipTapExtension.PLACEHOLDER,
  TipTapExtension.LINK,
  TipTapExtension.TEXT_STYLE,
  TipTapExtension.COLOR,
  TipTapExtension.BACKGROUND_COLOR,
  TipTapExtension.MENTION,
  TipTapExtension.BILIBILI,
  TipTapExtension.ALIGN,
  TipTapExtension.HISTORY,
] as const

/**
 * 字符限制枚举
 */
export enum CharacterLimit {
  /** 默认字符限制数量 - 通用编辑器的默认字符数限制 */
  DEFAULT = 1000,
  /** 文章字符限制数量 - 文章编辑器的字符数限制 */
  ARTICLE = 10000,
}

/** 默认字符限制数量 - 通用编辑器的默认字符数限制 */
export const DEFAULT_CHARACTER_LIMIT: number = CharacterLimit.DEFAULT

/** 文章字符限制数量 - 文章编辑器的字符数限制 */
export const ARTICLE_CHARACTER_LIMIT: number = CharacterLimit.ARTICLE
