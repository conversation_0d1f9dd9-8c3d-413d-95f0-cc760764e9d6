package com.shenmo.wen.app.core.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * 特权验证配置属性
 * 
 * <AUTHOR>
 */
@Data
@RefreshScope
@ConfigurationProperties(PrivilegeVerificationProperties.PREFIX)
public class PrivilegeVerificationProperties {

    public static final String PREFIX = "privilege.verification";
    /**
     * 每日申请次数限制
     */
    private Integer dailyLimit = 1;

    /**
     * 验证流程过期时间（分钟）
     */
    private Integer expireMinutes = 5;

    /**
     * 每步骤超时时间（分钟）
     */
    private Integer stepTimeoutMinutes = 3;

    /**
     * 验证页面URL路径前缀
     */
    private String verificationUrlPrefix = "/privilege/verification/";
}
