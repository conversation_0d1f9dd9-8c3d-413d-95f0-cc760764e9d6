<template>
  <div class="tag-bar-container" v-if="hotTags.length > 0">
    <NTag
      v-for="tag in hotTags"
      :key="tag.name"
      :type="selectedTag === tag.name ? 'primary' : 'default'"
      class="hot-tag"
      :bordered="false"
      @click="handleTagClick(tag.name)"
    >
      {{ tag.name }} ({{ tag.count }})
    </NTag>
  </div>
</template>

<script lang="ts" setup>
import { NTag } from 'naive-ui'
import { ref, onMounted, watch, computed } from 'vue'

import articleApi from '@/api/article'
import type { HotTag } from '@/types/search/hot-tag.types'
import logger from '@/utils/log/log' // 引入日志工具

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue', 'tagSelected'])

// 热门标签
const hotTags = ref<HotTag[]>([])
const selectedTag = ref(props.modelValue)

// 增加状态追踪
const tagSelectionHistory = ref<
  {
    timestamp: number
    from: string
    to: string
  }[]
>([])

// 计算属性：当前选中标签的详细信息
const currentTagInfo = computed(() => {
  return hotTags.value.find((tag) => tag.name === selectedTag.value)
})

// 监听外部值变化
watch(
  () => props.modelValue,
  (newVal) => {
    // 增加日志追踪
    logger.debug('外部标签值变化:', {
      oldValue: selectedTag.value,
      newValue: newVal,
      hotTags: hotTags.value,
    })
    selectedTag.value = newVal
  },
)

// 加载热门标签，增加错误处理
const loadHotTags = async () => {
  try {
    const res = await articleApi.getHotTags(10)
    if (res.data) {
      hotTags.value = res.data
      logger.debug('热门标签加载成功:', hotTags.value)
    }
  } catch (error) {
    logger.error('加载热门标签失败:', error as Error)
  }
}

// 处理标签点击，增加状态管理和日志
const handleTagClick = (tagName: string) => {
  const prevTag = selectedTag.value

  // 如果点击当前已选中的标签，则取消选择
  if (selectedTag.value === tagName) {
    selectedTag.value = ''
  } else {
    selectedTag.value = tagName
  }

  // 记录标签选择历史
  tagSelectionHistory.value.push({
    timestamp: Date.now(),
    from: prevTag,
    to: selectedTag.value,
  })

  // 限制历史记录长度
  if (tagSelectionHistory.value.length > 10) {
    tagSelectionHistory.value.shift()
  }

  // 日志追踪
  logger.debug('标签选择变化:', {
    prevTag,
    currentTag: selectedTag.value,
    hotTags: hotTags.value,
  })

  // 更新外部值并触发事件
  emit('update:modelValue', selectedTag.value)
  emit('tagSelected', selectedTag.value)
}

// 组件挂载时加载热门标签
onMounted(() => {
  loadHotTags()
})

// 暴露内部方法，方便调试和测试
defineExpose({
  tagSelectionHistory,
  currentTagInfo,
  hotTags,
})
</script>

<style lang="scss" scoped>
@use '@/styles/ui-elements/long-press';
</style>
