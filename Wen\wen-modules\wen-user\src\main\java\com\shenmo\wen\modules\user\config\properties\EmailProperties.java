package com.shenmo.wen.modules.user.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 邮件配置属性
 * 
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = EmailProperties.PREFIX)
public class EmailProperties {

    public static final String PREFIX = "email";

    /**
     * 是否启用邮件验证
     */
    private Boolean enabled = true;

    /**
     * 发送方邮箱地址
     */
    private String from;

    /**
     * 发送方名称
     */
    private String personal;

    /**
     * 邮件主题前缀
     */
    private String subjectPrefix;

    /**
     * 验证码有效期（分钟）
     */
    private Integer codeExpireMinutes = 5;

    /**
     * 验证码长度
     */
    private Integer codeLength = 6;

    /**
     * 网站域名
     */
    private String websiteDomain;
}
