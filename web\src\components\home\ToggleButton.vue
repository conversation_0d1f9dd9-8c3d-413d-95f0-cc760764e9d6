<template>
  <div class="toggle-button-container">
    <div
      class="toggle-card"
      :class="{ 'is-flipping': isFlipping }"
      @mouseenter="handleToggleButtonMouseEnter"
      @mouseleave="handleToggleButtonMouseLeave"
      @click="toggle"
      ref="toggleButtonRef"
    >
      <div class="toggle-card-front">
        <NGradientText type="info" class="cursor-pointer" :size="32">
          {{ buttonText }}
        </NGradientText>
      </div>
      <div class="toggle-card-back">
        <NGradientText type="info" class="cursor-pointer" :size="32">
          {{ alternateButtonText }}
        </NGradientText>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { NGradientText } from 'naive-ui'
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  value: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits(['update:value', 'toggle'])

// 翻转状态
const isFlipping = ref(false)
const flipTimer = ref<number | null>(null)
const autoFlipTimer = ref<number | null>(null)

// 按钮文本
const buttonText = computed(() => {
  return props.value ? '评' : '文'
})

const alternateButtonText = computed(() => {
  return props.value ? '文' : '评'
})

// 切换按钮状态
const toggle = () => {
  emit('update:value', !props.value)
  emit('toggle')

  // 无论当前状态如何，点击时总是触发翻转效果
  // 先清除任何现有的翻转计时器
  if (flipTimer.value) {
    clearTimeout(flipTimer.value)
    flipTimer.value = null
  }

  // 重新设置翻转状态
  isFlipping.value = true

  // 设置一个计时器，在翻转动画完成后重置状态
  flipTimer.value = window.setTimeout(() => {
    isFlipping.value = false
    // 重新开始自动翻转计时
    scheduleNextAutoFlip()
  }, 800) // 动画持续时间
}

// 处理鼠标悬浮，触发翻转
const handleToggleButtonMouseEnter = () => {
  // 如果已经在翻转中，不需要再次触发
  if (isFlipping.value) return

  // 清除任何即将到来的自动翻转
  if (autoFlipTimer.value) {
    clearTimeout(autoFlipTimer.value)
    autoFlipTimer.value = null
  }

  // 设置翻转状态
  isFlipping.value = true

  // 设置一个计时器，在翻转动画完成后重置状态
  flipTimer.value = window.setTimeout(() => {
    isFlipping.value = false
    // 重新开始自动翻转计时
    scheduleNextAutoFlip()
  }, 800) // 动画持续时间
}

// 处理鼠标离开
const handleToggleButtonMouseLeave = () => {
  // 不立即停止翻转，让当前翻转完成
  // 只有在没有正在进行的翻转时才计划下一次自动翻转
  if (!isFlipping.value) {
    scheduleNextAutoFlip()
  }
}

// 触发自动翻转
const triggerAutoFlip = () => {
  // 如果已经在翻转或者用户鼠标悬浮中，不执行自动翻转
  if (isFlipping.value) {
    scheduleNextAutoFlip()
    return
  }

  // 设置翻转状态
  isFlipping.value = true

  // 翻转完成后重置状态
  flipTimer.value = window.setTimeout(() => {
    isFlipping.value = false
    // 计划下一次自动翻转
    scheduleNextAutoFlip()
  }, 800) // 动画持续时间
}

// 计划下一次自动翻转
const scheduleNextAutoFlip = () => {
  // 清除任何现有的自动翻转计时器
  if (autoFlipTimer.value) {
    clearTimeout(autoFlipTimer.value)
    autoFlipTimer.value = null
  }

  // 设置一个随机时间（8-20秒）后触发自动翻转
  const nextFlipTime = 8000 + Math.random() * 12000
  autoFlipTimer.value = window.setTimeout(() => {
    triggerAutoFlip()
  }, nextFlipTime)
}

onMounted(() => {
  // 开始自动翻转计时
  scheduleNextAutoFlip()
})

onUnmounted(() => {
  // 清除翻转计时器
  if (flipTimer.value) {
    clearTimeout(flipTimer.value)
    flipTimer.value = null
  }

  if (autoFlipTimer.value) {
    clearTimeout(autoFlipTimer.value)
    autoFlipTimer.value = null
  }
})
</script>

<style lang="scss" scoped>
@use '@/styles/ui-elements/long-press';
</style>
