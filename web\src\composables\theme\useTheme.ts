import { ref, computed, watch, type Ref, type ComputedRef } from 'vue'

import { activeTheme, ThemeType, toggleTheme as themeToggle } from '@/utils/theme/theme'

/**
 * 主题配置类型
 */
interface ThemeConfig {
  /** 主题名称 */
  name: string
  /** 主题显示名称 */
  displayName: string
  /** 主题描述 */
  description?: string
  /** 主题颜色 */
  primaryColor?: string
  /** 是否为深色主题 */
  isDark: boolean
}

/**
 * 主题组合式函数返回值类型
 */
interface UseThemeReturn {
  /** 当前主题 */
  currentTheme: Ref<ThemeType>
  /** 是否为深色主题 */
  isDarkTheme: ComputedRef<boolean>
  /** 是否为浅色主题 */
  isLightTheme: ComputedRef<boolean>
  /** 可用主题列表 */
  availableThemes: ComputedRef<ThemeConfig[]>
  /** 当前主题配置 */
  currentThemeConfig: ComputedRef<ThemeConfig | undefined>
  /** 切换主题 */
  toggleTheme: () => void
  /** 设置指定主题 */
  setTheme: (theme: ThemeType) => void
  /** 设置深色主题 */
  setDarkTheme: () => void
  /** 设置浅色主题 */
  setLightTheme: () => void
  /** 根据系统偏好设置主题 */
  setSystemTheme: () => void
  /** 监听系统主题变化 */
  watchSystemTheme: () => () => void
}

/**
 * 主题管理组合式函数
 * 提供主题切换、主题状态管理等功能
 */
export function useTheme(): UseThemeReturn {
  // 当前主题
  const currentTheme = ref<ThemeType>(activeTheme.value)

  // 可用主题配置
  const themeConfigs: ThemeConfig[] = [
    {
      name: ThemeType.LIGHT,
      displayName: '浅色主题',
      description: '明亮清新的浅色界面',
      primaryColor: '#1890ff',
      isDark: false,
    },
    {
      name: ThemeType.DARK,
      displayName: '深色主题',
      description: '护眼舒适的深色界面',
      primaryColor: '#177ddc',
      isDark: true,
    },
  ]

  // 是否为深色主题
  const isDarkTheme = computed(() => currentTheme.value === ThemeType.DARK)

  // 是否为浅色主题
  const isLightTheme = computed(() => currentTheme.value === ThemeType.LIGHT)

  // 可用主题列表
  const availableThemes = computed(() => themeConfigs)

  // 当前主题配置
  const currentThemeConfig = computed(() => {
    return themeConfigs.find((config) => config.name === currentTheme.value)
  })

  /**
   * 切换主题（在深色和浅色之间切换）
   */
  const toggleTheme = (): void => {
    const newTheme = isDarkTheme.value ? ThemeType.LIGHT : ThemeType.DARK
    setThemeInternal(newTheme)
  }

  /**
   * 设置指定主题
   * @param theme 主题类型
   */
  const setThemeInternal = (theme: ThemeType): void => {
    currentTheme.value = theme
    // 使用theme模块的toggleTheme来切换主题
    if (theme !== activeTheme.value) {
      themeToggle()
    }
  }

  /**
   * 设置深色主题
   */
  const setDarkTheme = (): void => {
    setThemeInternal(ThemeType.DARK)
  }

  /**
   * 设置浅色主题
   */
  const setLightTheme = (): void => {
    setThemeInternal(ThemeType.LIGHT)
  }

  /**
   * 根据系统偏好设置主题
   */
  const setSystemTheme = (): void => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      setThemeInternal(prefersDark ? ThemeType.DARK : ThemeType.LIGHT)
    }
  }

  /**
   * 监听系统主题变化
   * @returns 取消监听的函数
   */
  const watchSystemTheme = (): (() => void) => {
    if (typeof window === 'undefined' || !window.matchMedia) {
      return () => {}
    }

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleChange = (e: MediaQueryListEvent): void => {
      setThemeInternal(e.matches ? ThemeType.DARK : ThemeType.LIGHT)
    }

    mediaQuery.addEventListener('change', handleChange)

    // 返回清理函数
    return (): void => {
      mediaQuery.removeEventListener('change', handleChange)
    }
  }

  // 监听主题变化，同步更新当前主题状态
  watch(
    activeTheme,
    (newTheme) => {
      currentTheme.value = newTheme
    },
    { immediate: true },
  )

  return {
    currentTheme,
    isDarkTheme,
    isLightTheme,
    availableThemes,
    currentThemeConfig,
    toggleTheme,
    setTheme: setThemeInternal,
    setDarkTheme,
    setLightTheme,
    setSystemTheme,
    watchSystemTheme,
  }
}
