/**
 * 萤火虫样式生成逻辑
 * 抽取自BackgroundAnimation组件
 */
export function useFireflyStyles() {
  // 萤火虫样式生成
  const getFireflyStyle = (index: number) => {
    // 基础属性
    const size = Math.floor(Math.random() * 4) + 3 // 3-7px，增大基础尺寸

    // 随机初始位置
    const left = Math.floor(Math.random() * 100)
    const top = Math.floor(Math.random() * 70) + 10

    // 移动和闪烁属性
    const appearDuration = Math.floor(Math.random() * 3) + 1 // 出现和消失的持续时间1-4秒
    const moveDuration = Math.floor(Math.random() * 5) + 3 // 移动持续时间3-8秒
    const pauseDuration = Math.floor(Math.random() * 4) + 2 // 停留时间2-6秒
    const totalDuration = appearDuration * 2 + moveDuration + pauseDuration // 完整循环的总时长

    // 循环间隔 - 消失后等待一段时间再出现
    const cycleDelay = Math.floor(Math.random() * 8) + 3 // 3-11秒的随机延迟

    // 初始动画延迟，使萤火虫不会同时出现
    const initialDelay = Math.floor(Math.random() * 10)

    // 移动距离和方向
    const moveX = (Math.random() * 30 + 10) * (Math.random() > 0.5 ? 1 : -1) // 10-40px，随机正负
    const moveY = (Math.random() * 30 + 10) * (Math.random() > 0.5 ? 1 : -1) // 10-40px, 随机正负

    // 萤火虫发光颜色
    const colors = ['#80ff72', '#c4ff0e', '#e8ff75', '#00ffaa']
    const colorIndex = index % colors.length
    const color = colors[colorIndex]

    // 闪烁频率和强度
    const pulseFrequency = Math.floor(Math.random() * 2) + 1 // 1-3秒一次完整闪烁
    const glowSize = Math.floor(Math.random() * 6) + 7 // 7-13px的光晕大小，增加光晕范围

    return {
      width: `${size}px`,
      height: `${size}px`,
      left: `${left}%`,
      top: `${top}%`,
      backgroundColor: color,
      // 自定义属性
      '--glow-color': color,
      '--glow-size': `${glowSize}px`,
      '--pulse-duration': `${pulseFrequency}s`,
      // 移动属性
      '--move-x': `${moveX}px`,
      '--move-y': `${moveY}px`,
      // 时间属性
      '--appear-duration': `${appearDuration}s`,
      '--move-duration': `${moveDuration}s`,
      '--pause-duration': `${pauseDuration}s`,
      '--total-duration': `${totalDuration + cycleDelay}s`,
      '--cycle-delay': `${cycleDelay}s`,
      // 初始延迟，让萤火虫错开出现
      animationDelay: `${initialDelay}s`,
    }
  }

  return {
    getFireflyStyle,
  }
}
