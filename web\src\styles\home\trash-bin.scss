/*
 * TrashBin 组件样式
 * 垃圾篓组件的样式定义，包括固定定位、激活状态和进入离开动画
 */

.trash-bin {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 8rem;
  height: 8rem;
  background-color: rgba(255, 255, 255, 95%);
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 15%);
  transition: all 0.3s ease;
  z-index: 9998;

  &.trash-bin-active {
    background-color: rgba(255, 68, 68, 10%);
    transform: translateX(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 68, 68, 30%);
  }

  .trash-bin-text {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #666;
    font-weight: 500;
  }

  &.trash-bin-active .trash-bin-text {
    color: #f44;
  }
}

/* 垃圾篓进入/离开动画 */
.trash-bin-fade-enter-active,
.trash-bin-fade-leave-active {
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.trash-bin-fade-enter-from {
  opacity: 0;
  transform: translateX(-50%) translateY(2rem);
}

.trash-bin-fade-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(2rem);
}
