/**
 * 登录用户存储类型定义
 * 用于本地存储，包含索引签名以支持JSON序列化
 */
import type { UserConfig } from './user-config.types'

/**
 * 登录用户存储类型定义
 * 用于本地存储，包含索引签名以支持JSON序列化
 */
export interface LoginUserStorage {
  /** 用户唯一标识符 */
  id: string
  /** 用户名 */
  username: string
  /** 手机号码 */
  phone: string
  /** 邮箱地址 */
  email: string
  /** 用户头像URL */
  avatar: string
  /** IP地理位置 */
  ipLocation: string
  /** 职业信息 */
  job: string
  /** 用户等级 */
  level: number
  /** 通知接收类型 */
  notificationReceiveType: number
  /** 用户配置信息 */
  config: UserConfig
  /** 其他属性，用于JSON序列化兼容性 */
  [key: string]: string | number | boolean | UserConfig
}
