package com.shenmo.wen.common.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * <AUTHOR>
 */
@Data
@RefreshScope
@ConfigurationProperties(WenServerProperties.PREFIX)
public class WenServerProperties {
    public static final String PREFIX = "server";
    private String ip = "127.0.0.1";

    private Minio minio = new Minio();

    /**
     * <AUTHOR>
     * @version 1.0.0
     */
    @Data
    public static class Minio {

        private String url;
        private String accessKey;
        private String secretKey;
    }
}
