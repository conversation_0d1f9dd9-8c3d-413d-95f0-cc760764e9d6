import { useMessage } from 'naive-ui'
import { computed, onUnmounted, ref } from 'vue'

import verificationApi from '@/api/verification'
import { VERIFICATION_STATUS } from '@/constants/verification/verification-status.constants'
import type { PrivilegeVerificationResponse } from '@/types/verification/privilege-verification-response.types'
import type { PrivilegeVerificationStartRequest } from '@/types/verification/privilege-verification-start-request.types'
import type { PrivilegeVerificationSubmitRequest } from '@/types/verification/privilege-verification-submit-request.types'
import type { VerificationTimeInfoResponse } from '@/types/verification/verification-time-info-response.types'

/**
 * 特权验证流程状态管理组合式函数
 */
export function usePrivilegeVerification() {
  const message = useMessage()

  // 验证流程状态
  const verificationData = ref<PrivilegeVerificationResponse | null>(null)
  const isLoading = ref(false)
  const isSubmitting = ref(false)
  const timeInfo = ref<VerificationTimeInfoResponse | null>(null)
  const timer = ref<number | null>(null)

  // 计算属性
  const currentStep = computed(() => verificationData.value?.currentStep || 1)
  const verificationStatus = computed(() => verificationData.value?.status || 0)
  const isInProgress = computed(() => verificationStatus.value === VERIFICATION_STATUS.IN_PROGRESS)
  const isCompleted = computed(() => verificationStatus.value === VERIFICATION_STATUS.SUCCESS)
  const isFailed = computed(() => verificationStatus.value === VERIFICATION_STATUS.FAILED)
  const isTimeout = computed(() => verificationStatus.value === VERIFICATION_STATUS.TIMEOUT)
  const remainingTime = computed(() => timeInfo.value?.remainingTime || 0)
  const isExpired = computed(() => timeInfo.value?.expired || false)

  /**
   * 启动特权验证流程
   */
  const startVerification = async (
    request: PrivilegeVerificationStartRequest,
  ): Promise<boolean> => {
    try {
      isLoading.value = true
      const response = await verificationApi.startVerification(request)

      if (response.success && response.data) {
        verificationData.value = response.data
        message.success('验证流程已启动')

        // 启动计时器
        await startTimer()
        return true
      } else {
        message.error(response.message || '启动验证流程失败')
        return false
      }
    } catch (error) {
      console.error('启动验证流程失败:', error)
      message.error('启动验证流程失败')
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 查询验证流程状态
   */
  const queryVerificationStatus = async (id: number): Promise<boolean> => {
    try {
      const response = await verificationApi.verificationStatus(id)

      if (response.success && response.data) {
        verificationData.value = response.data
        return true
      } else {
        message.error(response.message || '查询验证状态失败')
        return false
      }
    } catch (error) {
      console.error('查询验证状态失败:', error)
      message.error('查询验证状态失败')
      return false
    }
  }

  /**
   * 提交验证内容
   */
  const submitVerificationContent = async (content: string): Promise<boolean> => {
    if (!verificationData.value) {
      message.error('验证流程不存在')
      return false
    }

    try {
      isSubmitting.value = true
      const request: PrivilegeVerificationSubmitRequest = { content }
      const response = await verificationApi.submitVerificationContent(
        verificationData.value.id,
        request,
      )

      if (response.success && response.data) {
        message.success('验证内容提交成功')
        // 更新验证状态
        await queryVerificationStatus(verificationData.value.id)
        return true
      } else {
        message.error(response.message || '提交验证内容失败')
        return false
      }
    } catch (error) {
      console.error('提交验证内容失败:', error)
      message.error('提交验证内容失败')
      return false
    } finally {
      isSubmitting.value = false
    }
  }

  /**
   * 启动验证计时器
   */
  const startTimer = async (): Promise<boolean> => {
    if (!verificationData.value) {
      return false
    }

    try {
      const response = await verificationApi.startVerificationTimer(verificationData.value.id)

      if (response.success && response.data) {
        timeInfo.value = response.data
        startCountdown()
        return true
      } else {
        message.error(response.message || '启动计时器失败')
        return false
      }
    } catch (error) {
      console.error('启动计时器失败:', error)
      return false
    }
  }

  /**
   * 获取剩余时间
   */
  const getRemainingTime = async (): Promise<boolean> => {
    if (!verificationData.value) {
      return false
    }

    try {
      const response = await verificationApi.remainingTime(verificationData.value.id)

      if (response.success && response.data) {
        timeInfo.value = response.data
        return true
      } else {
        return false
      }
    } catch (error) {
      console.error('获取剩余时间失败:', error)
      return false
    }
  }

  /**
   * 启动倒计时
   */
  const startCountdown = (): void => {
    if (timer.value) {
      clearInterval(timer.value)
    }

    timer.value = window.setInterval(async () => {
      if (!verificationData.value) {
        return
      }

      // 更新剩余时间
      await getRemainingTime()

      // 检查是否过期
      if (isExpired.value) {
        window.clearInterval(timer.value!)
        timer.value = null
        message.warning('验证流程已过期')
        return
      }

      // 定期查询验证状态
      await queryVerificationStatus(verificationData.value.id)

      // 如果验证完成，停止计时器
      if (!isInProgress.value) {
        window.clearInterval(timer.value!)
        timer.value = null
      }
    }, 1000)
  }

  /**
   * 停止计时器
   */
  const stopTimer = (): void => {
    if (timer.value) {
      window.clearInterval(timer.value)
      timer.value = null
    }
  }

  /**
   * 重置验证状态
   */
  const resetVerification = (): void => {
    verificationData.value = null
    timeInfo.value = null
    isLoading.value = false
    isSubmitting.value = false
    stopTimer()
  }

  /**
   * 格式化剩余时间
   */
  const formatRemainingTime = (time: number): string => {
    if (time <= 0) {
      return '00:00'
    }

    const minutes = Math.floor(time / 60000)
    const seconds = Math.floor((time % 60000) / 1000)

    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }

  // 组件卸载时清理资源
  onUnmounted(() => {
    stopTimer()
  })

  return {
    // 状态
    verificationData,
    isLoading,
    isSubmitting,
    timeInfo,

    // 计算属性
    currentStep,
    verificationStatus,
    isInProgress,
    isCompleted,
    isFailed,
    isTimeout,
    remainingTime,
    isExpired,

    // 方法
    startVerification,
    queryVerificationStatus,
    submitVerificationContent,
    startTimer,
    getRemainingTime,
    stopTimer,
    resetVerification,
    formatRemainingTime,
  }
}
