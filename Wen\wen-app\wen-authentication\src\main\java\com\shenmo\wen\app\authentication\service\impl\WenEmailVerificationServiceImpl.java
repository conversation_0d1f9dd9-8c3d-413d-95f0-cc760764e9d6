package com.shenmo.wen.app.authentication.service.impl;

import com.shenmo.wen.app.authentication.enums.EmailCodeType;
import com.shenmo.wen.app.authentication.exception.AuthenticationException;
import com.shenmo.wen.app.authentication.exception.AuthenticationExceptionEnum;
import com.shenmo.wen.app.authentication.pojo.req.SendEmailCodeReq;
import com.shenmo.wen.app.authentication.service.WenEmailVerificationService;
import com.shenmo.wen.common.util.HtmlTemplateUtils;
import com.shenmo.wen.common.util.spring.SpringRedisUtils;
import com.shenmo.wen.modules.user.config.properties.EmailProperties;
import com.shenmo.wen.modules.user.mapper.WenUserMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;

import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 邮件验证服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WenEmailVerificationServiceImpl implements WenEmailVerificationService {
    private static final String EMAIL_CODE = "email:code:%s:%s";
    private static final String EMAIL_CODE_SEND_LIMIT = "email:code:send:limit:%s:%s";

    private final JavaMailSender mailSender;
    private final EmailProperties emailProperties;
    private final WenUserMapper userMapper;
    private final SecureRandom random = new SecureRandom();

    @Override
    public boolean sendVerificationCode(SendEmailCodeReq req) {
        if (!emailProperties.getEnabled()) {
            log.info("邮件验证功能已禁用");
            return true;
        }

        final String type = req.getType();
        final String email = req.getEmail();
        final String redisKey = String.format(EMAIL_CODE, type, email);

        // 根据验证码类型进行邮箱存在性校验
        validateEmailExistence(email, type);

        final String limitKey = String.format(EMAIL_CODE_SEND_LIMIT, type, email);
        // 检查是否已存在未过期的验证码
        if (SpringRedisUtils.hasKey(limitKey)) {
            log.warn("验证码发送过于频繁，邮箱: {}, 类型: {}", email, type);
            throw new AuthenticationException(AuthenticationExceptionEnum.EMAIL_CODE_SEND_TOO_FREQUENT);
        }

        try {
            String code = generateCode();

            // 设置发送限制标记
            SpringRedisUtils.set(limitKey, "1", 59, TimeUnit.SECONDS);

            // 存储验证码到Redis，设置过期时间
            SpringRedisUtils.set(redisKey, code,
                    emailProperties.getCodeExpireMinutes(), TimeUnit.MINUTES);

            // 发送 HTML 邮件
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

            helper.setFrom(emailProperties.getFrom(), emailProperties.getPersonal());
            helper.setTo(email);

            // 根据操作类型设置不同的邮件主题
            String operation = EmailCodeType.fromCode(type).getDescription();
            String subject = String.format("【%s】%s验证码：%s - 请在%d分钟内使用",
                    emailProperties.getSubjectPrefix(), operation, code, emailProperties.getCodeExpireMinutes());
            helper.setSubject(subject);

            String htmlContent = buildHtmlEmailContent(code, type);
            helper.setText(htmlContent, true); // true 表示发送 HTML 内容

            mailSender.send(mimeMessage);
            log.info("验证码邮件发送成功，邮箱: {}, 类型: {}", email, type);
            return true;

        } catch (MessagingException e) {
            log.error("邮件格式设置失败，邮箱: {}, 类型: {}", email, type, e);
            return false;
        } catch (Exception e) {
            log.error("发送验证码邮件失败，邮箱: {}, 类型: {}", email, type, e);
            return false;
        }
    }

    @Override
    public boolean verifyCode(String email, String code, String type) {
        if (!emailProperties.getEnabled()) {
            return true;
        }

        String redisKey = String.format(EMAIL_CODE, type, email);
        String storedCode = (String) SpringRedisUtils.get(redisKey);

        if (storedCode == null) {
            log.warn("验证码不存在或已过期，邮箱: {}, 类型: {}", email, type);
            return false;
        }

        boolean isValid = storedCode.equals(code);
        if (isValid) {
            // 验证成功后删除验证码
            SpringRedisUtils.delete(redisKey);
            log.info("邮箱验证码验证成功，邮箱: {}, 类型: {}", email, type);
        } else {
            log.warn("邮箱验证码验证失败，邮箱: {}, 类型: {}", email, type);
        }

        return isValid;
    }

    @Override
    public String generateCode() {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < emailProperties.getCodeLength(); i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }

    /**
     * 根据验证码类型校验邮箱存在性
     *
     * @param email 邮箱地址
     * @param type  验证码类型
     */
    private void validateEmailExistence(String email, String type) {
        EmailCodeType codeType = EmailCodeType.fromCode(type);
        boolean emailExists = userMapper.countByEmail(email) > 0;

        switch (codeType) {
            case LOGIN, FORGOT -> {
                // 登录和找回密码需要邮箱已存在
                if (!emailExists) {
                    log.warn("邮箱不存在，无法发送{}验证码，邮箱: {}", codeType.getDescription(), email);
                    throw new AuthenticationException(AuthenticationExceptionEnum.EMAIL_NOT_EXISTS);
                }
            }
            case REGISTER -> {
                // 注册需要邮箱不存在
                if (emailExists) {
                    log.warn("邮箱已存在，无法发送注册验证码，邮箱: {}", email);
                    throw new AuthenticationException(AuthenticationExceptionEnum.EMAIL_EXISTS);
                }
            }
        }
    }

    /**
     * 构建 HTML 邮件内容
     */
    private String buildHtmlEmailContent(String code, String type) {
        String operation = EmailCodeType.fromCode(type).getDescription();
        String subjectPrefix = emailProperties.getSubjectPrefix();
        String websiteDomain = emailProperties.getWebsiteDomain();

        Map<String, Object> variables = new HashMap<>();
        variables.put("subjectPrefix", subjectPrefix);
        variables.put("operation", operation);
        variables.put("code", code);
        variables.put("codeExpireMinutes", emailProperties.getCodeExpireMinutes());
        variables.put("websiteDomain", websiteDomain);

        return HtmlTemplateUtils.processTemplate(HtmlTemplateUtils.Templates.EMAIL_VERIFICATION, variables);
    }
}
