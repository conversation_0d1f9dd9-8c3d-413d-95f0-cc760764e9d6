package com.shenmo.wen.common.util;

import java.util.function.Supplier;

/**
 * ThreadLocal 工具
 *
 * <AUTHOR>
 */
public abstract class ThreadLocalUtils {

    /**
     * 空的ThreadLocal
     *
     * @param <T> 对象泛型
     * @return {@link ThreadLocal}
     * <AUTHOR>
     */
    public static <T> ThreadLocal<T> empty() {
        return new ThreadLocal<>();
    }

    /**
     * 默认设置的threadLocal
     *
     * @param supplier 供给函数式接口
     * @param <T>      对象泛型
     * @return {@link ThreadLocal}
     * <AUTHOR>
     */
    public static <T> ThreadLocal<T> defaultSet(Supplier<? extends T> supplier) {
        return ThreadLocal.withInitial(supplier);
    }

    /**
     * 释放threadLocal
     *
     * @param threadLocal {@link ThreadLocal}
     * <AUTHOR>
     */
    public static void release(ThreadLocal<?>... threadLocal) {
        if (null != threadLocal) {
            for (ThreadLocal<?> local : threadLocal) {
                if (null != local) {

                    local.remove();
                }
            }
        }
    }
}
