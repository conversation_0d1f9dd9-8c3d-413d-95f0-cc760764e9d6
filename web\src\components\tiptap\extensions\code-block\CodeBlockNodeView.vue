<template>
  <NodeViewWrapper
    as="pre"
    :class="[
      'code-block-container',
      {
        'code-wrap': wrapMode,
        'code-block-readonly': !isEditable,
        'editable-mode': isEditable,
        'readonly-mode': !isEditable,
      },
    ]"
    :data-language="language"
    :data-selectable="false"
  >
    <!-- 代码块功能栏 -->
    <CodeBlockToolbar
      :language="language"
      :wrap-mode="wrapMode"
      :copy-state="copyState"
      :is-editable="isEditable"
      @toggle-wrap="toggleWrapMode"
      @copy-code="copyCode"
      @language-change="handleLanguageChange"
    />

    <!-- 代码内容区域 -->
    <div class="code-scrollbar-container" :class="{ 'code-wrap': wrapMode }">
      <NScrollbar :x-scrollable="!wrapMode" :y-scrollable="false" trigger="hover" :size="8">
        <NodeViewContent
          as="code"
          :class="[
            `language-${language}`,
            {
              'code-wrap-enabled': wrapMode,
              'code-selectable': !isEditable,
            },
          ]"
          :style="codeElementStyles"
        />
      </NScrollbar>
    </div>
  </NodeViewWrapper>
</template>

<script setup lang="ts">
import { NodeViewWrapper, NodeViewContent } from '@tiptap/vue-3'
import { NScrollbar } from 'naive-ui'
import { ref, computed, onMounted } from 'vue'

import { TIPTAP_CODE_COPY } from '@/constants/tiptap/frequency-key.constants'
import frequencyLimit from '@/utils/performance/frequency-limit'
import message from '@/utils/ui/message'

import CodeBlockToolbar from './CodeBlockToolbar.vue'

import type { Editor } from '@tiptap/core'
import type { NodeViewProps } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'

// 定义组件属性
const props = defineProps<
  NodeViewProps & {
    node: ProseMirrorNode
    editor: Editor
    getPos: () => number
    updateAttributes: (attrs: Record<string, unknown>) => void
    deleteNode: () => void
    selected: boolean
    extension: {
      name: string
      options: Record<string, unknown>
    }
    HTMLAttributes: Record<string, unknown>
  }
>()

// 响应式状态
const wrapMode = ref(false)
const copyState = ref({
  copied: false,
  timer: null as number | null,
})

// 计算属性
const isEditable = computed(() => props.editor.isEditable)

const language = computed(() => {
  const lang = props.node.attrs.language || 'text'
  return lang === 'null' ? 'text' : lang
})

const codeElementStyles = computed(() => ({
  display: 'block',
  padding: '0.8rem 1rem',
  margin: '0',
  background: 'transparent',
  border: 'none',
  borderRadius: '0',
  fontFamily: 'inherit',
  fontSize: 'inherit',
  lineHeight: 'inherit',
  whiteSpace: wrapMode.value ? 'pre-wrap' : 'pre',
  wordBreak: wrapMode.value ? 'break-word' : 'normal',
  overflowWrap: wrapMode.value ? 'break-word' : 'normal',
  width: '100%',
  boxSizing: 'border-box',
}))

// 方法
const toggleWrapMode = () => {
  wrapMode.value = !wrapMode.value
}

const copyCode = async () => {
  try {
    const codeContent = props.node.textContent || ''
    await navigator.clipboard.writeText(codeContent)

    message.success('代码已复制到剪贴板')

    // 设置复制状态
    copyState.value.copied = true

    // 使用统一的防抖工具，3秒后恢复状态
    frequencyLimit.debounce(
      TIPTAP_CODE_COPY,
      () => {
        copyState.value.copied = false
      },
      3000,
    )
  } catch (error) {
    message.error('复制失败，请手动复制')
    console.error('复制失败:', error)
  }
}

// 处理语言变更
const handleLanguageChange = (newLanguage: string) => {
  if (props.updateAttributes) {
    props.updateAttributes({ language: newLanguage })
  }
}

// 生命周期
onMounted(() => {
  // 初始化换行模式（如果需要从属性中读取）
  if (props.node.attrs.wrap !== undefined && props.node.attrs.wrap !== null) {
    wrapMode.value = Boolean(props.node.attrs.wrap)
  }
})
</script>

<style lang="scss" scoped>
@use '@/styles/tiptap/code-block';
</style>
