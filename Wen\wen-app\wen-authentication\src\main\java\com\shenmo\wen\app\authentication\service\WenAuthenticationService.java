package com.shenmo.wen.app.authentication.service;

import com.shenmo.wen.app.authentication.pojo.req.LoginReq;
import com.shenmo.wen.app.authentication.pojo.req.RegisterReq;
import com.shenmo.wen.app.authentication.pojo.req.ResetPasswordReq;
import com.shenmo.wen.modules.user.pojo.resp.WenUserResp;

/**
 * 认证服务接口
 * 
 * <AUTHOR>
 */
public interface WenAuthenticationService {

    /**
     * 登录
     *
     * @param req 登录请求
     * @return 用户信息
     */
    WenUserResp login(LoginReq req);

    /**
     * 注册
     *
     * @param req 注册请求
     * @return 用户信息
     */
    WenUserResp register(RegisterReq req);

    /**
     * 退出登录
     */
    void logout();

    /**
     * 重置密码
     *
     * @param req 重置密码请求
     */
    void resetPassword(ResetPasswordReq req);
}
