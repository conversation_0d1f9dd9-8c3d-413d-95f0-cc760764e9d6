<template>
  <div
    class="article-container"
    ref="containerRef"
    @touchstart="handleContainerTouchStart"
    @touchend="handleContainerTouchEnd"
    @touchcancel="handleContainerTouchCancel"
  >
    <NInfiniteScroll
      @load="loadMore"
      :distance="100"
      class="infinite-scroll-container"
      ref="scrollContainerRef"
    >
      <NRow
        :gutter="20"
        style="
          width: 100%;
          box-sizing: border-box;
          margin: 0 auto;
          padding: 0 0.25rem;
          flex: 1;
          overflow-y: auto;
        "
      >
        <NCol v-for="(article, index) in articleList" :key="article.id" :span="cardColSpan">
          <ArticleCard
            :article="article"
            :index="index"
            :card-color="getCardColor(article.id, index)"
            :is-dragging="isDragging"
            :dragged-article="draggedArticle || undefined"
            :drag-over-card-id="dragOverCardId || undefined"
            :drag-over-position="dragOverPosition || undefined"
            :is-single-card-row="isSingleCardRow"
            :drag-style="dragStyle"
            @toggle-scope="handleToggleScope"
            @start-long-press="handleStartLongPress"
            @cancel-long-press="cancelLongPress"
            @download="handleDownload"
            @set-editor="handleSetEditor"
          />
        </NCol>
      </NRow>
      <div class="infinite-load-info">
        <NSpin v-if="loading" class="display-flex" />
        <NEmpty v-if="noMore" description="没有更多文章了..." />
      </div>
    </NInfiniteScroll>

    <!-- 垃圾篓组件 -->
    <TrashBin :visible="showTrashBin" :is-active="isOverTrashBin" />
  </div>
</template>

<script lang="ts" setup>
import { NRow, NCol, NSpin, NEmpty, NInfiniteScroll } from 'naive-ui'
import { onMounted, watch, type Ref } from 'vue'

import articleApi from '@/api/article'
import ArticleCard from '@/components/home/<USER>'
import TrashBin from '@/components/home/<USER>'
import { useArticleDrag } from '@/composables/article/useArticleDrag'
import { useArticleList } from '@/composables/article/useArticleList'
import type { ArticleDownloadEditor } from '@/types/api/article-download-editor.types'
import type { Article } from '@/types/article/article.types'
import type { TiptapEditorRef } from '@/types/component/tiptap-editor-ref.types'
import type { SearchCondition } from '@/types/search/search-condition.types'
import logger from '@/utils/log/log' // 引入日志工具

const props = defineProps<{
  searchCondition: Ref<SearchCondition>
}>()

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const emit = defineEmits(['reset'])

// 使用文章列表组合式函数
const {
  articleList,
  loading,
  noMore,
  cardColSpan,
  articleTiptapEditorMap,
  containerRef,
  scrollContainerRef,
  getCardColor,
  updateColSpan,
  resetList,
  loadArticles,
  handleToggleScope,
  handleDeleteArticle,
  handleReorderArticles,
} = useArticleList(props)

// 使用拖拽组合式函数
const {
  isDragging,
  draggedArticle,
  showTrashBin,
  isOverTrashBin,
  dragStyle,
  dragOverCardId,
  dragOverPosition,
  isSingleCardRow,
  startLongPress,
  cancelLongPress,
  isLongPressActive,
} = useArticleDrag({
  onDragStart: (article) => {
    logger.debug('开始拖拽文章:', article.title)
  },
  onDelete: handleDeleteArticle,
  onReorder: handleReorderArticles,
})

// 增强搜索条件监听，增加详细的状态追踪
watch(
  props.searchCondition,
  (newCondition, oldCondition) => {
    // 日志追踪
    logger.debug('搜索条件变化:', {
      oldCondition,
      newCondition,
      articleListLength: articleList.value.length,
    })

    // 检查条件是否实质性变化
    const hasSignificantChange = Object.keys(newCondition).some((key) => {
      // 对于布尔值和字符串，直接比较
      if (['searchKey', 'tag'].includes(key)) {
        return newCondition[key] !== oldCondition[key]
      }
      // 对于布尔类型的搜索条件，检查变化
      if (['owner', 'interaction', 'favorite'].includes(key)) {
        return newCondition[key] !== oldCondition[key]
      }
      return false
    })

    // 只有在条件实质性变化时才重置列表
    if (hasSignificantChange) {
      logger.info('搜索条件发生实质性变化，重置文章列表')
      resetList()
      // 强制加载文章
      loadArticles()
    } else {
      logger.debug('搜索条件未发生实质性变化，不执行重置')
    }
  },
  { deep: true },
)

onMounted(() => {
  updateColSpan()
  window.addEventListener('resize', resizeCallback)
})

const resizeCallback = () => {
  updateColSpan()
}

// 加载更多文章
const loadMore = () => {
  loadArticles(true)
}

// 处理开始长按
const handleStartLongPress = (
  event: MouseEvent | TouchEvent,
  article: Article,
  avatarElement: HTMLElement,
) => {
  startLongPress(event, article, avatarElement)
}

// 处理下载
const handleDownload = (id: string) => {
  const editorRef = articleTiptapEditorMap.value.get(id)
  if (editorRef?.editor) {
    articleApi.md(id, editorRef.editor as ArticleDownloadEditor)
  }
}

// 处理设置编辑器
const handleSetEditor = (articleId: string, editor: TiptapEditorRef) => {
  articleTiptapEditorMap.value.set(articleId, editor)
}

// 暴露方法给父组件
defineExpose({
  loadArticles,
  resetList,
})

const handleContainerTouchStart = () => {
  // 容器触摸开始时不需要特殊处理
}

const handleContainerTouchEnd = () => {
  // 容器触摸结束时不需要特殊处理，拖拽由全局事件处理
}

const handleContainerTouchCancel = () => {
  // 只在触摸被系统取消时处理
  if (isLongPressActive) {
    logger.debug('容器触摸取消 - 取消长按检测')
    cancelLongPress()
  }
}
</script>

<style lang="scss" scoped>
@use '@/styles/article/article-modal';
</style>
