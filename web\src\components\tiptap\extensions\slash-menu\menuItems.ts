import {
  TextHeader120FilledSvg,
  TextHeader220FilledSvg,
  TextHeader320FilledSvg,
  TextBold20FilledSvg,
  ItalicSvg,
  TextStrikethrough20FilledSvg,
  TextUnderline24FilledSvg,
  Code20FilledSvg,
  TextBulletListLtr16FilledSvg,
  TextNumberListLtr16FilledSvg,
  TaskListLtr24FilledSvg,
  BlockquoteSvg,
  Image28RegularSvg,
  LinkOutlinedSvg,
  VideoClip24RegularSvg,
  LineHorizontal120FilledSvg,
} from '@/icons'

import type { SlashMenuItem } from './types'
import type { Editor } from '@tiptap/core'

// 图片上传触发器类型
type ImageUploadTrigger = () => void

// 模态框触发器类型
type ModalTrigger = (title: string, callback: () => void, needInput?: boolean) => void

// 创建默认的斜杠菜单项
export const createDefaultSlashMenuItems = (
  imageUploadTrigger?: ImageUploadTrigger,
  modalTrigger?: ModalTrigger,
): SlashMenuItem[] => [
  {
    id: 'heading1',
    name: '标题 1',
    icon: TextHeader120FilledSvg,
    keywords: 'h1 标题 heading',
    shortcut: 'Ctrl+Alt+1',
    action: (editor: Editor) => editor.chain().focus().toggleHeading({ level: 1 }).run(),
  },
  {
    id: 'heading2',
    name: '标题 2',
    icon: TextHeader220FilledSvg,
    keywords: 'h2 标题 heading',
    shortcut: 'Ctrl+Alt+2',
    action: (editor: Editor) => editor.chain().focus().toggleHeading({ level: 2 }).run(),
  },
  {
    id: 'heading3',
    name: '标题 3',
    icon: TextHeader320FilledSvg,
    keywords: 'h3 标题 heading',
    shortcut: 'Ctrl+Alt+3',
    action: (editor: Editor) => editor.chain().focus().toggleHeading({ level: 3 }).run(),
  },
  '|',
  {
    id: 'bold',
    name: '加粗',
    icon: TextBold20FilledSvg,
    keywords: 'bold 加粗 粗体 b',
    shortcut: 'Ctrl+B',
    action: (editor: Editor) => editor.chain().focus().toggleBold().run(),
  },
  {
    id: 'italic',
    name: '斜体',
    icon: ItalicSvg,
    keywords: 'italic 斜体 i',
    shortcut: 'Ctrl+I',
    action: (editor: Editor) => editor.chain().focus().toggleItalic().run(),
  },
  {
    id: 'strike',
    name: '删除线',
    icon: TextStrikethrough20FilledSvg,
    keywords: 'strike 删除线 strikethrough',
    action: (editor: Editor) => editor.chain().focus().toggleStrike().run(),
  },
  {
    id: 'underline',
    name: '下划线',
    icon: TextUnderline24FilledSvg,
    keywords: 'underline 下划线 u',
    shortcut: 'Ctrl+U',
    action: (editor: Editor) => editor.chain().focus().toggleUnderline().run(),
  },
  {
    id: 'code',
    name: '行内代码',
    icon: Code20FilledSvg,
    keywords: 'code 代码 行内代码 inline',
    shortcut: 'Ctrl+E',
    action: (editor: Editor) => editor.chain().focus().toggleCode().run(),
  },
  '|',
  {
    id: 'bulletList',
    name: '无序列表',
    icon: TextBulletListLtr16FilledSvg,
    keywords: 'ul 列表 list bullet',
    action: (editor: Editor) => editor.chain().focus().toggleBulletList().run(),
  },
  {
    id: 'orderedList',
    name: '有序列表',
    icon: TextNumberListLtr16FilledSvg,
    keywords: 'ol 列表 list ordered number',
    action: (editor: Editor) => editor.chain().focus().toggleOrderedList().run(),
  },
  {
    id: 'taskList',
    name: '任务列表',
    icon: TaskListLtr24FilledSvg,
    keywords: 'todo 任务 task checklist',
    action: (editor: Editor) => editor.chain().focus().toggleTaskList().run(),
  },
  '|',
  {
    id: 'blockquote',
    name: '引用',
    icon: BlockquoteSvg,
    keywords: 'quote 引用 blockquote',
    action: (editor: Editor) => editor.chain().focus().toggleBlockquote().run(),
  },
  {
    id: 'codeBlock',
    name: '代码块',
    icon: Code20FilledSvg,
    keywords: 'code 代码 codeblock',
    shortcut: 'Ctrl+Alt+C',
    action: (editor: Editor) => editor.chain().focus().toggleCodeBlock().run(),
  },
  '|',
  {
    id: 'image',
    name: '图片',
    icon: Image28RegularSvg,
    keywords: 'image 图片 img picture',
    action: (_editor: Editor) => {
      // 图片上传逻辑在 SlashMenuExtension 中特殊处理
      // 实际的图片上传会通过 imageUploadTrigger 触发
    },
  },
  {
    id: 'link',
    name: '链接',
    icon: LinkOutlinedSvg,
    keywords: 'link 链接 url',
    action: (_editor: Editor) => {
      // 与工具栏一致的链接插入逻辑
      if (modalTrigger) {
        modalTrigger('插入链接', () => {}, false)
      }
    },
  },
  {
    id: 'bilibili',
    name: 'B站视频',
    icon: VideoClip24RegularSvg,
    keywords: 'bilibili b站 视频 video',
    action: (_editor: Editor) => {
      // 与工具栏一致的B站视频插入逻辑
      if (modalTrigger) {
        modalTrigger(
          '插入bilibili视频链接',
          () => {
            // 这个回调函数不会被调用，实际逻辑在 EditorModalHandler 中处理
          },
          true,
        )
      }
    },
  },
  {
    id: 'horizontalRule',
    name: '分割线',
    icon: LineHorizontal120FilledSvg,
    keywords: 'hr 分割线 divider line',
    action: (editor: Editor) => editor.chain().focus().setHorizontalRule().run(),
  },
]
