// Mention基础样式 - 统一的mention样式定义
%mention-base {
  display: inline-flex;
  align-items: center;
  background: var(--gray-2);
  border-radius: 0.2rem;
  padding: 0.15rem 0.3rem;
  margin: 0 0.1rem;
  gap: 0.3rem;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: var(--gray-3);
  }
}

%mention-avatar-base {
  max-width: initial;
  min-width: initial;
  display: inline-block;
  margin: 0;
  box-shadow: none;
  border-radius: 50%;
  height: 1.25rem;
  width: 1.25rem;
  vertical-align: middle;
  transition: none;
  order: 2;
  object-fit: cover;
  flex-shrink: 0;

  &:hover {
    transform: none;
  }
}

%mention-name-base {
  font-size: 0.85rem;
  color: var(--purple);
  order: 1;
  font-weight: 500;
  flex-shrink: 0;
}

// 应用基础样式到所有mention元素
.mention,
span[data-type='mention'] {
  @extend %mention-base;

  img,
  .mention-avatar {
    @extend %mention-avatar-base;
  }

  .mention-name {
    @extend %mention-name-base;
  }
}

// 确保mention元素在任何情况下都有正确的样式
span[data-type='mention'] {
  // 处理复制粘贴后的mention元素（简单文本形式）
  &:empty::before {
    content: '@' attr(data-label);
    font-size: 0.85rem;
    color: var(--purple);
    font-weight: 500;
  }

  // 确保contenteditable为false
  &[contenteditable='false'] {
    user-select: none;
  }
}

// ProseMirror编辑器内的mention样式继承基础样式
.ProseMirror {
  .mention,
  span[data-type='mention'] {
    @extend %mention-base;

    img,
    .mention-avatar {
      @extend %mention-avatar-base;
    }

    .mention-name {
      @extend %mention-name-base;
    }
  }
}

// mention 菜单样式 - 参考斜杠菜单
.dropdown-menu {
  pointer-events: all;
  width: max-content;
  max-height: 15rem;
  padding: 0.25rem;
  overflow: hidden auto;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.5rem;
  background-color: var(--bg-color, white);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 10%),
    0 2px 4px -2px rgba(0, 0, 0, 10%);
  z-index: 10000;

  // 位置适应样式
  &.dropdown-menu-above {
    transform-origin: bottom center;
    animation: slide-up-fade-in 0.15s ease-out;
  }

  &.dropdown-menu-below {
    transform-origin: top center;
    animation: slide-down-fade-in 0.15s ease-out;
  }

  button {
    background: none;
    border: none;
    display: flex;
    align-items: center;
    border-radius: 0.25rem;
    gap: 0.25rem;
    width: 100%;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s;
    padding: 0.25rem 0.5rem;
    color: var(--purple);
    font-weight: 500;
    font-size: 0.9rem;

    &:hover,
    &.is-selected {
      background-color: rgba(0, 0, 0, 5%);
    }

    .dropdown-avatar {
      width: 1.5rem;
      height: 1.5rem;
      border-radius: 50%;
      object-fit: cover;
    }
  }

  .item {
    padding: 0.25rem;
    color: #888;
  }
}

// 暗色主题支持
.dark-theme {
  .dropdown-menu {
    --bg-color: #1f2937;
    --border-color: #374151;
    --text-color: #f9fafb;
    --text-active: #fff;
    --text-muted: #9ca3af;
    --bg-hover: #374151;

    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 30%),
      0 2px 4px -2px rgba(0, 0, 0, 20%);

    button {
      &:hover,
      &.is-selected {
        background-color: rgba(255, 255, 255, 10%);
      }
    }

    .item {
      color: #aaa;
    }
  }
}

// 滚动条样式
.dropdown-menu::-webkit-scrollbar {
  width: 5px;
}

.dropdown-menu::-webkit-scrollbar-track {
  background: transparent;
}

.dropdown-menu::-webkit-scrollbar-thumb {
  background: var(--border-color, #e5e7eb);
  border-radius: 5px;
}

.dropdown-menu * {
  scrollbar-width: thin;
  scrollbar-color: var(--border-color, #e5e7eb) transparent;
}

// 动画定义
@keyframes slide-down-fade-in {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slide-up-fade-in {
  from {
    opacity: 0;
    transform: translateY(8px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
