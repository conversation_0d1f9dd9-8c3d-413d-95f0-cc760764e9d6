/**
 * Naive UI 全局配置
 * 用于统一配置所有 Naive UI 组件的默认行为
 */

import type { DialogOptions } from '@/types/ui/dialog-options.types'
import type { ModalOptions } from '@/types/ui/modal-options.types'

import type { GlobalThemeOverrides } from 'naive-ui'

/**
 * 全局主题覆盖配置
 * 禁用所有弹框组件的默认聚焦行为
 */
export const globalThemeOverrides: GlobalThemeOverrides = {
  // 对话框配置
  Dialog: {
    // 禁用默认聚焦
    // 注意：Naive UI 的 Dialog 组件可能不支持通过主题覆盖来设置 autoFocus
    // 这里主要是作为配置的占位符，实际的 autoFocus 需要在组件级别设置
  },
  // 模态框配置
  Modal: {
    // 禁用默认聚焦
    // 注意：Naive UI 的 Modal 组件可能不支持通过主题覆盖来设置 autoFocus
    // 这里主要是作为配置的占位符，实际的 autoFocus 需要在组件级别设置
  },
}

/**
 * 全局组件配置
 * 用于设置组件的默认 props
 */
export const globalComponentConfig = {
  // 对话框默认配置
  dialog: {
    autoFocus: false,
    closeOnEsc: true,
    maskClosable: false,
  },
  // 模态框默认配置
  modal: {
    autoFocus: false,
    closeOnEsc: true,
    maskClosable: false,
  },
}

/**
 * 创建带有默认配置的对话框选项
 */
export function createDialogOptions(options: DialogOptions): DialogOptions {
  return {
    ...globalComponentConfig.dialog,
    ...options,
  }
}

/**
 * 创建带有默认配置的模态框选项
 */
export function createModalOptions(options: ModalOptions): ModalOptions {
  return {
    ...globalComponentConfig.modal,
    ...options,
  }
}
