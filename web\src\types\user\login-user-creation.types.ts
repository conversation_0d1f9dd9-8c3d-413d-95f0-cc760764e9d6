import type { LoginUser } from './login-user.types'
import type { UserConfig } from './user-config.types'

/**
 * 创建登录用户时的参数类型
 * 允许部分字段为可选，用于从其他用户类型转换
 */
export interface LoginUserCreationParams {
  /** 用户唯一标识符 */
  id: string
  /** 用户名 */
  username: string
  /** 用户等级 */
  level: number
  /** 手机号码（可选） */
  phone?: string
  /** 邮箱地址（可选） */
  email?: string
  /** 用户头像URL（可选） */
  avatar?: string
  /** IP地理位置（可选） */
  ipLocation?: string
  /** 职业信息（可选） */
  job?: string
  /** 通知接收类型（可选） */
  notificationReceiveType?: number
  /** 用户配置信息（可选） */
  config?: UserConfig
  /** 用户简介（可选，用于兼容其他用户类型） */
  bio?: string
  /** 注册时间（可选，用于兼容其他用户类型） */
  createdAt?: string
  /** 最后登录时间（可选，用于兼容其他用户类型） */
  lastLoginAt?: string
}

/**
 * 创建完整的 LoginUser 对象
 * 为缺失的字段提供默认值
 */
export function createLoginUser(params: LoginUserCreationParams): LoginUser {
  return {
    id: params.id,
    username: params.username,
    phone: params.phone || '',
    email: params.email || '',
    avatar: params.avatar || '',
    ipLocation: params.ipLocation || '',
    job: params.job || '',
    level: params.level,
    notificationReceiveType: params.notificationReceiveType || 0,
    config: params.config || { homeCard: true },
  }
}
