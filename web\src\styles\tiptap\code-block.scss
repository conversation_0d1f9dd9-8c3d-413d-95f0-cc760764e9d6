/*
 * CodeBlock 相关组件样式
 * 代码块节点视图和工具栏组件的样式定义
 */

/*
 * CodeBlockNodeView 组件样式
 * 代码块节点视图组件的样式定义，包括容器、滚动条和编辑模式
 */

.code-block-container {
  position: relative;
  background-color: var(--creamy-white-1, #eeece4);
  border-radius: 4px;
  padding: 0;
  margin: 1rem 0;
  overflow: hidden;
  font-family: Consolas, 'Source Code Pro', 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  border: 1px solid var(--creamy-white-3, #dcd8ca);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 5%);
  max-width: 100%;
  width: 100%;

  /* 编辑模式下确保可以接收焦点和事件 */
  &.editable-mode {
    pointer-events: auto;

    :deep(.n-scrollbar-content) {
      pointer-events: auto;
    }

    :deep(code[contenteditable='true']) {
      pointer-events: auto;
      cursor: text;

      &:focus {
        outline: none;
        box-shadow: none;
      }
    }
  }
}

/* 工具栏样式已移至 CodeBlockToolbar.vue */

.code-scrollbar-container {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  position: relative;
  min-height: 3rem;
}

/* NScrollbar 样式 */
:deep(.n-scrollbar) {
  width: 100%;
  height: auto;
  max-width: 100%;
  box-sizing: border-box;
}

/* 非换行模式：内容宽度自适应，允许水平滚动 */
:not(.code-wrap) :deep(.n-scrollbar-content) {
  width: max-content;
  min-width: 100%;
  max-width: none; /* 允许内容超出以触发滚动 */
}

/* 换行模式：内容宽度100%，禁用水平滚动 */
.code-wrap :deep(.n-scrollbar-content) {
  width: 100%;
  max-width: 100%;
}

/* 水平滚动条轨道 - 透明背景 */
:deep(.n-scrollbar-rail--horizontal) {
  height: 8px;
  background-color: transparent;
  border-radius: 4px;
  bottom: 2px;

  /* 强制启用滚动条 */
  &.n-scrollbar-rail--disabled {
    display: block !important;
    opacity: 1 !important;
    pointer-events: auto !important;
  }
}

/* 水平滚动条滑块 */
:deep(.n-scrollbar-thumb--horizontal) {
  background-color: var(--gray-4, rgba(53, 38, 28, 40%));
  border-radius: 4px;
  transition: background-color 0.2s ease;
  min-width: 20px;
  height: 6px;
}

:deep(.n-scrollbar-thumb--horizontal:hover) {
  background-color: var(--gray-5, rgba(28, 25, 23, 60%));
}

/* 换行模式下隐藏水平滚动条 */
.code-wrap :deep(.n-scrollbar-rail--horizontal) {
  display: none !important;
}

/*
 * CodeBlockToolbar 组件样式
 * 代码块工具栏组件的样式定义，包括头部、语言标签、按钮等
 */

.code-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--creamy-white-2, #e4e1d8);
  padding: 0.4rem 0.8rem;
  border-bottom: 1px solid var(--creamy-white-3, #dcd8ca);
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans',
    'Helvetica Neue', sans-serif;
  font-size: 0.85rem;
  color: var(--gray-5, rgba(28, 25, 23, 60%));
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  flex-shrink: 0;
}

.code-block-language-container {
  flex-shrink: 0;

  .code-block-language {
    color: var(--gray-5, rgba(28, 25, 23, 60%));
    font-weight: 500;
    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans',
      'Helvetica Neue', sans-serif;
    text-transform: lowercase;
  }

  .code-language-select {
    width: auto;
    min-width: 80px;

    :deep(.n-base-selection) {
      background: transparent;
      border: 1px solid var(--border-color, #d9d9d9);
      border-radius: 4px;
      font-size: 0.85rem;
      font-weight: 500;
      text-transform: lowercase;
      width: auto;

      &:hover {
        border-color: var(--primary-color, #1890ff);
      }

      &:focus-within {
        border-color: var(--primary-color, #1890ff);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }

    :deep(.n-base-selection-label) {
      color: var(--gray-5, rgba(28, 25, 23, 60%));
      font-family:
        -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
        'Open Sans', 'Helvetica Neue', sans-serif;
      white-space: nowrap;
    }
  }
}

.code-block-toolbar {
  display: flex;
  gap: 0.5rem;
}

.code-wrap-button,
.code-copy-button {
  width: 24px;
  height: 24px;
  padding: 4px;
  background-color: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.code-wrap-button svg,
.code-copy-button svg {
  width: 16px;
  height: 16px;
  stroke: var(--gray-5, rgba(28, 25, 23, 60%));
  transition: all 0.2s ease;
}

.code-wrap-button:hover,
.code-copy-button:hover {
  background-color: var(--creamy-white-3, #dcd8ca);
}

.code-wrap-button:hover svg,
.code-copy-button:hover svg {
  stroke: var(--black, #2e2b29);
}

/* 按钮激活状态 */
.code-wrap-button.active,
.code-copy-button.active {
  background-color: var(--creamy-white-3, #dcd8ca);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 10%);
}

.code-wrap-button.active svg,
.code-copy-button.active svg {
  stroke: var(--black, #2e2b29);
}

/* 复制按钮成功状态 */
.code-copy-button.copied {
  background-color: rgba(34, 197, 94, 10%);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 5%);
}

.code-copy-button.copied svg {
  stroke: var(--green, #22c55e);
}

/* 换行按钮激活状态 */
.code-wrap-button.active {
  background-color: rgba(75, 163, 253, 10%);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 5%);
}

.code-wrap-button.active svg {
  stroke: var(--blue, #4ba3fd);
}
