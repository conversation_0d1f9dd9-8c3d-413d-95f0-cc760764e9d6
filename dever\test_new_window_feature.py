#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新窗口功能的脚本
"""

import json
from cursor_augment_automation import CursorAugmentAutomation, load_config

def test_new_window_feature():
    """测试新窗口功能"""
    print("=== 测试新窗口功能 ===")
    
    # 加载当前配置
    config = load_config()
    print(f"当前新窗口设置: {config['automation'].get('enable_new_window', False)}")
    
    # 测试禁用新窗口模式
    print("\n1. 测试禁用新窗口模式...")
    config["automation"]["enable_new_window"] = False
    automation = CursorAugmentAutomation(config)
    print(f"新窗口模式: {automation.config['automation']['enable_new_window']}")
    
    # 测试启用新窗口模式
    print("\n2. 测试启用新窗口模式...")
    config["automation"]["enable_new_window"] = True
    automation = CursorAugmentAutomation(config)
    print(f"新窗口模式: {automation.config['automation']['enable_new_window']}")
    
    print("\n=== 测试完成 ===")
    print("功能说明:")
    print("- 当'开启新窗口'未勾选时：直接在当前对话框输入内容并发送")
    print("- 当'开启新窗口'勾选时：先按Ctrl+L创建新对话，然后发送内容")

if __name__ == "__main__":
    test_new_window_feature()
