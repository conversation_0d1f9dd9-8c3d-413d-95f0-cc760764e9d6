/*
 * ThemeToggle 组件样式
 * 主题切换按钮的样式定义，包括日月交替场景动画
 */

/* 日月交替场景 - 快速版 */
.theme-toggle-scene {
  position: relative;
  width: 48px;
  height: 28px;
  border-radius: 14px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 20%);
  background: linear-gradient(180deg, #4a90e2 0%, #a5d6ff 100%);
}

.theme-toggle-scene.is-dark {
  background: linear-gradient(180deg, #000814 0%, #001440 100%);
}

.sky {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.sun,
.moon {
  position: absolute;
  width: 18px;
  height: 18px;
  transition: all 0.25s ease-out;
}

.sun {
  color: #ffd700;
  left: 14px;
  top: 5px;
  opacity: 1;
  z-index: 2;
}

.sun-set {
  transform: translateY(30px);
  opacity: 0;
}

.moon {
  color: #fff;
  right: 12px;
  top: 4px;
  opacity: 0;
  z-index: 2;
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 80%));
  width: 20px;
  height: 20px;
}

.moon-rise {
  opacity: 1;
}

/* 简化云彩和星星装饰元素 */
.theme-toggle-scene::before {
  content: '';
  position: absolute;
  width: 10px;
  height: 4px;
  background-color: rgba(255, 255, 255, 70%);
  border-radius: 4px;
  left: 5px;
  top: 18px;
  transition: all 0.25s ease;
  opacity: 1;
  z-index: 1;
}

.theme-toggle-scene::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 3px;
  background-color: rgba(255, 255, 255, 50%);
  border-radius: 3px;
  right: 7px;
  top: 6px;
  transition: all 0.25s ease;
  opacity: 1;
  z-index: 1;
}

.theme-toggle-scene.is-dark::before,
.theme-toggle-scene.is-dark::after {
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 90%);
}

.theme-toggle-scene.is-dark::before {
  left: 12px;
  top: 8px;
}

.theme-toggle-scene.is-dark::after {
  right: 10px;
  top: 16px;
}

/* 简化星星效果 */
.theme-toggle-scene.is-dark .sky::before,
.theme-toggle-scene.is-dark .sky::after {
  content: '';
  position: absolute;
  width: 1px;
  height: 1px;
  background-color: white;
  border-radius: 50%;
  opacity: 0.9;
  z-index: 1;
}

.theme-toggle-scene.is-dark .sky::before {
  left: 20px;
  top: 10px;
}

.theme-toggle-scene.is-dark .sky::after {
  left: 32px;
  top: 6px;
}

/* 鼠标悬停效果 */
.theme-toggle-scene:hover {
  transform: scale(1.02);
}

/* 点击效果 */
.theme-toggle-scene:active {
  transform: scale(0.98);
}
