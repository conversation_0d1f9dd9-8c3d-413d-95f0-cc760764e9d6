---
type: "manual"
---

web项目工程目录规范：
1. 所有的组件按照业务功能进行目录划分
2. 所有的样式都放在src/styles文件夹中，按照组件的目录划分规则进行划分
3. 所有的函数都放在src/components以及src/utils文件夹中保持composables + utils的双重分类，按照组件的目录划分规则进行划分
4. 所有的类型都放在src/types文件夹中，每个类型都使用单独的文件，不允许一个文件中存在多个类型，按照组件的目录划分规则进行划分
5. 所有的常量都放在src/constants文件夹中，按照组件的目录划分规则进行划分
6. 所有的api都放在src/api文件夹中，按照组件的目录划分规则进行划分
7. 所有的模板组件全部使用tsx的形式，不允许使用h函数
8. 不允许使用任何common文件夹与common命名，必须使用具体的业务命名
9. 不允许使用any、unknow类型，发现后需要使用或创建对应具体的类型文件

重构过程中严格遵守下方事项：
1. 逐个文件检查并开始实施，完成修改后在web目录下运行npm run code-check检查是否存在异常并严格解决，不允许忽略任何问题
2. 重构不允许影响原有业务逻辑
3. 重构后不需要输出任何总结性内容与测试性内容
4. 按照规范序号顺序执行重构任务
5. 每个文件不允许超过300行，超过则新建文件
6. 新创建的文件命名必须能表示该文件的行为，也就是业务命名清晰
7. 不要考虑历史包袱，不需要兼容旧代码，不需要保留弃用的、无用的代码