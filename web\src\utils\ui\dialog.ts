import { createDiscrete<PERSON><PERSON> } from 'naive-ui'

import { createDialogOptions } from '@/config/naive-ui-config'
import type { DialogOptions } from '@/types/ui/dialog-options.types'

const { dialog: naiveDialog } = createDiscreteApi(['dialog'])

// 包装 dialog 方法，自动应用全局配置
const dialog = {
  info: (options: DialogOptions) => naiveDialog.info(createDialogOptions(options)),
  success: (options: DialogOptions) => naiveDialog.success(createDialogOptions(options)),
  warning: (options: DialogOptions) => naiveDialog.warning(createDialogOptions(options)),
  error: (options: DialogOptions) => naiveDialog.error(createDialogOptions(options)),
  create: (options: DialogOptions) => naiveDialog.create(createDialogOptions(options)),
}

export default dialog
