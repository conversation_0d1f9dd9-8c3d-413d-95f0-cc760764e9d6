// 特权页面布局样式
.privilege-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;

  .privilege-layout-top {
    display: flex;
    flex-direction: column;
    padding: 20px;
    background: var(--card-color);
    border-bottom: 1px solid var(--border-color);
    z-index: 10;

    .middle-controls-container {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 20px;
      margin-bottom: 20px;

      @media (width <= 768px) {
        flex-direction: column;
        gap: 15px;
      }
    }
  }

  .tag-bar-wrapper {
    padding: 0 20px;
    background: var(--card-color);
    border-bottom: 1px solid var(--border-color);
  }

  .privilege-layout-content {
    flex: 1;
    overflow: hidden;
    padding: 20px;
    background: var(--body-color);
  }
}
