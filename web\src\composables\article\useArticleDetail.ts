import { ref, onMounted, watch, nextTick, type Ref } from 'vue'
import { useRoute } from 'vue-router'

import articleApi from '@/api/article'
import router from '@/router'
import { useArticleStore } from '@/stores/index'
import type { ArticleDetailResponse } from '@/types/article/article-detail-response.types'
import { type Article } from '@/types/article/article.types'
import dateTime from '@/utils/date/date-time'
import logger from '@/utils/log/log'
import { activeTheme } from '@/utils/theme/theme'
import tiptap from '@/utils/tiptap/tiptap'
import message from '@/utils/ui/message'

/**
 * 将API响应数据转换为Article类型
 * @param detail API响应的文章详情数据
 * @returns 转换后的Article对象
 */
function convertToArticle(detail: ArticleDetailResponse): Article {
  return {
    id: detail.id,
    title: detail.title,
    tags: detail.tag ? detail.tag.split(',') : [],
    tag: detail.tag || '',
    operationLevel: detail.operationLevel,
    publishedScope: detail.publishedScope,
    content: detail.content,
    contentObj: tiptap.toJsonObject(detail.content),
    publisher: detail.publisher,
    publisherAvatar: detail.publisherAvatar || '',
    isOwner: detail.isOwner,
    ipLocation: detail.ipLocation,
    publishedAt: detail.publishedAt ? dateTime.getRelativeTime(detail.publishedAt) : '',
    likeCount: detail.likeCount,
    isLike: detail.isLike,
    dislikeCount: detail.dislikeCount,
    isDislike: detail.isDislike,
    favoriteCount: detail.favoriteCount,
    isFavorite: detail.isFavorite,
    commentCount: detail.commentCount,
    lastModified: detail.lastModified ? dateTime.getRelativeTime(detail.lastModified) : '',
    exactPublishedAt: detail.publishedAt ? dateTime.toTimeString(detail.publishedAt) : '',
    exactLastModified: detail.lastModified ? dateTime.toTimeString(detail.lastModified) : '',
    shareUsers: detail.shareUsers,
  }
}

/**
 * 文章详情组合式函数返回值类型
 */
interface UseArticleDetailReturn {
  /** 文章数据 */
  article: Ref<Article>
  /** 文章加载状态 */
  articleLoading: Ref<boolean>
  /** 获取文章ID */
  getArticleId: () => string
  /** 加载文章详情 */
  loadArticleDetail: () => void
  /** 加载文章详情统计数据 */
  loadArticleDetailCount: () => void
  /** 返回首页 */
  backHome: () => void
  /** 初始化 */
  initialize: () => void
}

/**
 * 文章详情管理组合式函数
 * 提供文章详情的加载、状态管理和路由监听功能
 */
export function useArticleDetail(): UseArticleDetailReturn {
  const articleStore = useArticleStore()
  const route = useRoute()

  // 文章数据和加载状态
  const article = ref<Article>({} as Article)
  const articleLoading = ref(true)

  // 获取文章ID
  const getArticleId = () => {
    return articleStore.getId
  }

  // 加载文章详情
  const loadArticleDetail = () => {
    articleLoading.value = true
    const articleId = getArticleId()

    if (articleId) {
      articleApi
        .detail(articleId)
        .then((res) => {
          const detail = res?.data
          if (detail) {
            article.value = convertToArticle(detail)
            articleLoading.value = false
          }
          logger.debug('article detail: ', article.value)
        })
        .catch((error) => {
          articleLoading.value = false
          // 处理权限错误或不存在的文章
          if (error.response && error.response.status === 403) {
            message.error('哎呀，您没有权限查看这篇文章')
            router.push('/')
          } else {
            message.error('加载文章失败，请稍后重试')
          }
        })
    }
  }

  // 加载文章详情统计数据（评论数、点赞数等）
  const loadArticleDetailCount = () => {
    const articleId = getArticleId()
    if (articleId) {
      articleApi.detail(articleId).then((res) => {
        const detail = res?.data
        if (detail) {
          article.value.likeCount = detail.likeCount
          article.value.dislikeCount = 0 // 默认值，API中没有此字段
          article.value.favoriteCount = 0 // 默认值，API中没有此字段
          article.value.commentCount = detail.commentCount
        }
      })
    }
  }

  // 返回首页
  const backHome = () => {
    router.push('/')
  }

  // 监听路由变化，重新加载文章
  const setupRouteWatch = () => {
    watch(route, (newRoute, oldRoute) => {
      // 只在articleId变化时重新加载文章内容
      if (newRoute.params.articleId !== oldRoute?.params?.articleId) {
        loadArticleDetail()
      }
    })
  }

  // 监听主题变化并优化文章编辑器刷新
  const setupThemeWatch = () => {
    watch(activeTheme, () => {
      // 等待DOM更新
      nextTick(() => {
        if (!articleLoading.value) {
          // 刷新文章内容编辑器
          const articleEditor = document.querySelector('.article-content .ProseMirror')
          if (articleEditor instanceof HTMLElement) {
            // 使用轻量级方式触发编辑器重绘
            articleEditor.classList.add('theme-priority')
            void articleEditor.offsetHeight
          }
        }
      })
    })
  }

  // 初始化
  const initialize = () => {
    onMounted(() => {
      loadArticleDetail()
    })
    setupRouteWatch()
    setupThemeWatch()
  }

  return {
    // 状态
    article,
    articleLoading,

    // 方法
    getArticleId,
    loadArticleDetail,
    loadArticleDetailCount,
    backHome,
    initialize,
  }
}
