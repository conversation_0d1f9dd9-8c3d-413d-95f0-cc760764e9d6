<template>
  <div ref="container" class="vue-danmaku">
    <div ref="dmContainer" :class="['danmus', { show: !hidden }, { paused: paused }]"></div>
    <slot />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  onBeforeUnmount,
  type PropType,
  ref,
  reactive,
  computed,
} from 'vue'

import { useChannelManagement } from '@/composables/danmaku/useChannelManagement'
import { useDanmakuEffects } from '@/composables/danmaku/useDanmakuEffects'
import { useDanmakuOperations } from '@/composables/danmaku/useDanmakuOperations'
import type { DanmakuEmitFunction } from '@/types/danmaku/danmaku-emit-function.types'
import type { DanmakuFullConfig } from '@/types/danmaku/danmaku-full-config.types'
import type {
  DanmakuComponentProps,
  DanmakuChannelMap,
} from '@/types/danmaku/danmaku-operations-options.types'
import type { Danmu } from '@/types/danmaku/danmu.types'
import { createDanmuModelBinding } from '@/utils/danmaku/reactivity/modelBinding'
import { calculateDanmuSpeed } from '@/utils/danmaku/speed/speedCalculator'

export default defineComponent({
  name: 'DanmakuCore',
  props: {
    /**
     * 弹幕列表数据
     */
    danmus: {
      type: Array as PropType<Danmu[]>,
      required: true,
      default: () => [],
    },
    /**
     * 轨道数量，0为最大轨道数量（撑满容器）
     */
    channels: {
      type: Number,
      default: 0,
    },
    /**
     * 是否自动播放
     */
    autoplay: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否循环播放
     */
    loop: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否开启弹幕插槽，默认否
     */
    useSlot: {
      type: Boolean,
      default: false,
    },
    /**
     * 弹幕刷新频率(ms)
     */
    debounce: {
      type: Number,
      default: 100,
    },
    /**
     * 弹幕速度（像素/秒）
     */
    speeds: {
      type: Number,
      default: 200,
    },
    /**
     * 是否开启随机轨道注入弹幕
     */
    randomChannel: {
      type: Boolean,
      default: false,
    },
    /**
     * 弹幕字号（仅文本模式）
     */
    fontSize: {
      type: Number,
      default: 18,
    },
    /**
     * 弹幕垂直间距
     */
    top: {
      type: Number,
      default: 4,
    },
    /**
     * 弹幕水平间距
     */
    right: {
      type: Number,
      default: 0,
    },
    /**
     * 是否开启悬浮暂停
     */
    isSuspend: {
      type: Boolean,
      default: false,
    },
    /**
     * 弹幕额外样式
     */
    extraStyle: {
      type: String,
      default: '',
    },
  },
  emits: ['list-end', 'play-end', 'dm-over', 'dm-out', 'update:danmus'],
  setup(props, { emit, slots }) {
    // 容器
    const container = ref<HTMLDivElement>(document.createElement('div'))
    const dmContainer = ref<HTMLDivElement>(document.createElement('div'))
    const containerWidth = ref(0)
    const containerHeight = ref(0)

    // 变量
    const timer = ref(0)
    const calcChannels = ref(0)
    const danmuHeight = ref(48)
    const index = ref<number>(0)
    const hidden = ref(false)
    const paused = ref(false)

    // 使用v-model绑定弹幕列表
    const danmuList = createDanmuModelBinding<Danmu[]>(
      props,
      emit as (event: string, ...args: unknown[]) => void,
      'danmus',
    )

    // 弹幕配置对象
    const danmakuConfig = reactive({
      channels: computed(() => props.channels || calcChannels.value),
      autoplay: computed(() => props.autoplay),
      loop: computed(() => props.loop),
      useSlot: computed(() => props.useSlot),
      debounce: computed(() => props.debounce),
      randomChannel: computed(() => props.randomChannel),
      speeds: computed(() => calculateDanmuSpeed(props.speeds, containerWidth.value)),
      fontSize: computed(() => props.fontSize),
      top: computed(() => props.top),
      right: computed(() => props.right),
    })

    // 弹幕样式对象
    const danmuStyle = reactive({
      height: computed(() => danmuHeight.value),
      fontSize: computed(() => props.fontSize),
      speeds: computed(() => calculateDanmuSpeed(props.speeds, containerWidth.value)),
      top: computed(() => props.top),
      right: computed(() => props.right),
    })

    // 导入频道管理功能
    const { danChannel, getChannelIndex } = useChannelManagement(danmakuConfig)

    // 导入弹幕特效功能
    const { initSuspendEvents } = useDanmakuEffects(dmContainer, emit)

    // 导入弹幕操作功能
    const { insert, add, push, clearTimer, play, pause, stop, show, hide, reset, getPlayState } =
      useDanmakuOperations({
        props: props as DanmakuComponentProps,
        emit: emit as DanmakuEmitFunction,
        slots,
        danmakuConfig: danmakuConfig as DanmakuFullConfig,
        danmuStyle,
        danmuList,
        container,
        dmContainer,
        containerWidth,
        containerHeight,
        calcChannels,
        danmuHeight,
        index,
        hidden,
        paused,
        getChannelIndex,
        danChannel: danChannel as DanmakuChannelMap,
        timer,
      })

    function initCore() {
      containerWidth.value = container.value.offsetWidth
      containerHeight.value = container.value.offsetHeight
      if (containerWidth.value === 0 || containerHeight.value === 0) {
        throw new Error('获取不到容器宽高')
      }
    }

    function init() {
      initCore()
      if (props.isSuspend) {
        initSuspendEvents()
      }
      if (danmakuConfig.autoplay) {
        play()
      }
    }

    function resize() {
      initCore()
      const items = dmContainer.value.getElementsByClassName('dm')

      for (let i = 0; i < items.length; i++) {
        const el = items[i] as HTMLDivElement

        // 检测是否包含图片，为图片弹幕添加宽度缓冲
        const hasImages = el.querySelector('img') !== null
        let elementWidth = el.offsetWidth
        if (hasImages) {
          elementWidth += 100 // 为包含图片的弹幕添加缓冲
        }

        const totalDistance = containerWidth.value + elementWidth
        el.style.setProperty('--dm-scroll-width', `-${totalDistance}px`)
        el.style.left = `${containerWidth.value}px`
        el.style.animationDuration = `${totalDistance / (danmuStyle.speeds as number)}s`
      }
    }

    onMounted(() => {
      init()
    })

    onBeforeUnmount(() => {
      clearTimer()
      index.value = 0
    })

    return {
      // 元素引用
      container,
      dmContainer,

      // 变量
      hidden,
      paused,
      danmuList,

      // 函数
      getPlayState,
      resize,
      play,
      pause,
      stop,
      show,
      hide,
      reset,
      add,
      push,
      insert,
    }
  },
})
</script>

<style lang="scss">
@use '@/styles/danmaku-renderer';
</style>
