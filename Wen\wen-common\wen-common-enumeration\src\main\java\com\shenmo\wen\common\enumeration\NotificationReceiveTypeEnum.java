package com.shenmo.wen.common.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知接收类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum NotificationReceiveTypeEnum {

    CLOSE(0, "关闭"),
    ALL(1, "全部"),
    PUBLISH(2, "发布"),
    MODIFY(3, "修改"),
    FAVORITE(4, "收藏"),
    SHARE(5, "分享");

    private final int code;
    private final String text;

    public static NotificationReceiveTypeEnum of(int code) {
        final NotificationReceiveTypeEnum[] values = values();
        for (NotificationReceiveTypeEnum e : values) {
            if (e.code == code) {
                return e;
            }
        }
        throw new EnumConstantNotPresentException(NotificationReceiveTypeEnum.class, String.valueOf(code));
    }
}