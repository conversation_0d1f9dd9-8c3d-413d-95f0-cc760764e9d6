#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按钮检测测试脚本
用于测试和调试Cursor Augment Code面板中按钮状态的识别功能
"""

import time
import pygetwindow as gw
import cv2
import numpy as np
from PIL import Image, ImageGrab
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ButtonDetectionTest:
    def __init__(self):
        self.cursor_window = None
        
    def find_cursor_window(self):
        """查找Cursor窗口"""
        try:
            # 获取所有窗口
            all_windows = gw.getAllWindows()

            # 过滤出可能的Cursor窗口，排除我们的自动化工具
            cursor_windows = []
            for window in all_windows:
                title_lower = window.title.lower()
                if ("cursor" in title_lower and
                    "自动化工具" not in window.title and
                    "automation" not in title_lower and
                    window.title.strip() != ""):
                    cursor_windows.append(window)

            if not cursor_windows:
                logger.error("未找到Cursor编辑器窗口")
                logger.info("当前所有窗口:")
                for w in all_windows:
                    if w.title.strip():
                        logger.info(f"  - {w.title}")
                return False

            # 优先选择标题最简单的Cursor窗口（通常是编辑器）
            cursor_windows.sort(key=lambda w: len(w.title))
            self.cursor_window = cursor_windows[0]

            logger.info(f"找到Cursor窗口: {self.cursor_window.title}")
            logger.info(f"窗口位置: ({self.cursor_window.left}, {self.cursor_window.top})")
            logger.info(f"窗口大小: {self.cursor_window.width} x {self.cursor_window.height}")

            # 显示所有找到的Cursor相关窗口
            if len(cursor_windows) > 1:
                logger.info("找到多个Cursor窗口:")
                for i, w in enumerate(cursor_windows):
                    marker = "✓" if i == 0 else " "
                    logger.info(f"  {marker} {w.title}")

            return True
        except Exception as e:
            logger.error(f"查找Cursor窗口时出错: {e}")
            return False
    
    def capture_and_save_panel(self, filename="augment_panel_capture.png"):
        """截取并保存Augment面板区域"""
        try:
            if not self.cursor_window:
                return False
            
            # 激活窗口
            self.cursor_window.activate()
            time.sleep(1)
            
            # 获取窗口位置和大小
            left = self.cursor_window.left
            top = self.cursor_window.top
            width = self.cursor_window.width
            height = self.cursor_window.height
            
            # 截取左侧面板区域（假设占窗口宽度的1/3）
            panel_width = width // 3
            panel_area = (left, top, left + panel_width, top + height)
            
            logger.info(f"截取区域: {panel_area}")
            
            screenshot = ImageGrab.grab(bbox=panel_area)
            screenshot.save(filename)
            
            logger.info(f"面板截图已保存: {filename}")
            return True
        except Exception as e:
            logger.error(f"截取面板时出错: {e}")
            return False
    
    def analyze_saved_image(self, image_path="augment_panel_capture.png"):
        """分析保存的图片，帮助用户确定按钮位置"""
        try:
            if not os.path.exists(image_path):
                logger.error(f"图片文件不存在: {image_path}")
                return False
            
            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                logger.error("无法读取图片")
                return False
            
            height, width = image.shape[:2]
            logger.info(f"图片尺寸: {width} x {height}")
            
            # 显示图片信息
            logger.info("请查看保存的截图，找到发送按钮的位置")
            logger.info("然后手动截取按钮的正常状态和暂停状态图片")
            logger.info("保存为:")
            logger.info("  - templates/send_button_normal.png")
            logger.info("  - templates/send_button_pause.png")
            
            return True
        except Exception as e:
            logger.error(f"分析图片时出错: {e}")
            return False
    
    def test_template_matching(self, template_path):
        """测试模板匹配"""
        try:
            if not os.path.exists(template_path):
                logger.error(f"模板文件不存在: {template_path}")
                return False
            
            if not os.path.exists("augment_panel_capture.png"):
                logger.error("请先运行capture_panel()截取面板图片")
                return False
            
            # 读取图片
            image = cv2.imread("augment_panel_capture.png")
            template = cv2.imread(template_path)
            
            if image is None or template is None:
                logger.error("无法读取图片文件")
                return False
            
            # 模板匹配
            result = cv2.matchTemplate(image, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            logger.info(f"模板匹配结果:")
            logger.info(f"  最大匹配值: {max_val:.4f}")
            logger.info(f"  匹配位置: {max_loc}")
            
            if max_val >= 0.8:
                logger.info("✅ 匹配成功！")
                
                # 在图片上标记匹配位置
                template_h, template_w = template.shape[:2]
                top_left = max_loc
                bottom_right = (top_left[0] + template_w, top_left[1] + template_h)
                
                # 绘制矩形框
                cv2.rectangle(image, top_left, bottom_right, (0, 255, 0), 2)
                
                # 保存标记后的图片
                result_filename = f"match_result_{os.path.basename(template_path)}"
                cv2.imwrite(result_filename, image)
                logger.info(f"匹配结果已保存: {result_filename}")
            else:
                logger.warning("❌ 匹配失败，建议调整模板图片或降低阈值")
            
            return max_val >= 0.8
        except Exception as e:
            logger.error(f"测试模板匹配时出错: {e}")
            return False
    
    def create_template_directory(self):
        """创建模板目录"""
        if not os.path.exists("templates"):
            os.makedirs("templates")
            logger.info("创建templates目录")
    
    def run_test(self):
        """运行测试"""
        logger.info("开始按钮检测测试")
        
        # 创建模板目录
        self.create_template_directory()
        
        # 查找Cursor窗口
        if not self.find_cursor_window():
            return False
        
        # 截取面板
        logger.info("正在截取Augment面板...")
        if not self.capture_and_save_panel():
            return False
        
        # 分析截图
        self.analyze_saved_image()
        
        # 测试模板匹配（如果模板文件存在）
        templates = [
            "templates/send_button_normal.png",
            "templates/send_button_pause.png"
        ]
        
        for template_path in templates:
            if os.path.exists(template_path):
                logger.info(f"测试模板: {template_path}")
                self.test_template_matching(template_path)
            else:
                logger.info(f"模板文件不存在，跳过测试: {template_path}")
        
        logger.info("测试完成")
        return True

def main():
    """主函数"""
    test = ButtonDetectionTest()
    
    print("=== Cursor Augment Code 按钮检测测试 ===")
    print("这个脚本将帮助你:")
    print("1. 截取Cursor的Augment面板")
    print("2. 分析截图以确定按钮位置")
    print("3. 测试模板匹配效果")
    print()
    
    try:
        test.run_test()
    except KeyboardInterrupt:
        logger.info("用户中断测试")
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
