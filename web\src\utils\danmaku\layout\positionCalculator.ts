/**
 * 弹幕位置计算工具函数
 * 提供弹幕布局相关的位置计算功能
 */

/**
 * 获取弹幕右侧到屏幕右侧的距离
 * 用于计算弹幕在屏幕中的相对位置，判断弹幕是否完全移出屏幕
 *
 * @param el 弹幕元素
 * @param dmContainer 弹幕容器元素
 * @returns 弹幕右侧到容器右侧的距离（像素）
 * @example
 * ```typescript
 * const distance = getDanmuRightPosition(danmuElement, containerElement)
 * if (distance > 0) {
 *   console.log('弹幕已完全移出屏幕')
 * }
 * ```
 */
export function getDanmuRightPosition(el: HTMLDivElement, dmContainer: HTMLDivElement) {
  const eleWidth = el.offsetWidth || parseInt(el.style.width)
  const eleRight =
    el.getBoundingClientRect().right || dmContainer.getBoundingClientRect().right + eleWidth
  return dmContainer.getBoundingClientRect().right - eleRight
}
