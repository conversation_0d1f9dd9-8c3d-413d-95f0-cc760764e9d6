package com.shenmo.wen.common.objectstorage.response;

import lombok.Getter;
import org.springframework.lang.NonNull;

/**
 * 上传对象响应
 *
 * <AUTHOR>
 */
@Getter
public class UploadObjectResponse extends GenericResponse<Object> {

    /**
     * 桶名
     */
    private final String bucket;

    /**
     * 对象
     */
    private final String object;

    /**
     * 构造方法
     *
     * @param origin 源对象
     * @param bucket 桶名
     * @param object 对象
     * <AUTHOR>
     */
    public UploadObjectResponse(@NonNull Object origin, String bucket, String object) {
        super(origin);
        this.bucket = bucket;
        this.object = object;
    }
}
