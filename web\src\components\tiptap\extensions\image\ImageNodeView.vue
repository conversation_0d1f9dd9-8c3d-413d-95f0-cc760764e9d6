<template>
  <NodeViewWrapper
    ref="imageWrapper"
    class="image-wrapper"
    :class="{
      'readonly-image': !isEditable,
      'ProseMirror-selectednode': isSelected && isEditable,
      resizing: isResizing,
    }"
    :style="wrapperStyles"
    @click="handleWrapperClick"
  >
    <!-- 图片元素 -->
    <ImageElement
      ref="imageElementRef"
      :src="node.attrs.src"
      :alt="node.attrs.alt"
      :width="node.attrs.width"
      :height="node.attrs.height"
      :is-resizing="isResizing"
      :resize-width="resizeWidth"
      :resize-height="resizeHeight"
      :use-thumbnail="useThumbnail"
      @double-click="handleImageDoubleClick"
      @click="handleImageClick"
      @load="handleImageLoad"
      @error="handleImageError"
    />

    <!-- 调整大小控制点 -->
    <ImageResizeHandles
      :is-editable="isEditable"
      :is-selected="isSelected"
      @resize-start="handleResizeStart"
      @touch-start="handleTouchStart"
    />

    <!-- 尺寸信息显示 -->
    <div v-if="isResizing && resizeWidth && resizeHeight" class="resize-info">
      {{ Math.round(parseFloat(resizeWidth)) }} × {{ Math.round(parseFloat(resizeHeight)) }}
    </div>
  </NodeViewWrapper>
</template>

<script setup lang="ts">
import { Selection } from '@tiptap/pm/state'
import { NodeViewWrapper } from '@tiptap/vue-3'
import { computed, ref, shallowRef, onMounted, onBeforeUnmount } from 'vue'

import { openDanmuImageViewer } from '@/components/danmaku-renderer/media/imageViewer'

import ImageElement from './components/ImageElement.vue'
import ImageResizeHandles from './components/ImageResizeHandles.vue'
import { useImageResize } from './composables/useImageResizeLogic'

import type { NodeViewProps } from '@tiptap/core'
import type { Editor } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'

interface Props extends Omit<NodeViewProps, 'editor'> {
  node: ProseMirrorNode
  updateAttributes: (attrs: Record<string, unknown>) => void
  selected: boolean
  editor: Editor
  useThumbnail?: boolean
}

const props = defineProps<Props>()

// 组件引用
const imageWrapper = ref<HTMLElement>()
const imageElementRef = ref<InstanceType<typeof ImageElement>>()

// 基础状态
const isEditable = computed(() => props.editor.isEditable)
const isSelected = computed(() => props.selected)
const isResizing = shallowRef(false)

// 调整大小状态
const resizeWidth = shallowRef('')
const resizeHeight = shallowRef('')

// 使用组合式函数
const { handleResizeStart, handleTouchStart, updateResizeState } = useImageResize({
  isResizing,
  resizeWidth,
  resizeHeight,
  updateAttributes: props.updateAttributes,
  node: props.node,
})

// 图片预览函数
const openImagePreview = () => {
  const imageElement = imageElementRef.value?.imageElement
  if (imageElement && props.node.attrs.src) {
    // 使用图片预览功能
    openDanmuImageViewer(
      imageElement,
      props.node.attrs.src,
      () => {}, // 空的事件发射函数
    )
  }
}

// 图片事件处理函数
const handleImageDoubleClick = () => {
  // 编辑模式下双击预览
  if (isEditable.value) {
    openImagePreview()
  }
}

const handleImageClick = () => {
  // 非编辑模式下单击预览
  if (!isEditable.value) {
    openImagePreview()
  }
}

const handleImageLoad = () => {
  // 图片加载完成处理逻辑
}

const handleImageError = (event: Event) => {
  // 图片加载错误处理逻辑
  console.error('图片加载失败:', props.node.attrs.src, event)
}

// 计算样式
const wrapperStyles = computed(() => ({
  display: 'inline-block' as const,
  position: 'relative' as const,
  margin: '0',
  padding: '4px',
  verticalAlign: 'baseline' as const,
  lineHeight: '1',
  transform: 'translateY(0)',
  transition: 'none',
  zIndex: '1',
  willChange: 'transform' as const,
  boxSizing: 'border-box' as const,
  width: 'fit-content' as const,
  maxWidth: '100%',
  border: '0',
  userSelect: 'none' as const,
  pointerEvents: 'auto' as const,
}))

// 处理包装器点击
const handleWrapperClick = (event: MouseEvent) => {
  event.stopPropagation()
  if (!isEditable.value) return

  // 选中图片节点
  const { view } = props.editor
  if (view) {
    const pos = view.posAtDOM(imageWrapper.value!, 0)
    if (pos !== null) {
      view.dispatch(view.state.tr.setSelection(Selection.near(view.state.doc.resolve(pos))))
    }
  }
}

// 更新调整大小状态
const updateResizeStateInternal = (width: string, height: string, resizing: boolean) => {
  updateResizeState(width, height, resizing)
}

// 暴露更新方法给父组件
const updateNode = (updatedNode: ProseMirrorNode) => {
  if (updatedNode.attrs.src !== props.node.attrs.src) {
    // src 变化时会自动通过 computed 更新
  }
  if (updatedNode.attrs.width !== props.node.attrs.width) {
    const imageElement = imageElementRef.value?.imageElement
    if (imageElement) {
      imageElement.style.width = updatedNode.attrs.width || ''
    }
  }
  if (updatedNode.attrs.height !== props.node.attrs.height) {
    const imageElement = imageElementRef.value?.imageElement
    if (imageElement) {
      imageElement.style.height = updatedNode.attrs.height || ''
    }
  }
}

// 生命周期
onMounted(() => {
  // 初始化逻辑
})

onBeforeUnmount(() => {
  // 清理逻辑
})

// 暴露给父组件的方法
defineExpose({
  updateNode,
  updateResizeState: updateResizeStateInternal,
})
</script>

<style lang="scss" scoped>
@use '@/styles/tiptap/image';

.image-wrapper {
  &.readonly-image {
    cursor: pointer; // 只读模式下也显示指针，表示可以点击预览
  }

  &.resizing {
    user-select: none;
  }

  &.ProseMirror-selectednode {
    // 移除默认的 outline，使用样式文件中定义的选中效果
    outline: none !important;
  }
}
</style>
