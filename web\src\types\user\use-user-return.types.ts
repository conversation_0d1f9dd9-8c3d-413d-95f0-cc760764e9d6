import type { UserInfo } from './user-info.types'
import type { UserStats } from './user-stats.types'
import type { ComputedRef, Ref } from 'vue'

/**
 * 用户组合式函数返回值类型
 */
export interface UseUserReturn {
  /** 当前用户信息 */
  userInfo: Ref<UserInfo | null>
  /** 用户统计信息 */
  userStats: Ref<UserStats>
  /** 是否已登录 */
  isLoggedIn: ComputedRef<boolean>
  /** 用户头像URL */
  avatarUrl: ComputedRef<string>
  /** 用户等级显示 */
  levelDisplay: ComputedRef<string>
  /** 是否为高级用户 */
  isVipUser: ComputedRef<boolean>
  /** 获取用户信息 */
  getUserInfo: () => UserInfo | null
  /** 更新用户信息 */
  updateUserInfo: (info: Partial<UserInfo>) => void
  /** 更新用户统计 */
  updateUserStats: (stats: Partial<UserStats>) => void
  /** 清除用户数据 */
  clearUserData: () => void
  /** 获取用户头像URL */
  getAvatarUrl: (avatarUri?: string) => string
  /** 检查用户权限等级 */
  checkUserLevel: (requiredLevel: number) => boolean
}
