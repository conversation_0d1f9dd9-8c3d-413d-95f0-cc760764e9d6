<template>
  <div class="code-block-header">
    <!-- 语言标识 - 编辑模式下可修改 -->
    <div class="code-block-language-container">
      <!-- 编辑模式：语言选择器 -->
      <NSelect
        v-if="isEditable"
        :value="language"
        :options="languageOptions"
        size="small"
        class="code-language-select"
        :consistent-menu-width="false"
        @update:value="handleLanguageChange"
      />
      <!-- 只读模式：语言标识 -->
      <span
        v-else
        class="code-block-language"
        contenteditable="false"
        :style="{ userSelect: 'none', pointerEvents: 'none' }"
        >{{ language }}</span
      >
    </div>

    <!-- 工具栏 -->
    <div class="code-block-toolbar">
      <!-- 换行切换按钮 -->
      <button
        class="code-wrap-button"
        :class="{ active: wrapMode }"
        :title="wrapMode ? '禁用换行' : '启用换行'"
        @click="$emit('toggle-wrap')"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <polyline points="17 2 21 6 17 10"></polyline>
          <path d="M3 11V9a4 4 0 0 1 4-4h14"></path>
          <polyline points="7 22 3 18 7 14"></polyline>
          <path d="M21 13v2a4 4 0 0 1-4 4H3"></path>
        </svg>
      </button>

      <!-- 复制按钮 -->
      <button
        class="code-copy-button"
        :class="{ active: copyState.copied }"
        :title="copyState.copied ? '已复制' : '复制代码'"
        @click="$emit('copy-code')"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
          <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NSelect } from 'naive-ui'

interface CopyState {
  copied: boolean
  timer: number | null
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps<{
  language: string
  wrapMode: boolean
  copyState: CopyState
  isEditable: boolean
}>()

const emit = defineEmits<{
  'toggle-wrap': []
  'copy-code': []
  'language-change': [language: string]
}>()

// 支持的编程语言列表
const languageOptions = [
  { label: 'Plain Text', value: 'text' },
  { label: 'JavaScript', value: 'javascript' },
  { label: 'TypeScript', value: 'typescript' },
  { label: 'HTML', value: 'html' },
  { label: 'CSS', value: 'css' },
  { label: 'SCSS', value: 'scss' },
  { label: 'Vue', value: 'vue' },
  { label: 'Java', value: 'java' },
  { label: 'Python', value: 'python' },
  { label: 'C++', value: 'cpp' },
  { label: 'C', value: 'c' },
  { label: 'C#', value: 'csharp' },
  { label: 'Go', value: 'go' },
  { label: 'Rust', value: 'rust' },
  { label: 'PHP', value: 'php' },
  { label: 'Ruby', value: 'ruby' },
  { label: 'Swift', value: 'swift' },
  { label: 'Kotlin', value: 'kotlin' },
  { label: 'Dart', value: 'dart' },
  { label: 'Shell', value: 'bash' },
  { label: 'PowerShell', value: 'powershell' },
  { label: 'SQL', value: 'sql' },
  { label: 'JSON', value: 'json' },
  { label: 'XML', value: 'xml' },
  { label: 'YAML', value: 'yaml' },
  { label: 'Markdown', value: 'markdown' },
  { label: 'Dockerfile', value: 'dockerfile' },
  { label: 'Nginx', value: 'nginx' },
  { label: 'Apache', value: 'apache' },
]

// 处理语言变更
const handleLanguageChange = (newLanguage: string) => {
  emit('language-change', newLanguage)
}
</script>

<style lang="scss" scoped>
@use '@/styles/tiptap/code-block';
</style>
