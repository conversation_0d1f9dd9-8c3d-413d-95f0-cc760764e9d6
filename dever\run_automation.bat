@echo off
chcp 65001 >nul
echo ========================================
echo Cursor Augment Code 自动化脚本
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖包...
pip show pyautogui >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install pyautogui PyGetWindow opencv-python Pillow numpy pywin32
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        echo 请尝试运行 install_manual.bat 手动安装
        pause
        exit /b 1
    )
)

REM 检查模板文件
if not exist "templates\send_button_normal.png" (
    echo 警告: 缺少正常状态按钮模板 templates\send_button_normal.png
    echo 请先运行测试脚本创建模板文件
    echo.
    choice /c YN /m "是否运行测试脚本 (Y/N)"
    if errorlevel 2 goto :run_main
    python test_button_detection.py
    pause
    exit /b 0
)

if not exist "templates\send_button_pause.png" (
    echo 警告: 缺少暂停状态按钮模板 templates\send_button_pause.png
    echo 请先运行测试脚本创建模板文件
    echo.
    choice /c YN /m "是否运行测试脚本 (Y/N)"
    if errorlevel 2 goto :run_main
    python test_button_detection.py
    pause
    exit /b 0
)

:run_main
echo 启动自动化脚本...
echo.
python cursor_augment_automation.py

if errorlevel 1 (
    echo.
    echo 脚本执行失败，请检查错误信息
) else (
    echo.
    echo 脚本执行完成
)

echo.
pause
