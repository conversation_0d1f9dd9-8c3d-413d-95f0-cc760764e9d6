import {
  TextAlignRight24Filled,
  TextAlignLeft24Filled,
  TextAlignCenter24Filled,
  TextAlignJustify24Filled,
} from '@/icons'

import type { ToolbarButtonConfig } from './types'

/**
 * 对齐按钮配置
 */
export const alignButtons: ToolbarButtonConfig[] = [
  {
    icon: TextAlignLeft24Filled,
    extensionName: 'align',
    trigger: (editor) => editor?.chain().focus().setTextAlign('left').run(),
    isActive: (editor) => editor?.isActive({ textAlign: 'left' }),
    tooltip: '左对齐',
  },
  {
    icon: TextAlignCenter24Filled,
    extensionName: 'align',
    trigger: (editor) => editor?.chain().focus().setTextAlign('center').run(),
    isActive: (editor) => editor?.isActive({ textAlign: 'center' }),
    tooltip: '居中对齐',
  },
  {
    icon: TextAlignRight24Filled,
    extensionName: 'align',
    trigger: (editor) => editor?.chain().focus().setTextAlign('right').run(),
    isActive: (editor) => editor?.isActive({ textAlign: 'right' }),
    tooltip: '右对齐',
  },
  {
    icon: TextAlignJustify24Filled,
    extensionName: 'align',
    trigger: (editor) => editor?.chain().focus().setTextAlign('justify').run(),
    isActive: (editor) => editor?.isActive({ textAlign: 'justify' }),
    tooltip: '两端对齐',
  },
]
