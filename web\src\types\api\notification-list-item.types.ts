/**
 * 通知列表项类型定义
 * 定义通知列表中单个通知项的数据结构，与后端WenNotificationResp保持一致
 */

/**
 * 通知列表项类型
 * 用于通知列表中的单个通知项，匹配后端WenNotificationResp
 */
export interface NotificationListItem {
  /** 通知ID */
  id: string
  /** 用户ID */
  userId: string
  /** 文章ID */
  articleId: string
  /** 评论ID */
  commentId: string
  /** 通知内容 */
  content: string
  /** 通知类型（0: 系统, 1: 发布文章, 2: 修改文章, 3: 发布评论） */
  type: number
  /** 是否已读（0: 未读, 1: 已读） */
  isRead: number
  /** 发布者 */
  publisher: string
  /** 发布者头像 */
  publisherAvatar: string
  /** 创建时间戳 */
  ctTm: string
}
