<template>
  <div class="comment-controls-container">
    <div class="comment-reply-info">
      <NButton
        class="comment-reply-list-btn"
        v-show="showReplyListBtn && !comment.fixed"
        style="margin-left: 3%"
        text
        type="info"
        @click="$emit('showReplyList', comment)"
      >
        回复列表>
      </NButton>
      <CommentNote20Regular
        @click="$emit('handleCommentReplyClick', comment)"
        class="cursor-pointer"
        :size="20"
      />
      {{ comment.replyCount }}
    </div>
    <div class="comment-interaction-btn">
      <LikeOutlined
        :color="comment.isLike ? 'var(--blue)' : ''"
        class="cursor-pointer"
        :size="16"
        @click="$emit('interactionBtn', comment, 1)"
      />
      {{ comment.likeCount }}
      <DislikeOutlined
        :color="comment.isDislike ? 'var(--blue)' : ''"
        class="cursor-pointer"
        :size="16"
        @click="$emit('interactionBtn', comment, 0)"
      />
      {{ comment.dislikeCount }}
      <Star48Regular
        :color="comment.isFavorite ? 'var(--blue)' : ''"
        class="cursor-pointer"
        :size="18"
        @click="$emit('favoriteBtn', comment)"
      />
      {{ comment.favoriteCount }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { NButton } from 'naive-ui'

import { LikeOutlined, DislikeOutlined, CommentNote20Regular, Star48Regular } from '@/icons'
import type { Comment } from '@/types/comment/comment.types'

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps<{
  comment: Comment
  showReplyListBtn: boolean
}>()

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const emit = defineEmits<{
  (e: 'showReplyList', comment: Comment): void
  (e: 'handleCommentReplyClick', comment: Comment): void
  (e: 'interactionBtn', comment: Comment, actionType: number): void
  (e: 'favoriteBtn', comment: Comment): void
}>()
</script>

<style lang="scss" scoped>
@use '@/styles/comment/comment-info';
</style>
