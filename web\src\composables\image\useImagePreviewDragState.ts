import type { ImagePreviewDragOptions } from '@/types/image/image-preview-drag-options.types'
import type { ImagePreviewDragState } from '@/types/image/image-preview-drag-state.types'

// 默认配置常量
export const DEFAULT_DRAG_OPTIONS = {
  minScale: 0.5,
  maxScale: 3,
  scaleStep: 0.1,
} as const

/**
 * 图片预览拖拽状态管理组合式函数返回值类型
 */
export interface UseImagePreviewDragStateReturn {
  /** 拖拽状态 */
  state: ImagePreviewDragState
  /** 拖拽相关变量 */
  dragVars: {
    dragStartX: number
    dragStartY: number
    startTranslateX: number
    startTranslateY: number
    hasDragged: boolean
    dragThreshold: number
  }
  /** 缩放相关变量 */
  scaleVars: {
    isScaling: boolean
    scaleStartDistance: number
    scaleStartScale: number
    scaleCenterX: number
    scaleCenterY: number
  }
  /** 性能优化相关变量 */
  performanceVars: {
    animationFrameId: number | null
    pendingTransform: boolean
  }
  /** 配置选项 */
  options: {
    minScale: number
    maxScale: number
  }
}

/**
 * 图片预览拖拽状态管理组合式函数
 * 提供拖拽功能的状态管理
 */
export function useImagePreviewDragState(
  options: ImagePreviewDragOptions = {},
): UseImagePreviewDragStateReturn {
  const { minScale = DEFAULT_DRAG_OPTIONS.minScale, maxScale = DEFAULT_DRAG_OPTIONS.maxScale } =
    options

  // 状态变量
  const state: ImagePreviewDragState = {
    scale: 1,
    translateX: 0,
    translateY: 0,
    isDragging: false,
    isZooming: false,
  }

  // 拖拽相关变量
  const dragVars = {
    dragStartX: 0,
    dragStartY: 0,
    startTranslateX: 0,
    startTranslateY: 0,
    hasDragged: false, // 标记是否真正发生了拖拽
    dragThreshold: 5, // 拖拽阈值（像素）
  }

  // 缩放相关变量
  const scaleVars = {
    isScaling: false,
    scaleStartDistance: 0,
    scaleStartScale: 1,
    scaleCenterX: 0,
    scaleCenterY: 0,
  }

  // 性能优化相关变量
  const performanceVars = {
    animationFrameId: null as number | null,
    pendingTransform: false,
  }

  return {
    state,
    dragVars,
    scaleVars,
    performanceVars,
    options: {
      minScale,
      maxScale,
    },
  }
}
