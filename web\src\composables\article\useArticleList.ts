import { computed, type Ref } from 'vue'

import type { SearchCondition } from '@/types/search/search-condition.types'

import { useArticleListLayout, type UseArticleListLayoutReturn } from './useArticleListLayout'
import { useArticleListLoader, type UseArticleListLoaderReturn } from './useArticleListLoader'
import {
  useArticleListOperations,
  type UseArticleListOperationsReturn,
} from './useArticleListOperations'
import { useArticleListState, type UseArticleListStateReturn } from './useArticleListState'

/**
 * 文章列表组合式函数参数类型
 */
interface UseArticleListProps {
  /** 搜索条件 */
  searchCondition: Ref<SearchCondition>
}

/**
 * 文章列表组合式函数返回值类型
 */
export interface UseArticleListReturn
  extends UseArticleListStateReturn,
    UseArticleListLayoutReturn,
    UseArticleListLoaderReturn,
    UseArticleListOperationsReturn {}

/**
 * 文章列表管理组合式函数
 * 提供文章列表的加载、显示、操作等功能
 * @param props 组件属性
 */
export function useArticleList(props: UseArticleListProps): UseArticleListReturn {
  // 初始化状态管理
  const stateActions = useArticleListState()
  const {
    articleList,
    loading,
    noMore,
    cardColSpan,
    containerRef,
    currentLoadedArticlesCount,
    cardsPerRow,
  } = stateActions

  // 初始化加载器
  const articleListLength = computed(() => articleList.value.length)
  const loaderActions = useArticleListLoader(
    props.searchCondition,
    articleList,
    loading,
    noMore,
    currentLoadedArticlesCount,
    computed(() => 0), // 临时占位，将在布局初始化后更新
  )

  // 初始化布局管理
  const layoutActions = useArticleListLayout(
    cardColSpan,
    cardsPerRow,
    containerRef,
    articleListLength,
    currentLoadedArticlesCount,
    loaderActions.loadArticles,
  )

  // 更新加载器的计算属性
  const finalLoaderActions = useArticleListLoader(
    props.searchCondition,
    articleList,
    loading,
    noMore,
    currentLoadedArticlesCount,
    layoutActions.calculatedLoadSize,
  )

  // 初始化操作管理
  const operationsActions = useArticleListOperations(articleList)

  return {
    // 状态相关
    ...stateActions,
    // 布局相关
    ...layoutActions,
    // 加载相关
    ...finalLoaderActions,
    // 操作相关
    ...operationsActions,
  }
}
