


## 环境命令

``` shell
docker run --name nacos-server -e MODE=standalone -e SPRING_DATASOURCE_PLATFORM=mysql -e MYSQL_SERVICE_HOST=********** -e MYSQL_SERVICE_DB_NAME=nacos_server -e MYSQL_SERVICE_USER=root -e MYSQL_SERVICE_PASSWORD=wen@mysql -e NACOS_AUTH_ENABLE=true -e NACOS_AUTH_TOKEN="ZDE0YzAyOGU2MzQzZjUxOTdjY2JkNGI5ZDVmOGY5Mjg=" -e NACOS_AUTH_IDENTITY_KEY="ZWUyOTE1YWI3MDA3MDhmNDhmNDg1ZmZmODA0NGZlYjg=" -e NACOS_AUTH_IDENTITY_VALUE="MmE0NDI0MWFlYjNhMjVmYmUxZjY4YThkNmIxNDhiOTk=" -e MYSQL_SERVICE_DB_PARAM="characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useUnicode=true&useSSL=false&serverTimezone=Asia/Shanghai" -p 8848:8848 -p 9848:9848 -p 9849:9849 -d nacos/nacos-server:v2.4.3
docker run --name nacos-server -e MODE=standalone -e SPRING_DATASOURCE_PLATFORM=mysql -e MYSQL_SERVICE_HOST=************** -e MYSQL_SERVICE_DB_NAME=nacos_server -e MYSQL_SERVICE_USER=root -e MYSQL_SERVICE_PASSWORD=wen@mysql -e NACOS_AUTH_ENABLE=true -e NACOS_AUTH_TOKEN="ZDE0YzAyOGU2MzQzZjUxOTdjY2JkNGI5ZDVmOGY5Mjg=" -e NACOS_AUTH_IDENTITY_KEY="ZWUyOTE1YWI3MDA3MDhmNDhmNDg1ZmZmODA0NGZlYjg=" -e NACOS_AUTH_IDENTITY_VALUE="MmE0NDI0MWFlYjNhMjVmYmUxZjY4YThkNmIxNDhiOTk=" -e MYSQL_SERVICE_DB_PARAM="characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useUnicode=true&useSSL=false&serverTimezone=Asia/Shanghai" -p 8848:8848 -p 9848:9848 -p 9849:9849 -d nacos/nacos-server:v2.4.3

docker run --name minio -p 9000:9000 -p 9001:9001 --env=MINIO_ROOT_USER=minio --env=MINIO_ROOT_PASSWORD=wen@minio --env=MINIO_PROMETHEUS_AUTH_TYPE=public --volume=T:\docker\data\minio\data:/data -d minio/minio:RELEASE.2024-12-13T22-19-12Z server --console-address :9001 --address :9000 /data
docker run --name minio -p 9000:9000 -p 9001:9001 --env=MINIO_ROOT_USER=minio --env=MINIO_ROOT_PASSWORD=wen@minio --env=MINIO_PROMETHEUS_AUTH_TYPE=public --volume=/home/<USER>/data:/data -d minio/minio:RELEASE.2024-12-13T22-19-12Z server --console-address :9001 --address :9000 /data


docker run --name nacos-server -e MODE=standalone -p 8848:8848 -p 9848:9848 -p 9849:9849 -d nacos/nacos-server:v2.4.3 
# ...
```

