import { computed, watch, type Ref, type ComputedRef } from 'vue'

import logger from '@/utils/log/log'
import { activeTheme, ThemeType } from '@/utils/theme/theme'

import type { Span } from 'naive-ui/es/legacy-grid/src/interface'

// 文章卡片固定高度
const CARD_HEIGHT = 470

/**
 * 文章列表布局管理组合式函数返回值类型
 */
export interface UseArticleListLayoutReturn {
  /** 获取卡片颜色 */
  getCardColor: (id: string, index: number) => string
  /** 计算的加载大小 */
  calculatedLoadSize: ComputedRef<number>
  /** 更新列跨度 */
  updateColSpan: () => void
}

/**
 * 文章列表布局管理组合式函数
 * 提供文章列表的布局计算和颜色管理
 */
export function useArticleListLayout(
  cardColSpan: Ref<Span>,
  cardsPerRow: Ref<number>,
  containerRef: Ref<HTMLElement | null>,
  articleListLength: Ref<number>,
  currentLoadedArticlesCount: Ref<number>,
  loadArticles: (loadMore?: boolean, signal?: AbortSignal, forceLoadSize?: number) => Promise<void>,
): UseArticleListLayoutReturn {
  // 颜色配置
  const lightColors = [
    '#ffd6d6',
    '#ffe8d1',
    '#fff8c4',
    '#d5edd7',
    '#d0e8fa',
    '#ded6f2',
    '#ebcfe9',
    '#f8d4de',
  ]

  const darkColors = [
    '#8c3a3a',
    '#7d6339',
    '#75763a',
    '#366d5a',
    '#355678',
    '#534878',
    '#664766',
    '#6a4251',
  ]

  // 判断当前是否为暗色主题
  const isDarkTheme = computed(() => activeTheme.value === ThemeType.DARK)

  // 按彩虹顺序获取卡片颜色
  const getCardColor = (id: string, index: number): string => {
    const colorSet = isDarkTheme.value ? darkColors : lightColors
    return colorSet[index % colorSet.length]
  }

  // 根据窗口宽度动态调整卡片列数并返回每行卡片数
  const updateColSpan = (): void => {
    const width = window.innerWidth
    let span: Span = 24
    let newCardsPerRow = 1

    if (width >= 1680) {
      span = 6
      newCardsPerRow = 4
    } else if (width >= 1260) {
      span = 8
      newCardsPerRow = 3
    } else if (width >= 840) {
      span = 12
      newCardsPerRow = 2
    } else {
      span = 24
      newCardsPerRow = 1
    }
    cardColSpan.value = span
    // 更新 cardsPerRow ref
    cardsPerRow.value = newCardsPerRow
  }

  // 计算初始加载量（铺满屏幕）或后续加载量（根据每行卡片数优化加载策略）
  const calculatedLoadSize = computed(() => {
    const currentCardsPerRow = cardsPerRow.value // 使用响应式的 cardsPerRow
    const containerHeight = containerRef.value?.clientHeight || 0

    let size: number

    if (articleListLength.value === 0) {
      // 首次加载：计算铺满屏幕所需的文章数量
      const rowsNeeded = Math.ceil(containerHeight / CARD_HEIGHT)
      size = rowsNeeded * currentCardsPerRow
      // 确保至少加载一屏幕的内容，并是每行卡片数的倍数
      size = Math.max(currentCardsPerRow, size)
    } else {
      // 后续加载：根据每行卡片数采用不同的加载策略
      const initialLoadSize =
        Math.ceil((containerRef.value?.clientHeight || 0) / CARD_HEIGHT) * currentCardsPerRow

      if (currentCardsPerRow <= 2) {
        // 每行1-2张卡片时，加载两倍数量以提供更好的用户体验
        size = currentCardsPerRow * 2
      } else {
        // 每行3-4张卡片时，使用原来的一半策略
        size = Math.ceil(initialLoadSize / 2)
        // 确保是 currentCardsPerRow 的倍数，向上取整
        size = Math.ceil(size / currentCardsPerRow) * currentCardsPerRow
      }
      // 确保至少加载 currentCardsPerRow 数量的文章
      size = Math.max(currentCardsPerRow, size)
    }

    logger.debug(
      'Calculated Load Size:',
      size,
      'Cards Per Row:',
      currentCardsPerRow,
      'Current Loaded Articles:',
      currentLoadedArticlesCount.value,
      'Card Height:',
      CARD_HEIGHT,
    )
    return size
  })

  // 监听 cardsPerRow 变化，当视口改变导致每行卡片数变化时，如果当前文章数量不是其倍数，则加载更多文章
  watch(cardsPerRow, (newCardsPerRow, oldCardsPerRow) => {
    if (newCardsPerRow !== oldCardsPerRow) {
      const remainder = articleListLength.value % newCardsPerRow
      if (remainder !== 0) {
        const articlesToLoad = newCardsPerRow - remainder
        logger.debug(
          `视口变化，每行卡片数从 ${oldCardsPerRow} 变为 ${newCardsPerRow}，需要加载 ${articlesToLoad} 篇文章以补齐倍数。`,
        )
        // 强制加载补齐数量的文章
        loadArticles(true, undefined, articlesToLoad)
      }
    }
  })

  return {
    getCardColor,
    calculatedLoadSize,
    updateColSpan,
  }
}
