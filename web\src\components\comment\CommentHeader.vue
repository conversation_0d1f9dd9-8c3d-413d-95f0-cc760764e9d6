<template>
  <div class="comment-title-container">
    <div class="comment-header-top">
      <NBreadcrumb>
        <NBreadcrumbItem
          v-for="(b, i) in breadcrumb"
          :key="i"
          @click="handleBreadcrumbClick(i)"
          class="breadcrumb-item"
        >
          <NGradientText class="breadcrumb-text cursor-pointer">
            {{ b.publisher }}
            <NAvatar
              v-if="b.id && b.publisherAvatar"
              object-fit="cover"
              :size="22"
              round
              :src="fileApi.getResourceURL(b.publisherAvatar)"
            />
          </NGradientText>
        </NBreadcrumbItem>
      </NBreadcrumb>
      <UserInfoGroup @locationComment="$emit('locationComment')" />
    </div>
    <div class="comment-header-bottom">
      <NRadioGroup size="small" :value="modelValue" @update:value="handleSortChange">
        <NRadioButton value="0">热评</NRadioButton>
        <NRadioButton value="1">最新</NRadioButton>
        <NRadioButton value="2">回复</NRadioButton>
      </NRadioGroup>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  NBreadcrumb,
  NBreadcrumbItem,
  NGradientText,
  NAvatar,
  NRadioGroup,
  NRadioButton,
} from 'naive-ui'

import fileApi from '@/api/file'
import UserInfoGroup from '@/components/user/UserInfoGroup.vue'
import type { Comment } from '@/types/comment/comment.types'

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps<{
  breadcrumb: Comment[]
  modelValue: string
}>()

const emit = defineEmits<{
  (e: 'breadcrumbClick', index: number): void
  (e: 'locationComment'): void
  (e: 'update:modelValue', value: string): void
}>()

const handleBreadcrumbClick = (index: number) => {
  emit('breadcrumbClick', index)
}

const handleSortChange = (value: string) => {
  emit('update:modelValue', value)
}
</script>

<style lang="scss" scoped>
@use '@/styles/comment/comment-info';
</style>
