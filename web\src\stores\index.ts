import { defineStore } from 'pinia'

export const useArticleStore = defineStore('article', {
  state: () => ({
    id: '',
  }),
  actions: {
    setId(id: string) {
      this.id = id
    },
  },
  getters: {
    getId: (state) => state.id,
  },
})

export const useCommentStore = defineStore('comment', {
  state: () => ({
    id: '',
  }),
  actions: {
    setId(id: string) {
      this.id = id
    },
  },
  getters: {
    getId: (state) => state.id,
  },
})
