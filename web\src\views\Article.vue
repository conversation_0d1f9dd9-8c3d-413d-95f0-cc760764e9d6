<!--
  文章详情页面组件
  
  功能说明：
  - 显示文章的完整内容和详细信息
  - 提供文章编辑功能（仅限文章拥有者）
  - 支持文章互动操作（点赞、点踩、收藏）
  - 显示文章评论列表和评论功能
  - 提供时间格式切换功能
  
  主要业务逻辑：
  1. 文章内容展示：使用 TiptapEditor 组件以只读模式显示文章内容
  2. 权限控制：根据用户身份显示或隐藏编辑按钮
  3. 互动功能：集成点赞、点踩、收藏等社交功能
  4. 评论系统：右侧显示评论列表，支持评论互动
  5. 响应式设计：适配不同屏幕尺寸的显示效果
-->
<template>
  <div class="article-layout">
    <!-- 左侧文章 -->
    <!-- 骨架屏 -->
    <ArticleSkeleton :show="articleDetail.articleLoading.value" />
    <div v-if="!articleDetail.articleLoading.value" class="article-info-container">
      <div class="article-header">
        <div class="article-header-content-wrapper">
          <div class="article-header-content">
            <h2>{{ articleDetail.article.value.title }}</h2>
            <div class="article-tag-container">
              <NTag
                class="article-tag"
                v-for="tag in articleDetail.article.value.tags"
                :key="tag"
                type="primary"
                >{{ tag }}</NTag
              >
            </div>
          </div>
        </div>
        <div class="flex-column-start">
          <NGradientText
            type="info"
            class="display-block time-clickable"
            @click="articleTime.toggleTimeFormat('publish')"
          >
            发布时间：{{
              articleDetail.article.value.showExactPublishTime
                ? articleDetail.article.value.exactPublishedAt
                : articleDetail.article.value.publishedAt
            }}
          </NGradientText>
          <NGradientText
            type="info"
            class="display-block time-clickable"
            @click="articleTime.toggleTimeFormat('modify')"
          >
            最近修改：{{
              articleDetail.article.value.showExactModifyTime
                ? articleDetail.article.value.exactLastModified
                : articleDetail.article.value.lastModified
            }}
          </NGradientText>
          <NGradientText type="info" class="display-block">
            拥有者：{{ articleDetail.article.value.publisher }} | 等级：{{
              articleDetail.article.value.operationLevel
            }}
            | ip:
            {{ articleDetail.article.value.ipLocation }}
          </NGradientText>
        </div>
        <!-- 操作按钮区域：包含编辑、返回、互动和评论统计功能 -->
        <div class="action-buttons-container">
          <!-- 编辑和导航按钮容器 -->
          <div class="edit-button-container">
            <!-- 编辑按钮：仅文章拥有者可见 -->
            <DocumentEdit16Regular
              class="cursor-pointer"
              :size="28"
              v-if="articleDetail.article.value.isOwner"
              @click="openEditArticleDialog"
            />
            <!-- 返回首页按钮 -->
            <RollbackOutlined class="cursor-pointer" :size="28" @click="articleDetail.backHome" />
          </div>
          <!-- 文章互动按钮区域：点赞、点踩、收藏 -->
          <div class="interaction-container">
            <!-- 点赞按钮：根据用户是否已点赞显示不同颜色 -->
            <LikeOutlined
              :color="articleDetail.article.value.isLike ? 'var(--blue)' : ''"
              :size="20"
              class="cursor-pointer"
              @click="articleInteraction.interactionBtn(articleDetail.article.value.id, 1)"
            />
            {{ articleDetail.article.value.likeCount }}
            <!-- 点踩按钮：根据用户是否已点踩显示不同颜色 -->
            <DislikeOutlined
              :color="articleDetail.article.value.isDislike ? 'var(--blue)' : ''"
              :size="20"
              class="cursor-pointer"
              @click="articleInteraction.interactionBtn(articleDetail.article.value.id, 0)"
            />
            {{ articleDetail.article.value.dislikeCount }}
            <!-- 收藏按钮：根据用户是否已收藏显示不同颜色 -->
            <Star48Regular
              :color="articleDetail.article.value.isFavorite ? 'var(--blue)' : ''"
              :size="20"
              class="cursor-pointer"
              @click="articleInteraction.favoriteBtn(articleDetail.article.value.id)"
            />
            {{ articleDetail.article.value.favoriteCount }}
          </div>
          <!-- 评论数量显示区域 -->
          <div class="comment-count-container">
            <CommentOutlined :size="20" />{{ articleDetail.article.value.commentCount }}
          </div>
        </div>
      </div>
      <div class="article-content">
        <NScrollbar>
          <div style="padding-right: 1rem">
            <TiptapEditor
              v-model="articleDetail.article.value.contentObj"
              :editable="false"
              :file-bucket="ARTICLE"
              :all-extensions="true"
              :character-limit="ARTICLE_CHARACTER_LIMIT"
              :use-thumbnail="true"
            />
          </div>
        </NScrollbar>
      </div>
    </div>
    <!-- 编辑文章弹框 -->
    <ArticleModal ref="articleModalRef" @success="handleArticleSubmitSuccess" />
    <!-- 右侧评论 -->
    <CommentInfo
      ref="commentInfoRef"
      :articleId="articleDetail.getArticleId"
      @quick-reply-end="articleDetail.loadArticleDetailCount"
      @send-end="articleDetail.loadArticleDetailCount"
    />
  </div>
</template>

<script lang="ts" setup>
import { NTag, NGradientText, NScrollbar } from 'naive-ui'
import { ref } from 'vue'

// 导入组件
import ArticleModal from '@/components/article/ArticleModal.vue'
import ArticleSkeleton from '@/components/article/ArticleSkeleton.vue'
import CommentInfo from '@/components/comment/CommentInfo.vue'
import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'
// 导入 composables
import { useArticleDetail } from '@/composables/article/useArticleDetail'
import { useArticleInteraction } from '@/composables/article/useArticleInteraction'
import { useArticleTime } from '@/composables/article/useArticleTime'
// 导入常量
import { ARTICLE } from '@/constants/article/bucket.constants'
import { ARTICLE_CHARACTER_LIMIT } from '@/constants/tiptap/tiptap.constants'
import { DocumentEdit16Regular, Star48Regular } from '@/icons'
import { RollbackOutlined, LikeOutlined, DislikeOutlined, CommentOutlined } from '@/icons'
// 导入类型定义
import type { ArticleModalExpose } from '@/types/component/article-modal.types'
import type { ArticlePageEmits } from '@/types/component/article-page-emits.types'
import type { ArticlePageExpose } from '@/types/component/article-page-expose.types'
import type { ArticlePageProps } from '@/types/component/article-page-props.types'
import type { CommentInfoRef } from '@/types/component/comment-info.types'

// 定义组件 Props
withDefaults(defineProps<ArticlePageProps>(), {
  articleId: undefined,
})

// 定义组件 Emits
const emit = defineEmits<ArticlePageEmits>()

// 使用 composables 进行状态管理
const articleDetail = useArticleDetail()
const articleInteraction = useArticleInteraction(articleDetail.article)
const articleTime = useArticleTime(articleDetail.article)

// 组件引用定义
const articleModalRef = ref<ArticleModalExpose>()
const commentInfoRef = ref<CommentInfoRef>()

// 初始化文章详情数据
articleDetail.initialize()

/**
 * 打开编辑文章弹框
 * 当用户点击编辑按钮时调用此方法
 */
const openEditArticleDialog = (): void => {
  if (articleModalRef.value && articleDetail.article.value) {
    articleModalRef.value.openEditArticleDialog(articleDetail.article.value)
  }
}

/**
 * 处理文章提交成功的回调函数
 * 当文章编辑成功后，重新加载文章详情和评论列表
 */
const handleArticleSubmitSuccess = (): void => {
  // 重新加载文章详情
  articleDetail.loadArticleDetail()

  // 重新加载评论列表
  if (commentInfoRef.value) {
    commentInfoRef.value.loadCurrentCommentList()
  }

  // 触发文章编辑成功事件
  if (articleDetail.article.value) {
    emit('article-edit-success', articleDetail.article.value)
  }
}

/**
 * 重新加载文章详情
 * 暴露给父组件的方法
 */
const reloadArticleDetail = async (): Promise<void> => {
  await articleDetail.loadArticleDetail()
}

/**
 * 获取当前文章数据
 * 暴露给父组件的方法
 */
const getCurrentArticle = () => {
  return articleDetail.article.value
}

// 暴露组件方法给父组件
defineExpose<ArticlePageExpose>({
  reloadArticleDetail,
  getCurrentArticle,
})
</script>

<style lang="scss" scoped>
@use '@/styles/layout/article-view';
</style>
