#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Augment Code 自动化脚本 GUI界面
提供图形化界面来配置和运行自动化脚本
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import json
import sys
from cursor_augment_automation import CursorAugmentAutomation, load_config
from test_button_detection import ButtonDetectionTest

class AutomationGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Cursor Augment Code 自动化工具")
        self.root.geometry("800x600")
        
        # 加载配置
        self.config = load_config()
        
        # 创建界面
        self.create_widgets()
        
        # 状态变量
        self.automation = None
        self.is_running = False
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="Cursor Augment Code 自动化工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="配置设置", padding="10")
        config_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # 当前窗口任务文件路径
        ttk.Label(config_frame, text="当前窗口任务文件:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.current_tasks_file_var = tk.StringVar(value=self.config["automation"].get("current_window_tasks_file", "tasks.txt"))
        current_tasks_entry = ttk.Entry(config_frame, textvariable=self.current_tasks_file_var, width=50)
        current_tasks_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(config_frame, text="浏览",
                  command=self.browse_current_tasks_file).grid(row=0, column=2, padx=(5, 0), pady=2)

        # 新窗口任务文件路径
        ttk.Label(config_frame, text="新窗口任务文件:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.new_tasks_file_var = tk.StringVar(value=self.config["automation"].get("new_window_tasks_file", "tasks_new_window.txt"))
        new_tasks_entry = ttk.Entry(config_frame, textvariable=self.new_tasks_file_var, width=50)
        new_tasks_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(config_frame, text="浏览",
                  command=self.browse_new_tasks_file).grid(row=1, column=2, padx=(5, 0), pady=2)
        
        # 模板文件路径
        ttk.Label(config_frame, text="正常按钮模板:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.normal_template_var = tk.StringVar(value=self.config["template_matching"]["normal_button_template"])
        normal_entry = ttk.Entry(config_frame, textvariable=self.normal_template_var, width=50)
        normal_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(config_frame, text="浏览",
                  command=self.browse_normal_template).grid(row=2, column=2, padx=(5, 0), pady=2)

        ttk.Label(config_frame, text="暂停按钮模板:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.pause_template_var = tk.StringVar(value=self.config["template_matching"]["pause_button_template"])
        pause_entry = ttk.Entry(config_frame, textvariable=self.pause_template_var, width=50)
        pause_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(config_frame, text="浏览",
                  command=self.browse_pause_template).grid(row=3, column=2, padx=(5, 0), pady=2)
        
        # 匹配阈值
        ttk.Label(config_frame, text="匹配阈值:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.threshold_var = tk.DoubleVar(value=self.config["template_matching"]["match_threshold"])
        threshold_scale = ttk.Scale(config_frame, from_=0.5, to=1.0,
                                   variable=self.threshold_var, orient=tk.HORIZONTAL)
        threshold_scale.grid(row=4, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        self.threshold_label = ttk.Label(config_frame, text=f"{self.threshold_var.get():.2f}")
        self.threshold_label.grid(row=4, column=2, padx=(5, 0), pady=2)
        threshold_scale.configure(command=self.update_threshold_label)

        # 循环模式设置
        ttk.Label(config_frame, text="循环模式:").grid(row=5, column=0, sticky=tk.W, pady=2)
        self.loop_mode_var = tk.BooleanVar(value=self.config["automation"].get("loop_mode", False))
        loop_check = ttk.Checkbutton(config_frame, text="启用循环监控", variable=self.loop_mode_var)
        loop_check.grid(row=5, column=1, sticky=tk.W, padx=(5, 5), pady=2)

        ttk.Label(config_frame, text="检查间隔(秒):").grid(row=6, column=0, sticky=tk.W, pady=2)
        self.interval_var = tk.IntVar(value=self.config["automation"].get("check_interval_seconds", 180))
        interval_entry = ttk.Entry(config_frame, textvariable=self.interval_var, width=10)
        interval_entry.grid(row=6, column=1, sticky=tk.W, padx=(5, 5), pady=2)

        # 新窗口设置
        ttk.Label(config_frame, text="新窗口模式:").grid(row=7, column=0, sticky=tk.W, pady=2)
        self.new_window_var = tk.BooleanVar(value=self.config["automation"].get("enable_new_window", True))
        new_window_check = ttk.Checkbutton(config_frame, text="开启新窗口 (Ctrl+L)", variable=self.new_window_var)
        new_window_check.grid(row=7, column=1, sticky=tk.W, padx=(5, 5), pady=2)

        # 新窗口阈值设置
        ttk.Label(config_frame, text="新窗口阈值:").grid(row=8, column=0, sticky=tk.W, pady=2)
        self.new_window_threshold_var = tk.IntVar(value=self.config["automation"].get("new_window_threshold", 3))
        threshold_frame = ttk.Frame(config_frame)
        threshold_frame.grid(row=8, column=1, sticky=tk.W, padx=(5, 5), pady=2)
        threshold_entry = ttk.Entry(threshold_frame, textvariable=self.new_window_threshold_var, width=10)
        threshold_entry.pack(side=tk.LEFT)
        ttk.Label(threshold_frame, text="次发送后开启新窗口").pack(side=tk.LEFT, padx=(5, 0))

        # Cursor窗口选择
        ttk.Label(config_frame, text="Cursor窗口:").grid(row=9, column=0, sticky=tk.W, pady=2)
        cursor_frame = ttk.Frame(config_frame)
        cursor_frame.grid(row=9, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)

        self.cursor_windows_var = tk.StringVar(value="点击刷新查看可用窗口")
        self.cursor_combo = ttk.Combobox(cursor_frame, textvariable=self.cursor_windows_var,
                                        state="readonly", width=40)
        self.cursor_combo.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Button(cursor_frame, text="刷新",
                  command=self.refresh_cursor_windows).pack(side=tk.LEFT, padx=(5, 0))

        # 操作按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        # 测试按钮
        ttk.Button(button_frame, text="测试按钮检测", 
                  command=self.run_button_test).pack(side=tk.LEFT, padx=(0, 10))
        
        # 保存配置按钮
        ttk.Button(button_frame, text="保存配置", 
                  command=self.save_config).pack(side=tk.LEFT, padx=(0, 10))
        
        # 运行自动化按钮
        self.run_button = ttk.Button(button_frame, text="运行自动化", 
                                    command=self.run_automation)
        self.run_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 停止按钮
        self.stop_button = ttk.Button(button_frame, text="停止", 
                                     command=self.stop_automation, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清除日志按钮
        ttk.Button(log_frame, text="清除日志",
                  command=self.clear_log).grid(row=1, column=0, pady=(5, 0))

        # 初始化时刷新Cursor窗口列表
        self.refresh_cursor_windows()
    
    def update_threshold_label(self, value):
        """更新阈值标签"""
        self.threshold_label.config(text=f"{float(value):.2f}")
    

    
    def browse_normal_template(self):
        """浏览正常按钮模板"""
        filename = filedialog.askopenfilename(
            title="选择正常按钮模板",
            filetypes=[("图片文件", "*.png *.jpg *.jpeg"), ("所有文件", "*.*")]
        )
        if filename:
            self.normal_template_var.set(filename)
    
    def browse_pause_template(self):
        """浏览暂停按钮模板"""
        filename = filedialog.askopenfilename(
            title="选择暂停按钮模板",
            filetypes=[("图片文件", "*.png *.jpg *.jpeg"), ("所有文件", "*.*")]
        )
        if filename:
            self.pause_template_var.set(filename)

    def browse_current_tasks_file(self):
        """浏览当前窗口任务文件"""
        filename = filedialog.askopenfilename(
            title="选择当前窗口任务文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            self.current_tasks_file_var.set(filename)

    def browse_new_tasks_file(self):
        """浏览新窗口任务文件"""
        filename = filedialog.askopenfilename(
            title="选择新窗口任务文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            self.new_tasks_file_var.set(filename)

    def refresh_cursor_windows(self):
        """刷新Cursor窗口列表"""
        try:
            from cursor_augment_automation import CursorAugmentAutomation
            automation = CursorAugmentAutomation(self.config)
            cursor_windows = automation.find_cursor_windows()

            if cursor_windows:
                # 创建窗口选项列表
                window_options = []
                for i, window in enumerate(cursor_windows):
                    window_options.append(f"[{i}] {window.title}")

                # 更新下拉框
                self.cursor_combo['values'] = window_options

                # 设置当前选择
                selected_index = self.config["automation"].get("selected_cursor_window", 0)
                if 0 <= selected_index < len(window_options):
                    self.cursor_combo.current(selected_index)
                    self.cursor_windows_var.set(window_options[selected_index])
                else:
                    self.cursor_combo.current(0)
                    self.cursor_windows_var.set(window_options[0])

                self.log(f"找到 {len(cursor_windows)} 个Cursor窗口")
            else:
                self.cursor_combo['values'] = ["未找到Cursor窗口"]
                self.cursor_windows_var.set("未找到Cursor窗口")
                self.log("未找到Cursor窗口")

        except Exception as e:
            self.log(f"刷新窗口列表失败: {e}")
            self.cursor_combo['values'] = ["刷新失败"]
            self.cursor_windows_var.set("刷新失败")
    
    def save_config(self):
        """保存配置"""
        try:
            # 更新配置
            self.config["automation"]["tasks_file"] = self.current_tasks_file_var.get()  # 使用当前窗口任务文件作为默认
            self.config["automation"]["current_window_tasks_file"] = self.current_tasks_file_var.get()
            self.config["automation"]["new_window_tasks_file"] = self.new_tasks_file_var.get()
            self.config["template_matching"]["normal_button_template"] = self.normal_template_var.get()
            self.config["template_matching"]["pause_button_template"] = self.pause_template_var.get()
            self.config["template_matching"]["match_threshold"] = self.threshold_var.get()
            self.config["automation"]["loop_mode"] = self.loop_mode_var.get()
            self.config["automation"]["check_interval_seconds"] = self.interval_var.get()
            self.config["automation"]["enable_new_window"] = self.new_window_var.get()
            self.config["automation"]["new_window_threshold"] = self.new_window_threshold_var.get()

            # 保存选择的Cursor窗口索引
            selected_text = self.cursor_windows_var.get()
            if selected_text.startswith("[") and "]" in selected_text:
                try:
                    selected_index = int(selected_text.split("]")[0][1:])
                    self.config["automation"]["selected_cursor_window"] = selected_index
                except (ValueError, IndexError):
                    self.config["automation"]["selected_cursor_window"] = 0
            else:
                self.config["automation"]["selected_cursor_window"] = 0

            # 保存到文件
            with open("config.json", 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)

            self.log("配置已保存")
            messagebox.showinfo("成功", "配置已保存到 config.json")
        except Exception as e:
            self.log(f"保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def run_button_test(self):
        """运行按钮检测测试"""
        def test_thread():
            try:
                self.log("开始按钮检测测试...")
                test = ButtonDetectionTest()
                success = test.run_test()
                if success:
                    self.log("按钮检测测试完成")
                else:
                    self.log("按钮检测测试失败")
            except Exception as e:
                self.log(f"测试过程中出错: {e}")
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def run_automation(self):
        """运行自动化脚本"""
        if self.is_running:
            return
        
        def automation_thread():
            try:
                self.is_running = True
                self.run_button.config(state=tk.DISABLED)
                self.stop_button.config(state=tk.NORMAL)
                
                self.log("开始运行自动化脚本...")
                
                # 更新配置
                self.config["automation"]["tasks_file"] = self.current_tasks_file_var.get()  # 使用当前窗口任务文件作为默认
                self.config["automation"]["current_window_tasks_file"] = self.current_tasks_file_var.get()
                self.config["automation"]["new_window_tasks_file"] = self.new_tasks_file_var.get()
                self.config["template_matching"]["normal_button_template"] = self.normal_template_var.get()
                self.config["template_matching"]["pause_button_template"] = self.pause_template_var.get()
                self.config["template_matching"]["match_threshold"] = self.threshold_var.get()
                self.config["automation"]["loop_mode"] = self.loop_mode_var.get()
                self.config["automation"]["check_interval_seconds"] = self.interval_var.get()
                self.config["automation"]["enable_new_window"] = self.new_window_var.get()
                self.config["automation"]["new_window_threshold"] = self.new_window_threshold_var.get()

                # 保存选择的Cursor窗口索引
                selected_text = self.cursor_windows_var.get()
                if selected_text.startswith("[") and "]" in selected_text:
                    try:
                        selected_index = int(selected_text.split("]")[0][1:])
                        self.config["automation"]["selected_cursor_window"] = selected_index
                    except (ValueError, IndexError):
                        self.config["automation"]["selected_cursor_window"] = 0
                else:
                    self.config["automation"]["selected_cursor_window"] = 0
                
                # 创建自动化实例
                self.automation = CursorAugmentAutomation(self.config)
                
                # 运行自动化
                success = self.automation.run()
                
                if success:
                    self.log("自动化脚本执行成功")
                else:
                    self.log("自动化脚本执行失败")
                    
            except Exception as e:
                self.log(f"自动化脚本执行出错: {e}")
            finally:
                self.is_running = False
                self.run_button.config(state=tk.NORMAL)
                self.stop_button.config(state=tk.DISABLED)
        
        threading.Thread(target=automation_thread, daemon=True).start()
    
    def stop_automation(self):
        """停止自动化脚本"""
        self.is_running = False
        self.log("正在停止自动化脚本...")
    
    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)
    
    def log(self, message):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        # 在主线程中更新UI
        self.root.after(0, lambda: self._update_log(log_message))
    
    def _update_log(self, message):
        """更新日志文本框"""
        self.log_text.insert(tk.END, message)
        self.log_text.see(tk.END)

def main():
    """主函数"""
    root = tk.Tk()
    app = AutomationGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
