/**
 * 编辑器实例类型定义
 */

import type { JsonValue } from '@/types/json/json-value-base.types'

/**
 * 编辑器命令接口
 * 定义编辑器可执行的操作命令
 */
export interface EditorCommands {
  /** 清空编辑器内容 */
  clearContent: () => void
  /** 设置编辑器内容 */
  setContent: (content: unknown) => void
  /** 聚焦编辑器 */
  focus: () => void
  /** 其他编辑器命令 */
  [key: string]: unknown
}

/**
 * 编辑器实例接口
 * 定义编辑器实例的属性和方法
 */
export interface EditorInstance {
  /** 编辑器命令对象 */
  commands: EditorCommands
  /** 编辑器是否为空 */
  isEmpty: boolean
  /** 获取编辑器JSON格式内容 */
  getJSON?: () => JsonValue
}
