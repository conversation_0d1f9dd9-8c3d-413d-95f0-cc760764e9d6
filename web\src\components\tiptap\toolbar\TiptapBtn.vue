<template>
  <NTooltip
    trigger="hover"
    :theme-overrides="{
      padding: '4px 8px',
      textColor: '#333',
      color: '#fff',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
    }"
  >
    <template #trigger>
      <div class="tiptap-btn-wrapper">
        <NButton
          v-if="show"
          class="padding-4"
          quaternary
          size="small"
          @click="(e) => trigger(e)"
          :class="{ 'is-active': isActive }"
          :disabled="disabled"
        >
          <component :is="icon" :size="size" />
          <slot name="content"> </slot>
        </NButton>
      </div>
    </template>
    {{ tooltip }}
  </NTooltip>
</template>
<script lang="ts" setup>
import { NButton, NTooltip } from 'naive-ui'

import type { PropType } from 'vue'
defineProps({
  show: {
    type: Boolean,
    default: true,
  },
  trigger: {
    type: Function as PropType<(e: MouseEvent) => void>,
    default: () => {},
  },
  icon: {
    type: Object,
    required: true,
  },
  size: {
    type: String,
    default: '20',
  },
  isActive: {
    type: Boolean,
    default: false,
  },
  tooltip: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})
</script>

<style lang="scss" scoped>
@use '@/styles/tiptap/tiptap-editor';
</style>
