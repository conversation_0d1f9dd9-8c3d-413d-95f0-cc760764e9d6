import { createApp, h } from 'vue'

import type { CustomDanmu } from '@/types/danmaku/custom-danmu.types'

import type { VNode } from 'vue'

/**
 * 弹幕插槽属性接口
 */
interface DanmakuSlotProps {
  danmu: CustomDanmu
  index: number
}

/**
 * 弹幕插槽接口
 */
interface DanmakuSlots {
  dm?: (props: DanmakuSlotProps) => VNode
}

/**
 * 弹幕插槽组件操作
 */
export function useDanmakuSlotComponent() {
  /**
   * 创建插槽组件
   * @param danmu 弹幕数据
   * @param index 弹幕索引
   * @param slots 插槽对象
   * @returns 创建的组件实例
   */
  function getSlotComponent(danmu: CustomDanmu, index: number, slots: DanmakuSlots) {
    const DmComponent = createApp({
      render() {
        return h('div', {}, [
          slots.dm &&
            slots.dm({
              danmu,
              index,
            }),
        ])
      },
    })

    const ele = DmComponent.mount(document.createElement('div'))
    return ele
  }

  return {
    getSlotComponent,
  }
}
