package com.shenmo.wen.app.authentication.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.shenmo.wen.app.authentication.enums.EmailCodeType;
import com.shenmo.wen.app.authentication.validation.LoginValidationGroups;
import com.shenmo.wen.app.authentication.config.properties.CloudflareTurnstileProperties;
import com.shenmo.wen.app.authentication.config.properties.UserConfigProperties;
import com.shenmo.wen.app.authentication.exception.AuthenticationException;
import com.shenmo.wen.app.authentication.exception.AuthenticationExceptionEnum;
import com.shenmo.wen.app.authentication.pojo.cloudflare.CloudflareTurnstileResponse;
import com.shenmo.wen.app.authentication.pojo.req.LoginReq;
import com.shenmo.wen.app.authentication.pojo.req.RegisterReq;
import com.shenmo.wen.app.authentication.pojo.req.ResetPasswordReq;
import com.shenmo.wen.app.authentication.service.WenEmailVerificationService;
import com.shenmo.wen.app.authentication.service.WenAuthenticationService;
import com.shenmo.wen.common.util.AssertUtils;
import com.shenmo.wen.common.util.IpUtils;
import com.shenmo.wen.modules.user.mapper.WenUserMapper;
import com.shenmo.wen.modules.user.pojo.entity.WenUser;
import com.shenmo.wen.modules.user.pojo.resp.WenUserResp;

import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import java.util.Set;

/**
 * 认证服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WenAuthenticationServiceImpl implements WenAuthenticationService {
    private final WenUserMapper mapper;
    private final PasswordEncoder passwordEncoder;
    private final UserConfigProperties userConfigProperties;
    private final CloudflareTurnstileProperties cloudflareTurnstileProperties;
    private final RestTemplate restTemplate;
    private final WenEmailVerificationService emailVerificationService;
    private final Validator validator;

    @Override
    public WenUserResp login(LoginReq req) {
        // 验证Turnstile
        verifyTurnstile(req.getCftt());

        // 根据登录类型进行参数验证
        validateLoginReq(req);

        // 执行登录逻辑
        WenUser user = null;
        if (req.isPhoneLogin()) {
            // 手机号密码登录
            user = handlePhoneLogin(req);
        } else if (req.isEmailLogin()) {
            // 邮箱验证码登录
            user = handleEmailLogin(req);
        } else {
            throw new AuthenticationException(AuthenticationExceptionEnum.PHONE_OR_PASSWORD_MISTAKE);
        }
        final String ip = IpUtils.getIp();
        final Long id = user.getId();
        user.setIp(ip);
        mapper.updateIpById(id, ip);
        final WenUserResp userResp = new WenUserResp();
        BeanUtils.copyProperties(user, userResp);
        userResp.setIpLocation(IpUtils.getIpLocation(ip));
        StpUtil.login(id);
        return userResp;
    }

    @Override
    public WenUserResp register(RegisterReq req) {
        verifyTurnstile(req.getCftt());

        // 验证邮箱验证码
        if (!emailVerificationService.verifyCode(req.getEmail(), req.getEmailCode(),
                EmailCodeType.REGISTER.getCode())) {
            throw new AuthenticationException(AuthenticationExceptionEnum.EMAIL_CODE_MISTAKE);
        }

        final String phone = req.getPhone();
        final String email = req.getEmail();
        final String username = req.getUsername();

        // 检查手机号是否已存在
        AssertUtils.isTrue(mapper.countByPhone(phone) == 0, AuthenticationExceptionEnum.PHONE_EXISTS);
        // 检查用户名是否已存在
        AssertUtils.isTrue(mapper.countByUsername(username) == 0, AuthenticationExceptionEnum.USERNAME_EXISTS);
        // 检查邮箱是否已存在
        AssertUtils.isTrue(mapper.countByEmail(email) == 0, AuthenticationExceptionEnum.EMAIL_EXISTS);

        final WenUser user = new WenUser();
        user.setUsername(username);
        user.setPhone(phone);
        user.setEmail(email);
        final String password = req.getPassword();
        user.setPassword(passwordEncoder.encode(password));
        user.setJob(req.getJob());
        user.setAvatar(userConfigProperties.getAvatar());
        final String ip = IpUtils.getIp();
        user.setIp(ip);
        mapper.insert(user);
        final WenUserResp userResp = new WenUserResp();
        BeanUtils.copyProperties(user, userResp);
        userResp.setIpLocation(IpUtils.getIpLocation(ip));
        StpUtil.login(user.getId());
        return userResp;
    }

    @Override
    public void logout() {
        StpUtil.logout();
    }

    @Override
    public void resetPassword(ResetPasswordReq req) {
        // 验证 Turnstile
        verifyTurnstile(req.getCftt());

        // 验证邮箱验证码
        if (!emailVerificationService.verifyCode(req.getEmail(), req.getEmailCode(),
                EmailCodeType.FORGOT.getCode())) {
            throw new AuthenticationException(AuthenticationExceptionEnum.EMAIL_CODE_MISTAKE);
        }

        // 查找用户
        final WenUser user = mapper.byEmail(req.getEmail());
        if (user == null || user.getId() == null) {
            throw new AuthenticationException(AuthenticationExceptionEnum.PHONE_OR_PASSWORD_MISTAKE);
        }

        // 更新密码
        final String encodedPassword = passwordEncoder.encode(req.getNewPassword());
        mapper.update(Wrappers.<WenUser>lambdaUpdate()
                .eq(WenUser::getId, user.getId())
                .set(WenUser::getPassword, encodedPassword));

        log.info("用户 {} 重置密码成功", req.getEmail());
    }

    /**
     * 验证登录请求
     */
    private void validateLoginReq(LoginReq req) {
        Set<ConstraintViolation<LoginReq>> violations;

        if (req.isPhoneLogin()) {
            // 验证手机号登录参数
            violations = validator.validate(req, LoginValidationGroups.PhoneLogin.class);
        } else if (req.isEmailLogin()) {
            // 验证邮箱登录参数
            violations = validator.validate(req, LoginValidationGroups.EmailLogin.class);
        } else {
            throw new AuthenticationException(AuthenticationExceptionEnum.PHONE_OR_PASSWORD_MISTAKE);
        }

        if (!violations.isEmpty()) {
            // 参数验证失败，记录日志并抛出异常
            log.warn("登录参数验证失败: {}", violations.iterator().next().getMessage());
            throw new AuthenticationException(AuthenticationExceptionEnum.PHONE_OR_PASSWORD_MISTAKE);
        }
    }

    /**
     * 处理手机号登录
     */
    private WenUser handlePhoneLogin(LoginReq req) {
        final String phone = req.getPhone();
        final String password = req.getPassword();

        final WenUser user = mapper.byPhone(phone);
        if (user == null) {
            throw new AuthenticationException(AuthenticationExceptionEnum.PHONE_NOT_EXISTS);
        }

        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw new AuthenticationException(AuthenticationExceptionEnum.PHONE_OR_PASSWORD_MISTAKE);
        }

        return user;
    }

    /**
     * 处理邮箱登录
     */
    private WenUser handleEmailLogin(LoginReq req) {
        final String email = req.getEmail();
        final String emailCode = req.getEmailCode();

        // 验证邮箱验证码
        if (!emailVerificationService.verifyCode(email, emailCode, EmailCodeType.LOGIN.getCode())) {
            throw new AuthenticationException(AuthenticationExceptionEnum.EMAIL_CODE_MISTAKE);
        }

        final WenUser user = mapper.byEmail(email);
        if (user == null) {
            throw new AuthenticationException(AuthenticationExceptionEnum.EMAIL_NOT_EXISTS);
        }

        return user;
    }

    private void verifyTurnstile(String cftt) {
        if (!cloudflareTurnstileProperties.getEnabled()) {
            return;
        }
        AssertUtils.isTrue(StringUtils.isNotBlank(cftt), AuthenticationExceptionEnum.CF_TURNSTILE_NOT_BLANK);
        final String url = cloudflareTurnstileProperties.getUrl();
        final String secret = cloudflareTurnstileProperties.getSecret();

        // 构造参数
        MultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>();
        requestParams.add("secret", secret);
        requestParams.add("response", cftt);

        log.info("cloudflare turnstile requestParams: {}", requestParams);
        // 设置Content-Type
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(requestParams, headers);

        // 发送请求
        CloudflareTurnstileResponse response = restTemplate.postForObject(url, request,
                CloudflareTurnstileResponse.class);

        // 判断success字段
        boolean success = response != null && response.getSuccess();
        AssertUtils.isTrue(success, AuthenticationExceptionEnum.CF_TURNSTILE_MISTAKE);
    }
}
