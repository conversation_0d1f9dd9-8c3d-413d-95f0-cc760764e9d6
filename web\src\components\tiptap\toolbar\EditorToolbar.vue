<template>
  <div :class="toolbarClass">
    <!-- 文本格式按钮组 -->
    <ToolbarButtonGroup
      :buttons="allButtonConfigs.textFormat"
      :editor="editor"
      :extensions-set="extensionsSet"
      :show-modal="showModal"
      :modal="modal"
      @image-upload="$emit('image-upload')"
      @toggle-fullscreen="toggleFullscreen"
    />

    <!-- 标题按钮组 -->
    <ToolbarButtonGroup
      :buttons="allButtonConfigs.heading"
      :editor="editor"
      :extensions-set="extensionsSet"
      :show-modal="showModal"
      :modal="modal"
      @image-upload="$emit('image-upload')"
      @toggle-fullscreen="toggleFullscreen"
    />

    <!-- 列表按钮组 -->
    <ToolbarButtonGroup
      :buttons="allButtonConfigs.list"
      :editor="editor"
      :extensions-set="extensionsSet"
      :show-modal="showModal"
      :modal="modal"
      @image-upload="$emit('image-upload')"
      @toggle-fullscreen="toggleFullscreen"
    />

    <!-- 引用和代码块 -->
    <ToolbarButtonGroup
      :buttons="[allButtonConfigs.other[0], allButtonConfigs.other[1]]"
      :editor="editor"
      :extensions-set="extensionsSet"
      :show-modal="showModal"
      :modal="modal"
      @image-upload="$emit('image-upload')"
      @toggle-fullscreen="toggleFullscreen"
    />

    <!-- 颜色选择器 -->
    <ColorPicker
      :show="extensionsSet.has('color')"
      type="toolbar"
      :editor="editor"
      colorType="color"
      tooltip="文字颜色"
    />
    <ColorPicker
      :show="extensionsSet.has('backgroundColor')"
      type="toolbar"
      :editor="editor"
      colorType="backgroundColor"
      tooltip="背景色"
    />

    <!-- 其他功能按钮 -->
    <ToolbarButtonGroup
      :buttons="allButtonConfigs.other.slice(2)"
      :editor="editor"
      :extensions-set="extensionsSet"
      :show-modal="showModal"
      :modal="modal"
      @image-upload="$emit('image-upload')"
      @toggle-fullscreen="toggleFullscreen"
    />

    <!-- 格式刷 -->
    <FormatPainterBtn :show="extensionsSet.has('formatPainter')" :editor="editor" />

    <!-- 对齐按钮组 -->
    <ToolbarButtonGroup
      :buttons="allButtonConfigs.align"
      :editor="editor"
      :extensions-set="extensionsSet"
      :show-modal="showModal"
      :modal="modal"
      @image-upload="$emit('image-upload')"
      @toggle-fullscreen="toggleFullscreen"
    />

    <!-- 全屏按钮 -->
    <TiptapBtn
      :icon="isFullscreen ? ResizeSmall20Filled : FullScreenMaximize16Filled"
      :show="extensionsSet.has('fullscreen')"
      :trigger="toggleFullscreen"
      :is-active="isFullscreen"
      :tooltip="isFullscreen ? '退出全屏' : '全屏'"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

import ColorPicker from '@/components/tiptap/extensions/color/ColorPicker.vue'
import FormatPainterBtn from '@/components/tiptap/extensions/format-painter/FormatPainterBtn.vue'
import TiptapBtn from '@/components/tiptap/toolbar/TiptapBtn.vue'
import { FullScreenMaximize16Filled, ResizeSmall20Filled } from '@/icons'
import type { EditorWithFormatPainter } from '@/types/tiptap/editor-with-format-painter.types'

import ToolbarButtonGroup from './components/ToolbarButtonGroup.vue'
import { allButtonConfigs } from './configs/toolbarButtons'

// eslint-disable-next-line @typescript-eslint/no-unused-vars
import type { Node, Mark } from '@tiptap/pm/model'
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import type { Editor } from '@tiptap/vue-3'

interface Props {
  editor: EditorWithFormatPainter
  extensionsSet: Set<string>
  toolbarClass?: string | object | Array<string | object>
  externalFullscreenState?: boolean
  modal?: {
    inputValue: string
    inputTitle: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  toolbarClass: 'editor-toolbar',
  externalFullscreenState: undefined,
  modal: () => ({
    inputValue: '',
    inputTitle: '',
  }),
})

const emit = defineEmits(['image-upload', 'show-modal', 'toggle-fullscreen'])

const modal = ref(props.modal)

const isFullscreen = ref(false)

watch(
  () => props.externalFullscreenState,
  (newValue) => {
    if (newValue !== undefined) {
      isFullscreen.value = newValue
    }
  },
)

const showModal = (title: string, trigger: () => void, onlyInputValue = false) => {
  emit('show-modal', { title, trigger, onlyInputValue })
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  emit('toggle-fullscreen', isFullscreen.value)
}
</script>
