/**
 * 图片预览拖拽辅助函数
 * 提供拖拽功能所需的计算和工具函数
 */

/**
 * 计算两点间距离
 * @param touch1 第一个触摸点
 * @param touch2 第二个触摸点
 * @returns 两点间的距离
 */
export function getDistance(touch1: Touch, touch2: Touch): number {
  const dx = touch1.clientX - touch2.clientX
  const dy = touch1.clientY - touch2.clientY
  return Math.sqrt(dx * dx + dy * dy)
}

/**
 * 计算两点的中心点
 * @param touch1 第一个触摸点
 * @param touch2 第二个触摸点
 * @returns 中心点坐标
 */
export function getCenter(touch1: Touch, touch2: Touch): { x: number; y: number } {
  return {
    x: (touch1.clientX + touch2.clientX) / 2,
    y: (touch1.clientY + touch2.clientY) / 2,
  }
}

/**
 * 限制数值在指定范围内
 * @param value 要限制的值
 * @param min 最小值
 * @param max 最大值
 * @returns 限制后的值
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max)
}

/**
 * 计算缩放后的边界限制
 * @param imageElement 图片元素
 * @param scale 当前缩放比例
 * @returns 边界限制对象
 */
export function calculateBounds(
  imageElement: HTMLImageElement,
  scale: number,
): { maxX: number; maxY: number; minX: number; minY: number } {
  const rect = imageElement.getBoundingClientRect()
  const scaledWidth = rect.width * scale
  const scaledHeight = rect.height * scale
  const containerWidth = window.innerWidth
  const containerHeight = window.innerHeight

  const maxX = Math.max(0, (scaledWidth - containerWidth) / 2)
  const maxY = Math.max(0, (scaledHeight - containerHeight) / 2)

  return {
    maxX,
    maxY,
    minX: -maxX,
    minY: -maxY,
  }
}
