package com.shenmo.wen.app.authentication.enums;

/**
 * 邮箱验证码类型枚举
 * 
 * <AUTHOR>
 */
public enum EmailCodeType {
    
    /**
     * 登录验证码
     */
    LOGIN("login", "登录"),
    
    /**
     * 注册验证码
     */
    REGISTER("register", "注册"),
    
    /**
     * 忘记密码验证码
     */
    FORGOT("forgot", "忘记密码");
    
    private final String code;
    private final String description;
    
    EmailCodeType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 代码
     * @return 枚举值
     */
    public static EmailCodeType fromCode(String code) {
        for (EmailCodeType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的验证码类型: " + code);
    }
    
    /**
     * 验证代码是否有效
     * 
     * @param code 代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        try {
            fromCode(code);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
