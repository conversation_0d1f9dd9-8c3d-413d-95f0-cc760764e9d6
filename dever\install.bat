@echo off
chcp 65001 >nul
echo ========================================
echo Cursor Augment Code 自动化工具安装程序
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限
) else (
    echo 注意: 建议以管理员身份运行以避免权限问题
)

echo.

REM 检查Python是否安装
echo [1/4] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python
    echo.
    echo 请先安装Python 3.7或更高版本:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo ✅ Python版本: %PYTHON_VERSION%
)

REM 检查pip
echo [2/4] 检查pip包管理器...
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到pip
    echo 请确保pip已正确安装
    pause
    exit /b 1
) else (
    echo ✅ pip可用
)

REM 创建必要目录
echo [3/4] 创建目录结构...
if not exist "templates" (
    mkdir templates
    echo ✅ 创建templates目录
) else (
    echo ✅ templates目录已存在
)

REM 安装依赖包
echo [4/4] 安装Python依赖包...
echo 这可能需要几分钟时间...

REM 尝试直接安装
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 标准安装失败，尝试备用方法...
    echo.
    echo 尝试逐个安装依赖包...
    pip install pyautogui==0.9.54
    pip install PyGetWindow==0.0.9
    pip install opencv-python==********
    pip install Pillow==10.0.1
    pip install numpy==1.24.3
    pip install pywin32==306

    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        echo.
        echo 请尝试使用国内镜像:
        echo pip install pyautogui PyGetWindow opencv-python Pillow numpy pywin32 -i https://pypi.tuna.tsinghua.edu.cn/simple/
        echo.
        pause
        exit /b 1
    ) else (
        echo ✅ 依赖包安装完成
    )
) else (
    echo ✅ 依赖包安装完成
)

echo.
echo ========================================
echo 🎉 安装完成！
echo ========================================
echo.
echo 下一步操作:
echo 1. 双击运行 run_gui.bat 启动图形界面
echo 2. 或运行 run_automation.bat 使用命令行模式
echo 3. 首次使用请先阅读 QUICK_START.md
echo.
echo 重要提醒:
echo - 使用前需要创建按钮模板图片
echo - 确保Cursor编辑器已安装并运行
echo - 建议先运行测试功能验证环境
echo.
pause
