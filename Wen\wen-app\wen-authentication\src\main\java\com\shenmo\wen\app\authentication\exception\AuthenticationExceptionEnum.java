package com.shenmo.wen.app.authentication.exception;

import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnumOption;
import com.shenmo.wen.common.exception.enumeration.ExceptionType;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 */
@ExceptionType(type = AuthenticationException.class, module = AuthenticationExceptionEnum.MODULE)
public enum AuthenticationExceptionEnum implements ExceptionEnum {

    /**
     * 手机号或密码错误
     */
    PHONE_OR_PASSWORD_MISTAKE(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "不对不对，手机号跟密码不对")),

    /**
     * 手机号已存在
     */
    PHONE_EXISTS(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "这个手机号已经有了")),

    /**
     * 手机号不存在
     */
    PHONE_NOT_EXISTS(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "没有这个手机号，或许可以去注册一个？")),

    /**
     * 用户名已存在
     */
    USERNAME_EXISTS(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "换个用户名吧，别人用了")),

    /**
     * 邮箱已存在
     */
    EMAIL_EXISTS(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "换个邮箱吧，别人用了")),

    /**
     * 邮箱不存在
     */
    EMAIL_NOT_EXISTS(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "没有这个邮箱，快去注册吧~")),

    /**
     * 验证码不能为空
     */
    CF_TURNSTILE_NOT_BLANK(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "验证码哪去了呢")),

    /**
     * 验证码错误
     */
    CF_TURNSTILE_MISTAKE(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "验证码好像不太对，稍后再试吧~")),

    /**
     * 邮箱验证码错误
     */
    EMAIL_CODE_MISTAKE(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "邮箱验证码不对，在去看看呢~")),

    /**
     * 验证码发送过于频繁
     */
    EMAIL_CODE_SEND_TOO_FREQUENT(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "短信CD还没好，再等等吧"));

    public static final String MODULE = "002";

    /**
     * 异常枚举选项
     */
    private final ExceptionEnumOption exceptionEnumOption;

    AuthenticationExceptionEnum(ExceptionEnumOption exceptionEnumOption) {

        this.exceptionEnumOption = exceptionEnumOption;
    }

    @Override
    public ExceptionEnumOption getOption() {

        return exceptionEnumOption;
    }
}
