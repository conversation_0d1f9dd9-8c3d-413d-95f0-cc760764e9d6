#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Augment Code 窗口自动化脚本
自动检测Cursor中的Augment Code窗口，根据发送按钮状态执行相应操作
"""

import time
import pyautogui
import pygetwindow as gw
import cv2
import numpy as np
from PIL import Image, ImageGrab
import os
import sys
import logging
import json
import pyperclip

# 默认配置
DEFAULT_CONFIG = {
    "window_detection": {
        "cursor_window_titles": ["Cursor", "cursor"],
        "activation_delay": 1.0
    },
    "panel_capture": {
        "panel_width_ratio": 0.33,
        "capture_delay": 0.5
    },
    "template_matching": {
        "normal_button_template": "templates/send_button_normal.png",
        "pause_button_template": "templates/send_button_pause.png",
        "match_threshold": 0.8
    },
    "automation": {
        "tasks_file": "tasks.txt",
        "current_window_tasks_file": "tasks.txt",
        "new_window_tasks_file": "tasks_new_window.txt",
        "new_conversation_hotkey": ["ctrl", "l"],
        "paste_hotkey": ["ctrl", "v"],
        "send_key": "enter",
        "input_area_offset_from_bottom": 100,
        "loop_mode": True,
        "check_interval_seconds": 180,
        "enable_new_window": True,
        "new_window_threshold": 3,
        "send_count": 0,
        "selected_cursor_window": 0,
        "operation_delays": {
            "after_new_conversation": 2.0,
            "after_paste": 1.0,
            "between_operations": 0.5
        }
    },
    "logging": {
        "level": "INFO",
        "format": "%(asctime)s - %(levelname)s - %(message)s"
    },
    "safety": {
        "enable_failsafe": True,
        "pause_between_actions": 0.5
    }
}

def load_config(config_file="config.json"):
    """加载配置文件"""
    try:
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            # 合并默认配置
            def merge_config(default, user):
                for key, value in default.items():
                    if key not in user:
                        user[key] = value
                    elif isinstance(value, dict) and isinstance(user[key], dict):
                        merge_config(value, user[key])
            merge_config(DEFAULT_CONFIG, config)
            return config
        else:
            return DEFAULT_CONFIG
    except Exception as e:
        print(f"加载配置文件失败，使用默认配置: {e}")
        return DEFAULT_CONFIG

# 加载配置
config = load_config()

# 配置日志
log_level = getattr(logging, config["logging"]["level"].upper(), logging.INFO)
logging.basicConfig(level=log_level, format=config["logging"]["format"])
logger = logging.getLogger(__name__)

class CursorAugmentAutomation:
    def __init__(self, config=None):
        self.config = config or load_config()
        self.cursor_window = None

        # 从配置文件读取参数
        self.tasks_file_path = self.config["automation"]["tasks_file"]
        self.send_button_normal_template = self.config["template_matching"]["normal_button_template"]
        self.send_button_pause_template = self.config["template_matching"]["pause_button_template"]
        self.match_threshold = self.config["template_matching"]["match_threshold"]

        # 新窗口相关参数
        self.send_count = self.config["automation"].get("send_count", 0)
        self.new_window_threshold = self.config["automation"].get("new_window_threshold", 3)
        self.current_window_tasks_file = self.config["automation"].get("current_window_tasks_file", "tasks.txt")
        self.new_window_tasks_file = self.config["automation"].get("new_window_tasks_file", "tasks_new_window.txt")
        self.is_new_window_mode = False

        # 设置pyautogui参数
        pyautogui.FAILSAFE = self.config["safety"]["enable_failsafe"]
        pyautogui.PAUSE = self.config["safety"]["pause_between_actions"]
        
    def find_cursor_windows(self):
        """查找所有Cursor窗口"""
        try:
            # 获取所有窗口
            all_windows = gw.getAllWindows()

            # 过滤出可能的Cursor窗口，排除我们的自动化工具
            cursor_windows = []
            for window in all_windows:
                title_lower = window.title.lower()
                if ("cursor" in title_lower and
                    "自动化工具" not in window.title and
                    "automation" not in title_lower and
                    window.title.strip() != ""):
                    cursor_windows.append(window)

            if not cursor_windows:
                logger.error("未找到Cursor编辑器窗口")
                logger.info("当前所有窗口:")
                for w in all_windows:
                    if w.title.strip():
                        logger.info(f"  - {w.title}")
                return []

            # 按标题长度排序（通常较短的是主编辑器窗口）
            cursor_windows.sort(key=lambda w: len(w.title))

            logger.info(f"找到 {len(cursor_windows)} 个Cursor窗口:")
            for i, w in enumerate(cursor_windows):
                logger.info(f"  [{i}] {w.title}")

            return cursor_windows
        except Exception as e:
            logger.error(f"查找Cursor窗口时出错: {e}")
            return []

    def find_cursor_window(self):
        """查找Cursor窗口（兼容性方法）"""
        cursor_windows = self.find_cursor_windows()
        if not cursor_windows:
            return False

        # 如果配置中指定了窗口索引，使用指定的窗口
        selected_index = self.config["automation"].get("selected_cursor_window", 0)
        if 0 <= selected_index < len(cursor_windows):
            self.cursor_window = cursor_windows[selected_index]
            logger.info(f"使用指定的Cursor窗口 [{selected_index}]: {self.cursor_window.title}")
        else:
            # 默认使用第一个窗口
            self.cursor_window = cursor_windows[0]
            logger.info(f"使用默认Cursor窗口: {self.cursor_window.title}")

        return True
    
    def activate_cursor_window(self):
        """激活Cursor窗口"""
        try:
            if self.cursor_window:
                self.cursor_window.activate()
                time.sleep(self.config["window_detection"]["activation_delay"])
                return True
        except Exception as e:
            logger.error(f"激活Cursor窗口时出错: {e}")
        return False
    
    def capture_augment_panel_area(self):
        """截取Augment Code面板区域"""
        try:
            if not self.cursor_window:
                return None

            # 获取窗口位置和大小
            left = self.cursor_window.left
            top = self.cursor_window.top
            width = self.cursor_window.width
            height = self.cursor_window.height

            # 从配置读取面板宽度比例
            panel_width_ratio = self.config["panel_capture"]["panel_width_ratio"]
            panel_width = int(width * panel_width_ratio)
            panel_area = (left, top, left + panel_width, top + height)

            time.sleep(self.config["panel_capture"]["capture_delay"])
            screenshot = ImageGrab.grab(bbox=panel_area)
            return np.array(screenshot)
        except Exception as e:
            logger.error(f"截取面板区域时出错: {e}")
            return None
    
    def find_button_in_image(self, image, template_path):
        """在图像中查找按钮"""
        try:
            if not os.path.exists(template_path):
                logger.warning(f"模板文件不存在: {template_path}")
                return None

            template = cv2.imread(template_path, cv2.IMREAD_COLOR)
            if template is None:
                logger.error(f"无法加载模板图片: {template_path}")
                return None

            # 转换为OpenCV格式
            image_cv = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)

            # 模板匹配
            result = cv2.matchTemplate(image_cv, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            if max_val >= self.match_threshold:
                logger.debug(f"模板匹配成功: {template_path}, 匹配度: {max_val:.4f}")
                return max_loc
            else:
                logger.debug(f"模板匹配失败: {template_path}, 匹配度: {max_val:.4f}")
            return None
        except Exception as e:
            logger.error(f"查找按钮时出错: {e}")
            return None
    
    def check_send_button_status(self):
        """检查发送按钮状态"""
        try:
            panel_image = self.capture_augment_panel_area()
            if panel_image is None:
                return None
            
            # 检查是否为暂停状态
            pause_button_pos = self.find_button_in_image(panel_image, self.send_button_pause_template)
            if pause_button_pos:
                logger.info("检测到暂停状态的发送按钮")
                return "pause"
            
            # 检查是否为正常状态
            normal_button_pos = self.find_button_in_image(panel_image, self.send_button_normal_template)
            if normal_button_pos:
                logger.info("检测到正常状态的发送按钮")
                return "normal"
            
            logger.warning("未能识别发送按钮状态")
            return None
        except Exception as e:
            logger.error(f"检查按钮状态时出错: {e}")
            return None
    
    def read_tasks_content(self):
        """读取任务文件内容，根据窗口类型选择不同的文件"""
        try:
            # 根据是否为新窗口模式选择任务文件
            if self.is_new_window_mode:
                tasks_file = self.new_window_tasks_file
                logger.info(f"使用新窗口任务文件: {tasks_file}")
            else:
                tasks_file = self.current_window_tasks_file
                logger.info(f"使用当前窗口任务文件: {tasks_file}")

            if not os.path.exists(tasks_file):
                logger.error(f"任务文件不存在: {tasks_file}")
                return None

            with open(tasks_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            if not content:
                logger.warning(f"任务文件为空: {tasks_file}")
                return None

            logger.info(f"成功读取任务文件，内容长度: {len(content)} 字符")
            return content
        except Exception as e:
            logger.error(f"读取任务文件时出错: {e}")
            return None
    
    def create_new_conversation(self):
        """创建新对话"""
        try:
            # 先点击Augment Code输入区域确保焦点正确
            logger.info("点击Augment Code输入区域以确保焦点正确")
            if not self.click_augment_input_area():
                logger.error("无法点击Augment Code输入区域")
                return False

            # 等待一下确保焦点切换
            time.sleep(0.5)

            # 执行创建新对话的快捷键
            hotkey = self.config["automation"]["new_conversation_hotkey"]
            logger.info(f"执行 {'+'.join(hotkey)} 创建新对话")
            pyautogui.hotkey(*hotkey)
            delay = self.config["automation"]["operation_delays"]["after_new_conversation"]
            time.sleep(delay)
            return True
        except Exception as e:
            logger.error(f"创建新对话时出错: {e}")
            return False

    def click_augment_input_area(self):
        """点击Augment Code面板的输入区域"""
        try:
            if not self.cursor_window:
                return False

            # 获取窗口位置和大小
            left = self.cursor_window.left
            top = self.cursor_window.top
            width = self.cursor_window.width
            height = self.cursor_window.height

            # 计算Augment面板的输入区域位置
            panel_width_ratio = self.config["panel_capture"]["panel_width_ratio"]
            panel_width = int(width * panel_width_ratio)

            # 输入区域通常在面板底部，点击面板底部中央位置
            input_x = left + panel_width // 2
            offset_from_bottom = self.config["automation"]["input_area_offset_from_bottom"]
            input_y = top + height - offset_from_bottom

            logger.info(f"点击Augment输入区域: ({input_x}, {input_y})")
            pyautogui.click(input_x, input_y)
            return True
        except Exception as e:
            logger.error(f"点击Augment输入区域时出错: {e}")
            return False
    
    def send_tasks_content(self, content):
        """发送任务内容"""
        try:
            logger.info("开始发送任务内容")

            # 将内容复制到剪贴板
            pyperclip.copy(content)
            time.sleep(self.config["automation"]["operation_delays"]["between_operations"])

            # 点击Augment Code面板的输入区域以确保焦点正确
            self.click_augment_input_area()
            time.sleep(self.config["automation"]["operation_delays"]["between_operations"])

            # 粘贴内容
            paste_hotkey = self.config["automation"]["paste_hotkey"]
            pyautogui.hotkey(*paste_hotkey)
            time.sleep(self.config["automation"]["operation_delays"]["after_paste"])

            # 发送消息
            send_key = self.config["automation"]["send_key"]
            pyautogui.press(send_key)
            logger.info("任务内容发送完成")
            return True
        except Exception as e:
            logger.error(f"发送任务内容时出错: {e}")
            return False
    
    def create_template_directory(self):
        """创建模板目录"""
        template_dir = "templates"
        if not os.path.exists(template_dir):
            os.makedirs(template_dir)
            logger.info(f"创建模板目录: {template_dir}")

    def save_send_count(self):
        """保存发送计数到配置文件"""
        try:
            self.config["automation"]["send_count"] = self.send_count
            with open("config.json", 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            logger.debug(f"发送计数已保存: {self.send_count}")
        except Exception as e:
            logger.error(f"保存发送计数失败: {e}")

    def should_use_new_window(self):
        """判断是否应该使用新窗口"""
        enable_new_window = self.config["automation"].get("enable_new_window", False)
        if not enable_new_window:
            return False

        # 检查是否超过阈值
        return self.send_count >= self.new_window_threshold
    
    def run_once(self):
        """执行一次检查和自动化操作"""
        # 查找并激活Cursor窗口
        if not self.find_cursor_window():
            return False

        if not self.activate_cursor_window():
            return False

        # 检查发送按钮状态
        button_status = self.check_send_button_status()
        if button_status is None:
            logger.error("无法检测按钮状态")
            return False

        # 检查按钮状态并执行相应操作
        enable_new_window = self.config["automation"].get("enable_new_window", False)

        if button_status == "pause":
            # 暂停按钮：系统正在处理，等待不发送
            logger.info("发送按钮为暂停状态，系统正在处理中，跳过操作")
            return False

        elif button_status == "normal":
            # 正常按钮：系统空闲，可以发送内容
            logger.info("发送按钮为正常状态，系统空闲，开始发送任务")

            # 检查是否需要开启新窗口（只有达到阈值才开启）
            if enable_new_window and self.send_count >= self.new_window_threshold:
                logger.info(f"发送计数({self.send_count})已达到阈值({self.new_window_threshold})，开启新窗口发送新任务")

                # 创建新窗口
                if not self.create_new_conversation():
                    return False

                # 使用新窗口任务文件
                self.is_new_window_mode = True
                tasks_content = self.read_tasks_content()
                if tasks_content is None:
                    return False

                # 发送新窗口任务内容
                if not self.send_tasks_content(tasks_content):
                    return False

                # 重置计数
                self.send_count = 0
                logger.info("新窗口任务发送完成，计数重置为0")
            else:
                # 未达到阈值或未开启新窗口模式，在当前窗口发送
                if enable_new_window:
                    logger.info(f"开启新窗口模式但未达到阈值({self.send_count}/{self.new_window_threshold})，在当前窗口发送任务")
                else:
                    logger.info("未开启新窗口模式，在当前窗口发送任务")

                self.is_new_window_mode = False
                tasks_content = self.read_tasks_content()
                if tasks_content is None:
                    return False

                # 发送任务内容
                if not self.send_tasks_content(tasks_content):
                    return False

                # 增加发送计数
                self.send_count += 1
                logger.info(f"任务发送完成，计数更新为: {self.send_count}")

            # 保存计数
            self.save_send_count()
        else:
            logger.error("无法识别按钮状态")
            return False

        logger.info("自动化操作完成")
        return True

    def run(self):
        """主运行函数"""
        logger.info("开始运行Cursor Augment自动化脚本")

        # 创建模板目录
        self.create_template_directory()

        # 检查模板文件是否存在
        if not os.path.exists(self.send_button_normal_template) or not os.path.exists(self.send_button_pause_template):
            logger.error("模板文件不存在，请先截取发送按钮的正常状态和暂停状态图片")
            logger.error(f"需要的文件: {self.send_button_normal_template}, {self.send_button_pause_template}")
            return False

        # 检查是否启用循环模式
        loop_mode = self.config["automation"].get("loop_mode", False)

        if loop_mode:
            logger.info("启用循环监控模式")
            check_interval = self.config["automation"].get("check_interval_seconds", 60)
            logger.info(f"检查间隔: {check_interval} 秒")
            logger.info("按 Ctrl+C 停止监控")

            try:
                while True:
                    import datetime
                    current_time = datetime.datetime.now().strftime("%H:%M:%S")
                    logger.info(f"[{current_time}] 开始检查按钮状态...")

                    success = self.run_once()
                    if success:
                        logger.info("自动化操作执行成功，等待下次检查...")

                    logger.info(f"等待 {check_interval} 秒后进行下次检查...")
                    time.sleep(check_interval)

            except KeyboardInterrupt:
                logger.info("用户中断，停止循环监控")
                return True
            except Exception as e:
                logger.error(f"循环监控过程中出错: {e}")
                return False
        else:
            logger.info("单次执行模式")
            return self.run_once()

def main():
    """主函数"""
    try:
        # 加载配置
        config = load_config()
        automation = CursorAugmentAutomation(config)

        success = automation.run()
        if success:
            logger.info("脚本执行成功")
        else:
            logger.error("脚本执行失败")
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("用户中断脚本执行")
    except Exception as e:
        logger.error(f"脚本执行过程中出现未预期的错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
