import { darkTheme } from 'naive-ui'
import { ref } from 'vue'

import { ThemeType } from '@/types/theme/theme.types'
import localStorage from '@/utils/storage/local-storage'
import { getThemeColors, applyColorsToDOM } from '@/utils/theme/theme-colors'

// 重新导出 ThemeType 以保持向后兼容性
export { ThemeType }

// 主题存储键名
export const THEME_KEY = 'wen-theme'

// 主题名称映射
export const THEME_NAMES = {
  [ThemeType.LIGHT]: '浅色模式',
  [ThemeType.DARK]: '暗色模式',
}

// 主题对象映射
export const THEME_OBJECTS = {
  [ThemeType.LIGHT]: null, // null 代表默认的浅色主题
  [ThemeType.DARK]: darkTheme,
}

// 当前活动主题
export const activeTheme = ref<ThemeType>(ThemeType.LIGHT)

// 主题切换中状态标记
export const isThemeChanging = ref<boolean>(false)

// 加载保存的主题 - CSS变量版本
export const loadSavedTheme = () => {
  const savedTheme = localStorage.get(THEME_KEY)
  if (savedTheme && Object.values(ThemeType).includes(savedTheme as ThemeType)) {
    activeTheme.value = savedTheme as ThemeType
    applyThemeVars(savedTheme as ThemeType)
  }
}

// 切换主题 - 高性能混合版本
export const toggleTheme = () => {
  const newTheme = activeTheme.value === ThemeType.LIGHT ? ThemeType.DARK : ThemeType.LIGHT

  // 设置主题切换状态
  isThemeChanging.value = true

  // 设置状态
  activeTheme.value = newTheme
  localStorage.set(THEME_KEY, newTheme)

  // 立即应用主题 - 优先更新重要组件然后再更新其他部分
  applyThemeVars(newTheme)

  // 优先更新特定组件
  prioritizeComponents(newTheme)

  // 短暂延迟后重置状态
  setTimeout(() => {
    isThemeChanging.value = false
  }, 50) // 减少延迟时间

  return newTheme
}

// 应用主题到文档 - 使用统一的颜色管理
const applyThemeVars = (theme: ThemeType) => {
  const root = document.documentElement
  const isDark = theme === ThemeType.DARK

  // 使用统一的颜色管理系统
  const colors = getThemeColors(isDark)
  applyColorsToDOM(colors)

  // 设置data-theme属性
  root.setAttribute('data-theme', theme)

  // 同时设置类名，保持与现有样式的兼容性
  if (isDark) {
    root.classList.add('dark-theme')
  } else {
    root.classList.remove('dark-theme')
  }
}

// 为特定组件优先应用主题
const prioritizeComponents = (_theme: ThemeType) => {
  // 选择需要优先更新的组件
  const prioritySelectors = [
    '.search-container',
    '.n-input',
    '.user-info-group',
    '.user-info',
    '.avatar-container',
    '.n-popover',
    '.notification-container',
    // 评论相关组件
    '.comment-info-container',
    '.comment-list-container',
    '.user-comment-container',
    '.user-comment-container-fixed',
    '.comment-content-row',
    '.comment-input-row',
    '.comment-reply-row',
    '.tiptap-editor-wrapper',
    '.editor-content',
    '.ProseMirror',
    '.ProseMirrorInput',
    '.article-content',
  ]

  // 强制刷新特定元素
  const forceRefreshSpecificElements = () => {
    // 评论编辑器特殊处理
    document
      .querySelectorAll('.ProseMirror, .editor-content, .tiptap-editor-wrapper')
      .forEach((el) => {
        if (el instanceof HTMLElement) {
          // 添加特定属性标记需要立即刷新的组件
          el.setAttribute('data-theme-refresh', 'true')
          // 触发重绘
          requestAnimationFrame(() => {
            void el.offsetHeight
            // 应用后移除标记
            setTimeout(() => el.removeAttribute('data-theme-refresh'), 50)
          })
        }
      })
  }

  // 1. 标记所有优先组件
  prioritySelectors.forEach((selector) => {
    const elements = document.querySelectorAll(selector)
    elements.forEach((el) => {
      if (el instanceof HTMLElement) {
        // 标记为优先级组件
        el.classList.add('theme-priority')
      }
    })
  })

  // 2. 触发编辑器的特殊刷新
  forceRefreshSpecificElements()

  // 3. 直接对评论容器执行一次DOM强制重绘
  const commentContainers = document.querySelectorAll(
    '.comment-info-container, .article-info-container',
  )
  commentContainers.forEach((container) => {
    if (container instanceof HTMLElement) {
      // 使用更彻底的方式刷新关键容器
      const oldDisplay = container.style.display
      container.style.display = 'none'
      void container.offsetHeight // 触发重排
      container.style.display = oldDisplay
    }
  })
}

export default {
  activeTheme,
  loadSavedTheme,
  toggleTheme,
  THEME_NAMES,
  isThemeChanging,
}
