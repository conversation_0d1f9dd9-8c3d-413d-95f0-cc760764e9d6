<template>
  <!-- 调整大小控制点 -->
  <template v-if="isEditable && isSelected">
    <!-- 角控制点 -->
    <div
      v-for="position in cornerHandles"
      :key="position"
      :class="['resize-handle', `handle-${position}`]"
      :data-handle-position="position"
      :data-center-handle="false"
      :style="getHandleStyle(position)"
      @mousedown="(e) => $emit('resizeStart', e, position)"
      @touchstart="(e) => $emit('touchStart', e, position)"
    ></div>

    <!-- 边控制点 -->
    <div
      v-for="position in edgeHandles"
      :key="position"
      :class="['resize-handle', `handle-${position}`]"
      :data-handle-position="position"
      :data-center-handle="true"
      :style="getHandleStyle(position)"
      @mousedown="(e) => $emit('resizeStart', e, position)"
      @touchstart="(e) => $emit('touchStart', e, position)"
    ></div>
  </template>
</template>

<script setup lang="ts">
import { readonly } from 'vue'

interface Props {
  isEditable: boolean
  isSelected: boolean
}

const props = defineProps<Props>()

defineEmits<{
  resizeStart: [event: MouseEvent, position: string]
  touchStart: [event: TouchEvent, position: string]
}>()

// 控制点定义
const cornerHandles = readonly(['top-left', 'top-right', 'bottom-left', 'bottom-right'])
const edgeHandles = readonly(['top', 'right', 'bottom', 'left'])

// 获取控制点光标样式
const getCursorForHandle = (position: string): string => {
  const cursorMap: Record<string, string> = {
    'top-left': 'nw-resize',
    'top-right': 'ne-resize',
    'bottom-left': 'sw-resize',
    'bottom-right': 'se-resize',
    top: 'n-resize',
    bottom: 's-resize',
    left: 'w-resize',
    right: 'e-resize',
  }
  return cursorMap[position] || 'default'
}

// 计算控制点样式
const getHandleStyle = (position: string) => {
  const baseStyle = {
    position: 'absolute' as const,
    background: '#2d8cf0',
    border: '1px solid white',
    borderRadius: '50%',
    zIndex: '100',
    display: props.isSelected && props.isEditable ? 'block' : 'none',
    visibility: (props.isSelected && props.isEditable ? 'visible' : 'hidden') as
      | 'visible'
      | 'hidden',
    opacity: props.isSelected && props.isEditable ? '1' : '0',
  }

  // 角控制点样式
  if (cornerHandles.includes(position)) {
    const cornerStyles = {
      width: '8px',
      height: '8px',
      cursor: getCursorForHandle(position),
    }

    const positionStyles: Record<string, Record<string, string>> = {
      'top-left': { top: '-1px', left: '-1px' },
      'top-right': { top: '-1px', right: '-1px' },
      'bottom-left': { bottom: '-1px', left: '-1px' },
      'bottom-right': { bottom: '-1px', right: '-1px' },
    }

    return { ...baseStyle, ...cornerStyles, ...positionStyles[position] }
  }
  // 边控制点样式
  else if (edgeHandles.includes(position)) {
    const edgeStyles = {
      width: '6px',
      height: '6px',
      cursor: getCursorForHandle(position),
    }

    const positionStyles: Record<string, Record<string, string>> = {
      top: { top: '-1px', left: '50%', transform: 'translateX(-50%)' },
      bottom: { bottom: '-1px', left: '50%', transform: 'translateX(-50%)' },
      left: { left: '-1px', top: '50%', transform: 'translateY(-50%)' },
      right: { right: '-1px', top: '50%', transform: 'translateY(-50%)' },
    }

    return { ...baseStyle, ...edgeStyles, ...positionStyles[position] }
  }

  return baseStyle
}
</script>

<style lang="scss" scoped>
.resize-handle {
  transition: opacity 0.2s ease;

  &:hover {
    background: #1c6dd0;
    transform: scale(1.1);
  }
}
</style>
