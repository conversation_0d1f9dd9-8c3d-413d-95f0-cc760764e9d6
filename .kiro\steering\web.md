---
inclusion: fileMatch
fileMatchPattern: ['web/**/*']
---

# Web项目开发规范

## 目录结构规范

### 核心原则
- 所有文件按业务功能进行目录划分，避免使用通用命名（如common）
- 每个文件不超过300行，超出时拆分为多个文件
- 文件命名必须清晰表达业务含义

### 目录组织
- **组件**: `src/components/` - 按业务功能划分子目录
- **样式**: `src/styles/` - 对应组件目录结构
- **工具函数**: `src/utils/` 和 `src/components/` - 保持composables + utils双重分类
- **类型定义**: `src/types/` - 每个类型单独文件，按业务划分
- **常量**: `src/constants/` - 按业务功能划分
- **API接口**: `src/api/` - 按业务模块划分

## 代码规范

### TypeScript规范
- 禁止使用 `any`、`unknown` 类型
- 必须为所有变量、函数、组件定义明确类型
- 发现未定义类型时，创建对应的类型文件

### 组件规范
- 所有组件使用TSX格式，禁止使用h函数
- 组件文件以 `.tsx` 结尾
- 组件命名使用PascalCase

## 重构流程规范

### 执行顺序
1. 逐个文件检查和修改
2. 每次修改后运行 `npm run code-check` 验证
3. 严格解决所有检查问题，不允许忽略

### 重构原则
- 保持原有业务逻辑不变
- 移除所有弃用和无用代码
- 不考虑历史包袱，彻底重构
- 完成后无需输出总结或测试内容