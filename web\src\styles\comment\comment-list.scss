/*
 * CommentList 组件样式
 * 评论列表组件的样式定义，包括滚动容器和响应式设计
 */

.comment-list-container {
  flex: 1;
  overflow-y: auto;
  position: relative;
  padding: 1.25rem 0 1.25rem 1.25rem;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex布局正常工作 */
  height: 100%; /* 确保容器占满可用高度 */
  transition: max-height 0.35s cubic-bezier(0.25, 0.8, 0.25, 1); /* 平滑的高度过渡 */
  box-sizing: border-box; /* 确保padding不会影响总高度 */

  .comment-scroll {
    flex: 1;
    padding-right: 1.25rem;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 确保flex布局正常工作 */
    height: 100%; /* 确保滚动区域占满容器高度 */

    .comment-list-footer {
      padding: 1.25rem 0;
      display: flex;
      justify-content: center;

      // margin-bottom: 20px;
      flex-shrink: 0; /* 防止footer被压缩 */
    }
  }
}

// 评论列表项动画
.smooth-move,
.smooth-enter-active,
.smooth-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.smooth-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.smooth-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.98);
}

.smooth-leave-active {
  position: absolute;
  width: 100%;
}

// 为新评论添加特殊的进入动画
.comment-list-container .user-comment-container:first-child {
  animation: new-comment-highlight 0.6s ease-out;
}

@keyframes new-comment-highlight {
  0% {
    transform: scale(1.02);
  }

  50% {
    transform: scale(1.01);
  }

  100% {
    transform: scale(1);
  }
}

// 添加响应式设计，调整窄屏下的布局
@media (width <= 768px) {
  .comment-list-container {
    height: auto; /* 改回自适应高度 */
    min-height: 70vh; /* 增加最小高度 */
    max-height: calc(100vh - 150px); /* 调整预留空间 */
    overflow-y: auto; /* 评论列表内部滚动 */
    padding-bottom: 150px; /* 增加底部间距到150px，确保不被主输入框遮挡 */
    box-sizing: border-box;
  }
}
