package com.shenmo.wen.app.core.user.pojo.entity;

import org.apache.ibatis.type.JdbcType;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shenmo.wen.common.mybatis.handler.TimestampToLongTypeHandler;

import lombok.Data;

@Data
@TableName(value = "wen_user_privilege_template", autoResultMap = true)
public class WenUserPrivilegeTemplate {

    /**
     * 特权模板ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 特权模板名称
     */
    private String name;

    /**
     * 特权模板图标
     */
    private String icon;

    /**
     * 特权模板面额
     */
    private Integer denomination;

    /**
     * 特权模板链接
     */
    private String link;

    /**
     * 特权模板描述
     */
    private String description;

    /**
     * 特权模板二维码URL
     */
    private String qrCodeUrl;

    /**
     * 创建时间
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long ctTm;

    /**
     * 修改时间
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long mdTm;
}
