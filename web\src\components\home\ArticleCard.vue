<template>
  <NCard
    class="card-item cursor-pointer"
    :class="cardClasses"
    :data-article-id="article.id"
    @click.ctrl="handleArticleClick"
    header-style="padding-bottom:0.25rem;border-bottom: var(--border-1);"
    :style="cardStyle"
  >
    <template #header>
      <div class="article-header">
        <NTooltip v-if="article.isOwner">
          <template #trigger>
            <div class="scope-icon-wrapper clickable" @click.stop="$emit('toggleScope', article)">
              <component
                :is="
                  article.publishedScope == ArticlePublishedScope.PERSONAL
                    ? LockOutlined
                    : UnlockOutlined
                "
                :size="18"
              />
            </div>
          </template>
          {{ scopeTooltipText }}
        </NTooltip>

        <div class="article-title" @click.stop="handleArticleClick">
          {{ article.title }}
        </div>
      </div>
    </template>

    <template #header-extra>
      <NAvatar
        round
        :size="45"
        :src="article.publisherAvatar"
        object-fit="cover"
        class="article-avatar"
        @mousedown.stop="handleAvatarMouseDown"
        @touchstart.stop="handleAvatarTouchStart"
        @mouseup.stop="$emit('cancelLongPress')"
        @mouseleave.stop="$emit('cancelLongPress')"
        @touchcancel.stop="$emit('cancelLongPress')"
        @contextmenu.prevent
      />
    </template>

    <div class="flex-between-center">
      <div>
        <NTag type="primary" class="card-tag" v-for="item in article.tags" :key="item">{{
          item
        }}</NTag>
      </div>
      <div>
        <DocumentDownload
          :size="24"
          class="cursor-pointer"
          @click.stop="$emit('download', article.id)"
        />
      </div>
    </div>

    <div class="article-content">
      <NScrollbar style="padding-right: 0.5rem">
        <TiptapEditor
          :ref="(el: TiptapEditorRef) => el && $emit('setEditor', article.id, el)"
          v-model="article.contentObj"
          :editable="false"
          :file-bucket="ARTICLE"
          :all-extensions="true"
          :character-limit="ARTICLE_CHARACTER_LIMIT"
        />
      </NScrollbar>
    </div>
  </NCard>
</template>

/** * 文章卡片组件 * * 功能说明： * - 展示文章基本信息（标题、标签、内容预览） * - 支持拖拽排序功能
* - 提供文章操作（下载、切换可见性） * - 支持长按头像触发用户操作 * - 点击卡片在新标签页打开文章详情
* * 使用场景： * - 首页文章列表展示 * - 文章管理界面 */
<script lang="ts" setup>
import { NCard, NTag, NAvatar, NScrollbar, NTooltip } from 'naive-ui'
import { computed } from 'vue'

import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'
import { ArticlePublishedScope } from '@/constants/article/article-published-scope.constants'
import { ARTICLE } from '@/constants/article/bucket.constants'
import { ARTICLE_CHARACTER_LIMIT } from '@/constants/tiptap/tiptap.constants'
import { DocumentDownload, LockOutlined, UnlockOutlined } from '@/icons'
import router from '@/router'
import type { ArticleCardEmits } from '@/types/component/article-card-emits.types'
import type { ArticleCardProps } from '@/types/component/article-card-props.types'
import type { TiptapEditorRef } from '@/types/component/tiptap-editor-ref.types'

// 定义组件 Props，使用明确的类型定义
const props = defineProps<ArticleCardProps>()

// 定义组件 Emits，使用明确的类型定义
const emit = defineEmits<ArticleCardEmits>()

/**
 * 计算卡片的 CSS 类名
 * 根据拖拽状态和位置动态添加相应的样式类
 */
const cardClasses = computed(() => {
  const isDraggedCard = props.isDragging && props.draggedArticle?.id === props.article.id
  const isDragOverCard = props.isDragging && props.dragOverCardId === props.article.id

  return {
    dragging: isDraggedCard, // 当前卡片正在被拖拽
    'drag-over-before':
      isDragOverCard && props.dragOverPosition === 'before' && !props.isSingleCardRow, // 水平布局时的前置拖拽指示
    'drag-over-after':
      isDragOverCard && props.dragOverPosition === 'after' && !props.isSingleCardRow, // 水平布局时的后置拖拽指示
    'drag-over-before-vertical':
      isDragOverCard && props.dragOverPosition === 'before' && props.isSingleCardRow, // 垂直布局时的前置拖拽指示
    'drag-over-after-vertical':
      isDragOverCard && props.dragOverPosition === 'after' && props.isSingleCardRow, // 垂直布局时的后置拖拽指示
  }
})

/**
 * 计算卡片的内联样式
 * 包含背景色和拖拽时的动态样式
 */
const cardStyle = computed(() => ({
  backgroundColor: props.cardColor,
  ...(props.isDragging && props.draggedArticle?.id === props.article.id ? props.dragStyle : {}),
}))

/**
 * 计算可见性切换按钮的提示文本
 * 根据文章当前可见性和用户权限显示不同的提示
 */
const scopeTooltipText = computed(() => {
  const isPersonal = props.article.publishedScope === ArticlePublishedScope.PERSONAL
  return props.article.isOwner
    ? `点击切换为${isPersonal ? '公开' : '个人'}可见`
    : `${isPersonal ? '个人' : '公开'}可见`
})

/**
 * 处理文章卡片点击事件
 * 在新标签页中打开文章详情页面并自动聚焦
 */
const handleArticleClick = () => {
  const route = router.resolve({ name: 'Article', params: { articleId: props.article.id } })
  const newWindow = window.open(route.href, '_blank')
  newWindow?.focus()
}

/**
 * 处理头像鼠标按下事件
 * 触发长按操作，用于显示用户相关操作菜单
 */
const handleAvatarMouseDown = (event: MouseEvent) => {
  emit('startLongPress', event, props.article, event.currentTarget as HTMLElement)
}

/**
 * 处理头像触摸开始事件
 * 移动端触发长按操作，用于显示用户相关操作菜单
 */
const handleAvatarTouchStart = (event: TouchEvent) => {
  emit('startLongPress', event, props.article, event.currentTarget as HTMLElement)
}
</script>

<style lang="scss" scoped>
@use '@/styles/article/article-modal';
</style>
