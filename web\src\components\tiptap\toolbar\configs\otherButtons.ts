import {
  LinkOutlined,
  Code20Filled,
  Image28Regular,
  ArrowUndo16Filled,
  ArrowRedo16Filled,
  LineHorizontal120Filled,
  VideoClip24Regular,
  FullScreenMaximize16Filled,
  ResizeSmall20Filled,
  Blockquote,
} from '@/icons'

import type { ToolbarButtonConfig } from './types'

/**
 * 其他功能按钮配置
 */
export const otherButtons: ToolbarButtonConfig[] = [
  {
    icon: Blockquote,
    extensionName: 'blockquote',
    trigger: (editor) => editor?.chain().focus().toggleBlockquote().run(),
    isActive: (editor) => editor?.isActive('blockquote'),
    tooltip: '引用',
  },
  {
    icon: Code20Filled,
    extensionName: 'codeBlockLowlight',
    trigger: (editor) => editor?.chain().focus().toggleCodeBlock().run(),
    isActive: (editor) => editor?.isActive('codeBlock'),
    tooltip: '代码块',
  },
  {
    icon: LineHorizontal120Filled,
    extensionName: 'horizontalRule',
    trigger: (editor) => editor?.chain().focus().setHorizontalRule().run(),
    tooltip: '分割线',
  },
  {
    icon: LinkOutlined,
    extensionName: 'link',
    trigger: (editor, showModal) => showModal?.('插入链接', () => {}),
    isActive: (editor) => editor?.isActive('link'),
    tooltip: '链接',
  },
  {
    icon: Image28Regular,
    extensionName: 'image',
    trigger: () => {},
    tooltip: '图片',
    emit: 'image-upload',
  },
  {
    icon: VideoClip24Regular,
    extensionName: 'bilibili',
    trigger: (editor, showModal, modal) =>
      showModal?.(
        '插入bilibili视频链接',
        () =>
          // @ts-expect-error - setBilibiliVideo is a custom command that may not be typed
          editor?.commands.setBilibiliVideo({
            src: modal?.inputValue,
          }),
        true,
      ),
    tooltip: 'B站视频',
  },
  {
    icon: ArrowUndo16Filled,
    extensionName: 'history',
    trigger: (editor) => editor?.chain().focus().undo().run(),
    tooltip: '撤销',
  },
  {
    icon: ArrowRedo16Filled,
    extensionName: 'history',
    trigger: (editor) => editor?.chain().focus().redo().run(),
    tooltip: '重做',
  },
]

/**
 * 全屏按钮配置（特殊处理）
 */
export const createFullscreenButton = (isFullscreen: boolean): ToolbarButtonConfig => ({
  icon: isFullscreen ? ResizeSmall20Filled : FullScreenMaximize16Filled,
  extensionName: 'fullscreen',
  trigger: () => {},
  isActive: () => isFullscreen,
  tooltip: isFullscreen ? '退出全屏' : '全屏',
  emit: 'toggle-fullscreen',
})
