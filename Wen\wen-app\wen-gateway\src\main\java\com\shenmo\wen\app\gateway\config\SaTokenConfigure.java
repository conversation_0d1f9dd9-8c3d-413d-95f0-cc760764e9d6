package com.shenmo.wen.app.gateway.config;

import cn.dev33.satoken.config.SaTokenConfig;
import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.fun.SaParamFunction;
import cn.dev33.satoken.reactor.context.SaReactorSyncHolder;
import cn.dev33.satoken.reactor.filter.SaReactorFilter;
import cn.dev33.satoken.router.SaHttpMethod;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.router.SaRouterStaff;
import cn.dev33.satoken.stp.StpUtil;
import com.shenmo.wen.common.constant.RedisKeyConstant;
import com.shenmo.wen.common.exception.enumeration.BaseExceptionEnum;
import com.shenmo.wen.common.pojo.response.ResponseData;
import com.shenmo.wen.common.util.JacksonUtils;
import com.shenmo.wen.common.util.spring.SpringRedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.config.GlobalCorsProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsProcessor;
import org.springframework.web.cors.reactive.DefaultCorsProcessor;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * [Sa-Token 权限认证] 配置类
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class SaTokenConfigure {

    private static final SaParamFunction<SaRouterStaff> FREE_EMPTY = spf -> {
    };
    private final GlobalCorsProperties globalCorsProperties;
    private final CorsProcessor corsProcessor = new DefaultCorsProcessor();
    private final SaTokenConfig saTokenConfig;

    /**
     * 注入 Spring Boot 自动装配的 WebClient.Builder
     * 通过 WebClientCustomizer 自动配置了负载均衡功能
     */
    private final WebClient.Builder webClientBuilder;

    @Bean
    public SaReactorFilter getSaReactorFilter() {
        return new SaReactorFilter()
                // 拦截地址
                .addInclude("/**")
                // 开放地址
                .addExclude("/favicon.ico", "/actuator/health")
                .setBeforeAuth(obj -> {
                    for (CorsConfiguration cors : globalCorsProperties.getCorsConfigurations().values()) {
                        corsProcessor.process(cors, SaReactorSyncHolder.getExchange());
                    }
                    // 如果是预检请求，则立即返回到前端
                    SaRouter.match(SaHttpMethod.OPTIONS)
                            .back();
                })
                // 鉴权方法：每次访问进入
                .setAuth(obj -> {
                    SaRouter.match(SaHttpMethod.POST).match("/authentication/sessions").free(FREE_EMPTY)
                            .match(SaHttpMethod.POST).match("/authentication/accounts").free(FREE_EMPTY)
                            .match(SaHttpMethod.PUT).match("/authentication/accounts/password").free(FREE_EMPTY)
                            .match(SaHttpMethod.POST).match("/authentication/email-codes").free(FREE_EMPTY)
                            .match("/osr/privilege/verification/**")
                            .check(r -> {
                                // 特权验证页面不需要登录，但需要触发步骤三逻辑
                                handlePrivilegeVerificationAccess();
                            })
                            // 其他所有请求需要鉴权
                            .match("/**")
                            .check(r -> {
                                try {
                                    StpUtil.checkLogin();
                                } catch (Exception e) {
                                    StpUtil.logout();
                                    final SetOperations<String, Object> set = SpringRedisUtils.forSet();
                                    set.remove(RedisKeyConstant.USER_ONLINE, StpUtil.getLoginIdAsLong());
                                }
                                StpUtil.renewTimeout(saTokenConfig.getTimeout());
                            });
                })
                // 异常处理方法：每次setAuth函数出现异常时进入
                .setError(e -> {
                    final BaseExceptionEnum tokenNotFound = BaseExceptionEnum.TOKEN_NOT_FOUND;
                    SaHolder.getContext().getResponse().setStatus(tokenNotFound.getStatus().value());
                    return JacksonUtils.toJson(ResponseData.error(tokenNotFound.getCode(), tokenNotFound.getMessage()));
                });
    }

    /**
     * 处理特权验证页面访问
     */
    private void handlePrivilegeVerificationAccess() {
        try {
            // 从请求路径中提取验证ID
            String requestPath = SaHolder.getContext().getRequest().getRequestPath();
            String verificationId = extractVerificationIdFromPath(requestPath);

            if (verificationId != null) {
                // 使用负载均衡方式异步调用core服务触发步骤三
                webClientBuilder.build().post()
                        .uri("lb://core/core/user/privilege/verification/" + verificationId + "/trigger-step-three")
                        .retrieve()
                        .bodyToMono(String.class)
                        .subscribe(
                                result -> log.info("触发特权验证步骤三成功: {}", verificationId),
                                error -> log.error("触发特权验证步骤三失败: {}", verificationId, error));
            }
        } catch (Exception e) {
            log.error("处理特权验证页面访问失败", e);
        }
    }

    /**
     * 从请求路径中提取验证ID
     */
    private String extractVerificationIdFromPath(String requestPath) {
        // 从 /privilege/verification/{verificationId} 中提取 verificationId
        if (requestPath != null && requestPath.startsWith("/privilege/verification/")) {
            String[] parts = requestPath.split("/");
            if (parts.length >= 4) {
                return parts[3]; // 获取验证ID部分
            }
        }
        return null;
    }
}
