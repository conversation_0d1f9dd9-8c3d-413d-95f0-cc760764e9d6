package com.shenmo.wen.common.objectstorage.response;

import lombok.Getter;
import org.springframework.lang.NonNull;

import java.util.List;

/**
 * 抽象的列举对象响应
 *
 * <AUTHOR>
 */
@Getter
public abstract class ListObjectResponse<O> extends GenericResponse<O> {

    /**
     * 桶名
     */
    protected final String bucket;

    /**
     * 对象前缀
     */
    protected final String prefix;

    /**
     * 构造方法
     *
     * @param origin 源对象
     * @param bucket 桶名
     * @param prefix 对象前缀
     * <AUTHOR>
     */
    public ListObjectResponse(@NonNull O origin, String bucket, String prefix) {
        super(origin);
        this.bucket = bucket;
        this.prefix = prefix;
    }

    /**
     * 获取对象数量
     *
     * @return 对象数量
     * <AUTHOR>
     */
    @NonNull
    public abstract Long getSize();

    /**
     * 获取前缀数量
     *
     * @return 前缀数量
     * <AUTHOR>
     */
    @NonNull
    public abstract Long getPrefixSize();

    /**
     * 获取对象名称列表
     *
     * @return 对象名称列表
     * <AUTHOR>
     */
    @NonNull
    public abstract List<String> getNames();

    /**
     * 获取对象前缀列表
     *
     * @return 目录对象名称列表
     * <AUTHOR>
     */
    @NonNull
    public abstract List<String> getPrefixes();

}
