package com.shenmo.wen.common.objectstorage.response;

import com.shenmo.wen.common.util.ThrowUtils;
import io.minio.Result;
import io.minio.messages.Item;
import org.springframework.lang.NonNull;

import java.util.ArrayList;
import java.util.List;

/**
 * minio列举对象响应
 *
 * <AUTHOR>
 */
public class MinioListObjectResponse extends ListObjectResponse<List<Result<Item>>> {


    /**
     * 构造方法
     *
     * @param origin 源对象
     * @param bucket 桶名
     * @param prefix 对象前缀
     * <AUTHOR>
     */
    public MinioListObjectResponse(List<Result<Item>> origin, String bucket, String prefix) {
        super(origin, bucket, prefix);
    }

    @NonNull
    @Override
    public Long getSize() {
        return (long) getNames().size();
    }

    @NonNull
    @Override
    public Long getPrefixSize() {
        return (long) getPrefixes().size();
    }

    @NonNull
    @Override
    public List<String> getNames() {

        List<String> objectNames = new ArrayList<>();
        for (Result<Item> result : origin) {
            final Item item;
            try {
                item = result.get();
                objectNames.add(item.objectName());
            } catch (Exception e) {
                throw ThrowUtils.getThrow().internalServerError(String.format("获取某个桶下指定前缀的对象名时失败: %s -> %s", bucket, prefix), e);
            }
        }
        return objectNames;
    }

    @NonNull
    @Override
    public List<String> getPrefixes() {
        List<String> objectNames = new ArrayList<>();
        for (Result<Item> result : origin) {
            final Item item;
            try {
                item = result.get();
                if (item.isDir()) {
                    objectNames.add(item.objectName());
                }
            } catch (Exception e) {
                throw ThrowUtils.getThrow().internalServerError(String.format("获取某个桶下所有前缀时失败: %s -> %s", bucket, prefix), e);
            }
        }
        return objectNames;
    }
}
