import image, { createImageExtension } from '@/components/tiptap/extensions/image/ImageExtension'
import { createVueNodeView } from '@/components/tiptap/extensions/image/ImageNodeViewVue'
import { handleImageUpload } from '@/components/tiptap/extensions/image/ImageResourceManager'
import { useImageUpload } from '@/components/tiptap/extensions/image/useImageUpload'

export { createImageExtension, handleImageUpload, createVueNodeView, useImageUpload }
export default image
