/**
 * 文章详情API响应类型定义
 * 定义文章详情接口的响应数据结构，与后端WenArticleResp保持一致
 */

import type { SearchUser } from '@/types/user/search-user.types'

/**
 * 文章详情响应数据类型
 * 直接对应后端WenArticleResp的字段结构
 */
export interface ArticleDetailResponse {
  /** 文章ID */
  id: string
  /** 文章标题 */
  title: string
  /** 文章标签 */
  tag?: string
  /** 文章内容 */
  content: string
  /** 文章发布人 */
  publisher: string
  /** 文章发布人头像 */
  publisherAvatar: string
  /** 是否文章拥有者 */
  isOwner: boolean
  /** 文章发布时的IP归属地 */
  ipLocation: string
  /** 发布时间 */
  publishedAt: string
  /** 点赞数 */
  likeCount: number
  /** 是否已点赞 */
  isLike: boolean
  /** 点踩数 */
  dislikeCount: number
  /** 是否已点踩 */
  isDislike: boolean
  /** 评论数 */
  commentCount: number
  /** 收藏数 */
  favoriteCount: number
  /** 是否已收藏 */
  isFavorite: boolean
  /** 操作权限级别 */
  operationLevel: number
  /** 发布范围 */
  publishedScope: number
  /** 最后修改时间 */
  lastModified: string
  /** 分享用户列表 */
  shareUsers?: SearchUser[]
}
