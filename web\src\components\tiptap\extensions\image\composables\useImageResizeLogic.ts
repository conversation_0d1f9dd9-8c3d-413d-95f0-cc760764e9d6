/**
 * 图片调整大小逻辑组合式函数
 */

import { ref, type Ref } from 'vue'

import type { Node as ProseMirrorNode } from '@tiptap/pm/model'

interface UseImageResizeOptions {
  isResizing: Ref<boolean>
  resizeWidth: Ref<string>
  resizeHeight: Ref<string>
  updateAttributes: (attrs: Record<string, unknown>) => void
  node: ProseMirrorNode
}

export function useImageResize(options: UseImageResizeOptions) {
  const { isResizing, resizeWidth, resizeHeight, updateAttributes, node } = options

  // 调整大小状态
  const startX = ref(0)
  const startY = ref(0)
  const startWidth = ref(0)
  const startHeight = ref(0)
  const currentHandle = ref<string | null>(null)
  const aspectRatio = ref(1)

  // 开始调整大小
  const handleResizeStart = (event: MouseEvent, position: string) => {
    event.preventDefault()
    event.stopPropagation()

    isResizing.value = true
    currentHandle.value = position
    startX.value = event.clientX
    startY.value = event.clientY

    // 获取当前尺寸
    const currentWidth = parseInt(node.attrs.width) || 200
    const currentHeight = parseInt(node.attrs.height) || 150

    startWidth.value = currentWidth
    startHeight.value = currentHeight
    aspectRatio.value = currentWidth / currentHeight

    // 添加事件监听器
    document.addEventListener('mousemove', handleMouseResize)
    document.addEventListener('mouseup', handleMouseEnd)
    document.body.style.overflow = 'hidden'
  }

  // 触摸开始
  const handleTouchStart = (event: TouchEvent, position: string) => {
    event.preventDefault()
    event.stopPropagation()

    const touch = event.touches[0]
    if (!touch) return

    isResizing.value = true
    currentHandle.value = position
    startX.value = touch.clientX
    startY.value = touch.clientY

    const currentWidth = parseInt(node.attrs.width) || 200
    const currentHeight = parseInt(node.attrs.height) || 150

    startWidth.value = currentWidth
    startHeight.value = currentHeight
    aspectRatio.value = currentWidth / currentHeight

    document.addEventListener('touchmove', handleTouchResize)
    document.addEventListener('touchend', handleTouchEnd)
    document.body.style.overflow = 'hidden'
  }

  // 鼠标调整大小
  const handleMouseResize = (event: MouseEvent) => {
    if (!isResizing.value || !currentHandle.value) return

    const deltaX = event.clientX - startX.value
    const deltaY = event.clientY - startY.value

    calculateNewSize(deltaX, deltaY)
  }

  // 触摸调整大小
  const handleTouchResize = (event: TouchEvent) => {
    if (!isResizing.value || !currentHandle.value) return

    const touch = event.touches[0]
    if (!touch) return

    const deltaX = touch.clientX - startX.value
    const deltaY = touch.clientY - startY.value

    calculateNewSize(deltaX, deltaY)
  }

  // 计算新尺寸
  const calculateNewSize = (deltaX: number, deltaY: number) => {
    if (!currentHandle.value) return

    let newWidth = startWidth.value
    let newHeight = startHeight.value

    // 根据控制点位置计算新尺寸
    switch (currentHandle.value) {
      case 'top-left':
        newWidth = startWidth.value - deltaX
        newHeight = startHeight.value - deltaY
        break
      case 'top-right':
        newWidth = startWidth.value + deltaX
        newHeight = startHeight.value - deltaY
        break
      case 'bottom-left':
        newWidth = startWidth.value - deltaX
        newHeight = startHeight.value + deltaY
        break
      case 'bottom-right':
        newWidth = startWidth.value + deltaX
        newHeight = startHeight.value + deltaY
        break
      case 'top':
        newHeight = startHeight.value - deltaY
        newWidth = newHeight * aspectRatio.value
        break
      case 'bottom':
        newHeight = startHeight.value + deltaY
        newWidth = newHeight * aspectRatio.value
        break
      case 'left':
        newWidth = startWidth.value - deltaX
        newHeight = newWidth / aspectRatio.value
        break
      case 'right':
        newWidth = startWidth.value + deltaX
        newHeight = newWidth / aspectRatio.value
        break
    }

    // 限制最小尺寸
    newWidth = Math.max(50, newWidth)
    newHeight = Math.max(50, newHeight)

    // 限制最大尺寸
    newWidth = Math.min(800, newWidth)
    newHeight = Math.min(600, newHeight)

    resizeWidth.value = `${newWidth}px`
    resizeHeight.value = `${newHeight}px`
  }

  // 结束调整大小
  const handleMouseEnd = () => {
    finishResize()
  }

  const handleTouchEnd = () => {
    finishResize()
  }

  const finishResize = () => {
    if (!isResizing.value) return

    // 更新节点属性
    if (resizeWidth.value && resizeHeight.value) {
      updateAttributes({
        width: resizeWidth.value,
        height: resizeHeight.value,
      })
    }

    // 重置状态
    isResizing.value = false
    currentHandle.value = null
    resizeWidth.value = ''
    resizeHeight.value = ''

    // 移除事件监听器
    document.removeEventListener('mousemove', handleMouseResize)
    document.removeEventListener('mouseup', handleMouseEnd)
    document.removeEventListener('touchmove', handleTouchResize)
    document.removeEventListener('touchend', handleTouchEnd)
    document.body.style.overflow = ''
  }

  // 更新调整大小状态
  const updateResizeState = (width: string, height: string, resizing: boolean) => {
    resizeWidth.value = width
    resizeHeight.value = height
    isResizing.value = resizing
  }

  return {
    handleResizeStart,
    handleTouchStart,
    updateResizeState,
  }
}
