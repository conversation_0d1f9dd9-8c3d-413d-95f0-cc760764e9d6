package com.shenmo.wen.common.exception.enumeration;

import com.shenmo.wen.common.exception.BaseException;

import java.lang.annotation.*;

/**
 * 异常枚举编码注解
 *
 * <AUTHOR>
 */
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
public @interface ExceptionType {

    /**
     * 异常类型
     */
    Class<? extends BaseException> type() default BaseException.class;

    /**
     * 模块编码
     *
     * <AUTHOR>
     */
    String module();
}
