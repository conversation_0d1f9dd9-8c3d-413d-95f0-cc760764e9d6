/*
 * LongPress 组件样式
 * 长按交互组件的样式定义，包括动画效果
 */

.long-press-wrapper {
  display: inline-block;
  cursor: pointer;
}

.slot-content {
  transition: transform 0.2s ease; /* 添加平滑过渡效果 */
}

.long-press-active {
  animation: long-press-animation 0.5s infinite alternate; /* 持续播放动画 */
}

@keyframes long-press-animation {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(1.1); /* 缩放效果 */
  }
}
