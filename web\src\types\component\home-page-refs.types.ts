import type { ArticleListRef } from './article-list-ref.types'
import type { ArticleModalExpose } from './article-modal-expose.types'
import type { CommentDanmakuRef } from './comment-danmaku-ref.types'
import type { Ref } from 'vue'

/**
 * 首页组件引用类型
 */
export interface HomePageRefs {
  /** 文章列表组件引用 */
  articleListRef: Ref<ArticleListRef | null>
  /** 评论弹幕组件引用 */
  commentDanmakuRef: Ref<CommentDanmakuRef | null>
  /** 文章模态框组件引用 */
  articleModalRef: Ref<ArticleModalExpose | null>
}
