import { ref, onMounted, onUnmounted } from 'vue'

import { createDialogOptions } from '@/config/naive-ui-config'
import { type Article } from '@/types/article/article.types'
import dialog from '@/utils/ui/dialog'

export function useArticleModalState() {
  // 弹框显示状态
  const isArticleDialogVisible = ref<boolean>(false)

  // 编辑状态，判断是创建还是编辑
  const isEditingArticle = ref<boolean>(false)

  // 加载状态
  const submitLoading = ref<boolean>(false)
  const quickSaveLoading = ref<boolean>(false)

  // 打开创建文章弹框
  const openCreateArticleDialog = (resetForm: () => void) => {
    isEditingArticle.value = false // 确保是创建模式
    resetForm()
    isArticleDialogVisible.value = true
  }

  // 打开编辑文章弹框
  const openEditArticleDialog = (articleData: Article, setFormData: (data: Article) => void) => {
    isEditingArticle.value = true // 编辑模式
    setFormData(articleData)
    isArticleDialogVisible.value = true
  }

  // 处理关闭弹框
  const handleClose = (resetForm: () => void): boolean => {
    dialog.warning(
      createDialogOptions({
        title: '提示',
        content: '你确定关闭？',
        positiveText: '确定',
        negativeText: '不确定',
        onPositiveClick: () => {
          resetForm()
          isArticleDialogVisible.value = false
        },
        onNegativeClick: () => {},
      }),
    )
    return false
  }

  // 全局键盘事件监听（只处理 Ctrl+S 快速保存）
  const handleGlobalKeyDown = (quickSave: () => void) => {
    return (e: KeyboardEvent) => {
      if (isArticleDialogVisible.value && e.ctrlKey && e.key === 's') {
        e.preventDefault() // 阻止浏览器默认的保存行为
        e.stopPropagation() // 阻止事件冒泡
        quickSave() // 使用快速保存方法
      }
    }
  }

  // 设置键盘监听
  const setupKeyboardListener = (quickSave: () => void) => {
    const keydownHandler = handleGlobalKeyDown(quickSave)

    onMounted(() => {
      window.addEventListener('keydown', keydownHandler)
    })

    onUnmounted(() => {
      window.removeEventListener('keydown', keydownHandler)
    })
  }

  return {
    // 状态
    isArticleDialogVisible,
    isEditingArticle,
    submitLoading,
    quickSaveLoading,

    // 方法
    openCreateArticleDialog,
    openEditArticleDialog,
    handleClose,
    setupKeyboardListener,
  }
}
