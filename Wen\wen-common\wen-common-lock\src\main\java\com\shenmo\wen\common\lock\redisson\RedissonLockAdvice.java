package com.shenmo.wen.common.lock.redisson;

import com.shenmo.wen.common.constant.StringConstant;
import com.shenmo.wen.common.exception.BaseException;
import com.shenmo.wen.common.util.spring.SpelUtils;
import com.shenmo.wen.common.util.spring.SpringAopUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.ObjectProvider;

import java.lang.annotation.Target;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;


/**
 * redisson lock增强
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
public class RedissonLockAdvice {

    /**
     * redisson 客户端
     */
    private final RedissonClient redissonClient;

    /**
     * 锁选项
     */
    private final LockOption lockOption;

    /**
     * 构造方法
     *
     * @param redissonClient redisson 客户端
     * @param lockOptionProvider     锁前缀
     * <AUTHOR>
     */
    public RedissonLockAdvice(RedissonClient redissonClient, ObjectProvider<LockOption> lockOptionProvider) {

        this.redissonClient = redissonClient;
        this.lockOption = lockOptionProvider.getIfAvailable();
    }

    /**
     * 对@RedissonLock注解标注的方法进行环绕增强
     * <p>
     * {@link RedissonLock}
     * <p>
     * {@link ProceedingJoinPoint}
     * <p>
     * {@link Target}
     *
     * @param pjp aop环绕连接点
     * @return 执行结果
     * @throws Throwable {@link ProceedingJoinPoint#proceed(Object[])}可能抛出的异常
     * <AUTHOR>
     */
    @Around("@annotation(com.shenmo.wen.common.lock.redisson.RedissonLock)")
    private Object redissonLock(ProceedingJoinPoint pjp) throws Throwable {
        final Object pjpTarget = pjp.getTarget();
        final Object[] args = pjp.getArgs();
        final Method method = SpringAopUtils.acquireCurrentMethod(pjp, pjpTarget.getClass());
        final RedissonLock redissonLock = method.getAnnotation(RedissonLock.class);
        if (Objects.nonNull(redissonLock)) {
            final boolean continueTry = redissonLock.enableContinueTry();
            String lockKey = redissonLock.value();
            lockKey = String.valueOf(SpelUtils.getMethodArgValue(lockKey, pjpTarget, method, args));
            if (redissonLock.enablePrefix() && Objects.nonNull(lockOption)) {
                lockKey = lockOption.prefix() + StringConstant.COLON + lockKey;
            }
            // 获取锁
            log.info("'{}' on lock", lockKey);
            final RLock lock = redissonClient.getLock(lockKey);
            final RBucket<String> expMsg = redissonClient.getBucket(lockKey + "_exp_msg");
            final String message = Optional.ofNullable(expMsg.get()).orElse(redissonLock.message());
            final boolean enableException = redissonLock.enableException();
            while (true) {
                if (lock.tryLock()) {
                    try {
                        RedissonLockContextHolder.set(lock);
                        // 保存当前锁的异常消息
                        expMsg.set(message);
                        // 执行原逻辑
                        return pjp.proceed();
                    } finally {
                        // 释放消息
                        try {
                            expMsg.delete();
                            // 释放锁
                            final Lock l = RedissonLockContextHolder.get();
                            if (Objects.nonNull(l)) {
                                l.unlock();
                            }
                        } finally {
                            RedissonLockContextHolder.release();
                        }
                    }
                } else {
                    if (!continueTry) {
                        if (enableException) {
                            // 抛出定制异常
                            throw new BaseException(redissonLock.httpStatus(), Objects.nonNull(lockOption) ? lockOption.messageHandle(message) : message);
                        } else {
                            if (!method.getReturnType().equals(Void.TYPE)) {
                                log.warn("The parameter 'RedissonLock#enableException' is false, and yet lock is currently used in the non void method, maybe occur 'NullPointException'");
                            }
                            return null;
                        }
                    }
                }
                TimeUnit.MILLISECONDS.sleep(500);
            }
        } else {
            // 执行原逻辑
            return pjp.proceed();
        }
    }


    /**
     * 锁选项
     *
     * <AUTHOR>
     * @version 1.0.0
     */
    @FunctionalInterface
    public interface LockOption {

        /**
         * 获取锁前缀
         *
         * @return 自定义锁前缀
         * <AUTHOR>
         */
        String prefix();

        /**
         * {@link RedissonLock#message()}处理
         *
         * @param message {@link RedissonLock#message()}
         * <AUTHOR>
         */
        default String messageHandle(String message) {
            return message;
        }
    }
}
