import { type ResponseData } from '@/types/api/response-data.types'
import type { LoginParams } from '@/types/auth/login-params.types'
import type { LoginResponse } from '@/types/auth/login-response.types'
import type { RegisterParams } from '@/types/auth/register-params.types'
import type { RegisterResponse } from '@/types/auth/register-response.types'
import type { ResetPasswordParams } from '@/types/auth/reset-password-params.types'
import type { SendEmailCodeParams } from '@/types/auth/send-email-code-params.types'
import api from '@/utils/api/api'

/**
 * 认证相关API接口
 * 提供用户登录、注册、登出等认证功能
 */
const authApi = {
  /** API基础路径 */
  URL: '/authentication',

  /**
   * 用户登录
   * @param params 登录参数，包含用户名/邮箱和密码
   * @returns 返回登录成功后的用户信息和令牌
   */
  login: async (params: LoginParams): Promise<ResponseData<LoginResponse>> => {
    const response = await api.post<ResponseData<LoginResponse>>(authApi.URL + '/sessions', params)
    return response.data
  },

  /**
   * 用户注册（创建账户）
   * @param params 注册参数，包含用户名、邮箱、密码等信息
   * @returns 返回注册结果信息
   */
  register: async (params: RegisterParams): Promise<ResponseData<RegisterResponse>> => {
    const response = await api.post<ResponseData<RegisterResponse>>(
      authApi.URL + '/accounts',
      params,
    )
    return response.data
  },

  /**
   * 用户登出
   * 清除服务端的用户会话信息
   * @returns 返回登出操作结果
   */
  logout: async (): Promise<ResponseData<void>> => {
    const response = await api.del<ResponseData<void>>(authApi.URL + '/sessions')
    return response.data
  },

  /**
   * 发送邮箱验证码
   * @param params 发送验证码参数，包含邮箱地址和验证码类型
   * @returns 返回发送结果，true表示发送成功
   */
  sendEmailCode: async (params: SendEmailCodeParams): Promise<ResponseData<boolean>> => {
    const response = await api.post<ResponseData<boolean>>(authApi.URL + '/email-codes', params)
    return response.data
  },

  /**
   * 重置账户密码
   * @param params 重置密码参数，包含邮箱、验证码和新密码
   * @returns 返回重置密码操作结果
   */
  resetPassword: async (params: ResetPasswordParams): Promise<ResponseData<void>> => {
    const response = await api.put<ResponseData<void>>(authApi.URL + '/accounts/password', params)
    return response.data
  },
}

export default authApi
