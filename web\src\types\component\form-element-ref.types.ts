/**
 * 表单元素引用类型定义
 * 定义各种表单元素的引用类型
 */

/**
 * NForm 组件引用类型
 */
export interface NFormRef {
  /** 验证表单 */
  validate: (callback?: (errors: boolean) => void) => Promise<void>
  /** 恢复验证状态 */
  restoreValidation: () => void
  /** 验证指定字段 */
  validateField: (path: string, callback?: (errors: boolean) => void) => Promise<void>
}

/**
 * Turnstile 验证组件引用类型
 */
export interface TurnstileRef {
  /** 重置验证 */
  reset: () => void
  /** 获取验证令牌 */
  getToken: () => string | null
}

/**
 * 通用元素引用类型
 */
export type ElementRef = HTMLElement | null

/**
 * 组件引用类型
 */
export type ComponentRef<T = Record<string, unknown>> = T | null
