package com.shenmo.wen.app.authentication.config;

import cn.dev33.satoken.listener.SaTokenListenerForSimple;
import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import com.shenmo.wen.common.constant.RedisKeyConstant;
import com.shenmo.wen.common.util.spring.SpringRedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WenSaTokenListener extends SaTokenListenerForSimple {

    @Override
    public void doLogin(String loginType, Object loginId, String tokenValue, SaLoginParameter loginParameter) {
        final SetOperations<String, Object> set = SpringRedisUtils.forSet();
        set.add(RedisKeyConstant.USER_ONLINE, String.valueOf(loginId));
    }

    @Override
    public void doLogout(String loginType, Object loginId, String tokenValue) {
        final SetOperations<String, Object> set = SpringRedisUtils.forSet();
        set.remove(RedisKeyConstant.USER_ONLINE, String.valueOf(loginId));
    }
}
