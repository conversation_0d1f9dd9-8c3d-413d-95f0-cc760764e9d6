<!--
  登录表单组件

  功能说明：
  - 支持手机号和邮箱两种登录方式
  - 集成 Turnstile 人机验证
  - 邮箱验证码发送功能
  - 表单验证和错误提示
-->
<template>
  <NForm
    label-placement="left"
    :model="form"
    :rules="loginFormRules"
    ref="formRef"
    :label-width="90"
    class="login-form"
  >
    <!-- 登录方式切换 -->
    <LoginModeSwitch v-model:login-mode="loginMode" />

    <!-- 手机号登录模式 -->
    <template v-if="loginMode === 'phone'">
      <NFormItem label="手机号" path="phone">
        <NInput
          class="login-form-ipt"
          v-model:value="form.phone"
          placeholder="请输入手机号"
          maxlength="11"
          @input="phoneInputFilter"
          @keyup.enter="$emit('login')"
        />
      </NFormItem>
      <NFormItem label="密码" path="password">
        <NInput
          class="login-form-ipt"
          v-model:value="form.password"
          type="password"
          placeholder="请输入密码"
          show-password-on="click"
          @keyup.enter="$emit('login')"
        />
      </NFormItem>
    </template>

    <!-- 邮箱登录模式 -->
    <template v-if="loginMode === 'email'">
      <NFormItem label="邮箱" path="email">
        <NInput
          class="login-form-ipt"
          v-model:value="form.email"
          placeholder="请输入邮箱地址"
          @keyup.enter="$emit('login')"
        />
      </NFormItem>
      <NFormItem label="验证码" path="emailCode">
        <EmailCodeInput
          v-model:value="form.emailCode"
          :email-code-state="emailCodeState"
          :disabled="!isValidEmail || !isTurnstileVerified"
          @send-code="$emit('send-email-code', EmailCodeType.LOGIN)"
          @keyup.enter="$emit('login')"
        />
      </NFormItem>
    </template>

    <!-- Turnstile 验证组件 -->
    <TurnstileVerification
      ref="turnstileRef"
      @success="$emit('turnstile-success', $event)"
      @error="$emit('turnstile-error')"
    />

    <!-- 忘记密码链接 -->
    <div class="forgot-password-link">
      <NButton text type="tertiary" @click="$emit('show-forgot-password')" size="small">
        忘记密码？
      </NButton>
    </div>

    <!-- 按钮容器 -->
    <div class="login-form-btn">
      <NButton class="login-btn" type="info" @click="$emit('login')" :loading="loading">
        登录
      </NButton>
      <NButton class="flip-btn" @click="$emit('flip-card')">注册</NButton>
    </div>
  </NForm>
</template>

<script lang="ts" setup>
import { NForm, NFormItem, NButton, NInput } from 'naive-ui'
import { ref, computed, watch } from 'vue'

import TurnstileVerification from '@/components/verification/TurnstileVerification.vue'
import type { LoginMode } from '@/types/auth/login-mode.types'
import type { EmailCodeState } from '@/types/component/email-code-state.types'
import type { NFormRef } from '@/types/component/form-element-ref.types'
import type { LoginFormData } from '@/types/component/login-form-data.types'
import { EmailCodeType } from '@/types/email/email-code-type.types'
import type { FormRules } from '@/types/form/form-rules.types'

import EmailCodeInput from './EmailCodeInput.vue'
import LoginModeSwitch from './LoginModeSwitch.vue'

// 定义组件 Props
interface LoginFormProps {
  form: LoginFormData
  loginMode: LoginMode
  emailCodeState: EmailCodeState
  isValidEmail: boolean
  isTurnstileVerified: boolean
  loading: boolean
}

const props = defineProps<LoginFormProps>()

// 定义组件 Emits
interface LoginFormEmits {
  'update:form': [value: LoginFormData]
  'update:login-mode': [value: LoginMode]
  login: []
  'flip-card': []
  'show-forgot-password': []
  'send-email-code': [type: EmailCodeType]
  'turnstile-success': [token: string]
  'turnstile-error': []
}

const emit = defineEmits<LoginFormEmits>()

// 组件引用
const formRef = ref<NFormRef | null>(null)
const turnstileRef = ref()

// 双向绑定
const form = computed({
  get: () => props.form,
  set: (value) => emit('update:form', value),
})

const loginMode = computed({
  get: () => props.loginMode,
  set: (value) => emit('update:login-mode', value),
})

/**
 * 登录表单验证规则
 */
const loginFormRules = computed((): FormRules => {
  const rules: FormRules = {}

  if (props.loginMode === 'phone') {
    rules.phone = [
      { required: true, message: '手机号不能为空', trigger: 'blur' },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入有效的手机号',
        trigger: 'blur',
      },
    ]
    rules.password = [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, message: '密码长度至少需要6个字符', trigger: 'blur' },
    ]
  } else if (props.loginMode === 'email') {
    rules.email = [
      { required: true, message: '邮箱不能为空', trigger: 'blur' },
      {
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: '请输入有效的邮箱地址',
        trigger: 'blur',
      },
    ]
    rules.emailCode = [
      { required: true, message: '请输入邮箱验证码', trigger: 'blur' },
      {
        pattern: /^\d{6}$/,
        message: '验证码格式不正确',
        trigger: 'blur',
      },
    ]
  }

  return rules
})

/**
 * 手机号输入过滤
 */
const phoneInputFilter = (value: string): void => {
  form.value.phone = value.replace(/\D/g, '')
}

/**
 * 监听登录模式变化，清空表单验证
 */
watch(
  () => props.loginMode,
  (): void => {
    if (formRef.value) {
      formRef.value.restoreValidation()
    }
  },
)

/**
 * 表单验证方法
 */
const validate = (callback?: (errors?: boolean) => void) => {
  return formRef.value?.validate(callback)
}

/**
 * 重置表单验证
 */
const restoreValidation = () => {
  formRef.value?.restoreValidation()
}

/**
 * 重置 Turnstile
 */
const resetTurnstile = () => {
  turnstileRef.value?.reset()
}

// 暴露方法给父组件
defineExpose({
  validate,
  restoreValidation,
  resetTurnstile,
})
</script>

<style lang="scss" scoped>
@use '@/styles/auth/login-form';
</style>
