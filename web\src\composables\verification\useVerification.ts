import { ref, type Ref } from 'vue'

/**
 * 验证规则类型
 */
interface ValidationRule {
  /** 规则名称 */
  name: string
  /** 验证函数 */
  validator: (value: unknown) => boolean
  /** 错误消息 */
  message: string
}

/**
 * 验证结果类型
 */
interface ValidationResult {
  /** 是否验证通过 */
  isValid: boolean
  /** 错误消息列表 */
  errors: string[]
}

/**
 * 字段验证状态类型
 */
interface FieldValidation {
  /** 字段值 */
  value: unknown
  /** 验证规则 */
  rules: ValidationRule[]
  /** 验证结果 */
  result: ValidationResult
  /** 是否已验证 */
  validated: boolean
}

/**
 * 验证组合式函数返回值类型
 */
interface UseVerificationReturn {
  /** 验证状态映射 */
  validations: Ref<Map<string, FieldValidation>>
  /** 整体验证状态 */
  isFormValid: Ref<boolean>
  /** 添加字段验证 */
  addFieldValidation: (fieldName: string, rules: ValidationRule[]) => void
  /** 验证单个字段 */
  validateField: (fieldName: string, value: unknown) => ValidationResult
  /** 验证所有字段 */
  validateAll: () => ValidationResult
  /** 清除字段验证 */
  clearFieldValidation: (fieldName: string) => void
  /** 清除所有验证 */
  clearAllValidations: () => void
  /** 获取字段错误消息 */
  getFieldErrors: (fieldName: string) => string[]
  /** 创建常用验证规则 */
  createRules: {
    required: (message?: string) => ValidationRule
    email: (message?: string) => ValidationRule
    minLength: (length: number, message?: string) => ValidationRule
    maxLength: (length: number, message?: string) => ValidationRule
    pattern: (regex: RegExp, message?: string) => ValidationRule
    numeric: (message?: string) => ValidationRule
  }
}

/**
 * 验证管理组合式函数
 * 提供表单验证、字段验证等功能
 */
export function useVerification(): UseVerificationReturn {
  // 验证状态映射
  const validations = ref<Map<string, FieldValidation>>(new Map())

  // 整体验证状态
  const isFormValid = ref(true)

  /**
   * 添加字段验证规则
   * @param fieldName 字段名称
   * @param rules 验证规则数组
   */
  const addFieldValidation = (fieldName: string, rules: ValidationRule[]): void => {
    validations.value.set(fieldName, {
      value: undefined,
      rules,
      result: { isValid: true, errors: [] },
      validated: false,
    })
  }

  /**
   * 验证单个字段
   * @param fieldName 字段名称
   * @param value 字段值
   * @returns 验证结果
   */
  const validateField = (fieldName: string, value: unknown): ValidationResult => {
    const fieldValidation = validations.value.get(fieldName)
    if (!fieldValidation) {
      return { isValid: true, errors: [] }
    }

    const errors: string[] = []

    // 执行所有验证规则
    for (const rule of fieldValidation.rules) {
      if (!rule.validator(value)) {
        errors.push(rule.message)
      }
    }

    const result: ValidationResult = {
      isValid: errors.length === 0,
      errors,
    }

    // 更新字段验证状态
    fieldValidation.value = value
    fieldValidation.result = result
    fieldValidation.validated = true

    // 更新整体验证状态
    updateFormValidation()

    return result
  }

  /**
   * 验证所有字段
   * @returns 整体验证结果
   */
  const validateAll = (): ValidationResult => {
    const allErrors: string[] = []
    let isValid = true

    for (const [fieldName, fieldValidation] of validations.value) {
      const result = validateField(fieldName, fieldValidation.value)
      if (!result.isValid) {
        isValid = false
        allErrors.push(...result.errors)
      }
    }

    return { isValid, errors: allErrors }
  }

  /**
   * 更新整体表单验证状态
   */
  const updateFormValidation = (): void => {
    let isValid = true
    for (const fieldValidation of validations.value.values()) {
      if (fieldValidation.validated && !fieldValidation.result.isValid) {
        isValid = false
        break
      }
    }
    isFormValid.value = isValid
  }

  /**
   * 清除字段验证
   * @param fieldName 字段名称
   */
  const clearFieldValidation = (fieldName: string): void => {
    validations.value.delete(fieldName)
    updateFormValidation()
  }

  /**
   * 清除所有验证
   */
  const clearAllValidations = (): void => {
    validations.value.clear()
    isFormValid.value = true
  }

  /**
   * 获取字段错误消息
   * @param fieldName 字段名称
   * @returns 错误消息数组
   */
  const getFieldErrors = (fieldName: string): string[] => {
    const fieldValidation = validations.value.get(fieldName)
    return fieldValidation?.result.errors || []
  }

  // 常用验证规则创建器
  const createRules = {
    /**
     * 必填验证规则
     * @param message 自定义错误消息
     */
    required: (message: string = '此字段为必填项'): ValidationRule => ({
      name: 'required',
      validator: (value: unknown) => {
        if (value === null || value === undefined) return false
        if (typeof value === 'string') return value.trim().length > 0
        if (Array.isArray(value)) return value.length > 0
        return true
      },
      message,
    }),

    /**
     * 邮箱验证规则
     * @param message 自定义错误消息
     */
    email: (message: string = '请输入有效的邮箱地址'): ValidationRule => ({
      name: 'email',
      validator: (value: unknown) => {
        if (!value) return true // 空值由 required 规则处理
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        return emailRegex.test(String(value))
      },
      message,
    }),

    /**
     * 最小长度验证规则
     * @param length 最小长度
     * @param message 自定义错误消息
     */
    minLength: (length: number, message?: string): ValidationRule => ({
      name: 'minLength',
      validator: (value: unknown) => {
        if (!value) return true // 空值由 required 规则处理
        return String(value).length >= length
      },
      message: message || `最少需要 ${length} 个字符`,
    }),

    /**
     * 最大长度验证规则
     * @param length 最大长度
     * @param message 自定义错误消息
     */
    maxLength: (length: number, message?: string): ValidationRule => ({
      name: 'maxLength',
      validator: (value: unknown) => {
        if (!value) return true // 空值由 required 规则处理
        return String(value).length <= length
      },
      message: message || `最多允许 ${length} 个字符`,
    }),

    /**
     * 正则表达式验证规则
     * @param regex 正则表达式
     * @param message 自定义错误消息
     */
    pattern: (regex: RegExp, message: string = '格式不正确'): ValidationRule => ({
      name: 'pattern',
      validator: (value: unknown) => {
        if (!value) return true // 空值由 required 规则处理
        return regex.test(String(value))
      },
      message,
    }),

    /**
     * 数字验证规则
     * @param message 自定义错误消息
     */
    numeric: (message: string = '请输入有效的数字'): ValidationRule => ({
      name: 'numeric',
      validator: (value: unknown) => {
        if (!value) return true // 空值由 required 规则处理
        return !isNaN(Number(value))
      },
      message,
    }),
  }

  return {
    validations,
    isFormValid,
    addFieldValidation,
    validateField,
    validateAll,
    clearFieldValidation,
    clearAllValidations,
    getFieldErrors,
    createRules,
  }
}
