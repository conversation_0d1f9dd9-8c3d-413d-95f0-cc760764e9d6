<template>
  <span
    class="mention"
    :data-type="'mention'"
    :data-id="mentionData.id"
    :data-label="mentionData.label"
    :data-avatar="mentionData.avatar || ''"
    contenteditable="false"
  >
    <span class="mention-name">@{{ mentionData.label }}</span>
    <img
      v-if="mentionData.avatar"
      :src="avatarUrl"
      :alt="mentionData.label"
      class="mention-avatar"
      loading="lazy"
    />
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

import fileApi from '@/api/file'

interface MentionData {
  id: string
  label: string
  avatar?: string
}

interface Props {
  mentionData: MentionData
}

const props = defineProps<Props>()

/**
 * 计算头像 URL
 */
const avatarUrl = computed(() => {
  if (!props.mentionData.avatar) return ''
  return fileApi.getResourceURL(props.mentionData.avatar)
})
</script>

<!-- 样式继承自全局mention样式，无需重复定义 -->
