import logger from '@/utils/log/log'

/**
 * 时间处理工具类
 */
class DateTime {
  /**
   * 将时间戳（毫秒）转换为指定格式的时间字符串
   * @param timestamp 时间戳，单位为毫秒
   * @param format 可选的时间字符串格式，默认为'YYYY-MM-DD HH:mm:ss'，可自定义格式，例如'YYYY-MM-DD'等
   * @returns 格式化后的时间字符串
   */
  static toTimeString(timestamp: string, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
    const date = new Date(parseInt(timestamp))
    const year = date.getFullYear().toString()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')

    const formatMap: { [key: string]: string } = {
      YYYY: year,
      MM: month,
      DD: day,
      HH: hours,
      mm: minutes,
      ss: seconds,
    }

    return format.replace(/YYYY|MM|DD|HH|mm|ss/g, (matched) => {
      return formatMap[matched]
    })
  }

  /**
   * 获取当前时间戳（毫秒）
   * @returns 当前时间的时间戳（毫秒）
   */
  static getCurrentTimestamp(): number {
    return Date.now()
  }

  /**
   * 将日期字符串转换为时间戳（毫秒），支持多种常见日期格式，如'YYYY-MM-DD HH:mm:ss'、'YYYY-MM-DD'等
   * @param dateString 日期字符串，需符合指定格式
   * @param format 日期字符串的格式，默认为'YYYY-MM-DD HH:mm:ss'，若格式不同需传入对应格式
   * @returns 对应的时间戳（毫秒），若解析失败返回null
   */
  static dateToTimestamp(
    dateString: string,
    format: string = 'YYYY-MM-DD HH:mm:ss',
  ): number | null {
    const formatRegexMap: { [key: string]: RegExp } = {
      'YYYY-MM-DD HH:mm:ss': /^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/g,
      'YYYY-MM-DD': /^(\d{4})-(\d{2})-(\d{2})$/g,
    }

    const regex = formatRegexMap[format]
    if (!regex) {
      logger.warn(`不支持的日期格式: ${format}`)
      return null
    }

    const match = dateString.match(regex)
    if (!match) {
      logger.warn(`日期字符串 ${dateString} 不符合格式 ${format}`)
      return null
    }

    const year = parseInt(match[1], 10)
    const month = parseInt(match[2], 10) - 1
    const day = parseInt(match[3], 10)
    let hours = 0
    let minutes = 0
    let seconds = 0

    if (format === 'YYYY-MM-DD HH:mm:ss') {
      hours = parseInt(match[4], 10)
      minutes = parseInt(match[5], 10)
      seconds = parseInt(match[6], 10)
    }

    const date = new Date(year, month, day, hours, minutes, seconds)
    return date.getTime()
  }

  /**
   * 格式化日期对象为指定格式的时间字符串
   * @param date 要格式化的日期对象（Date类型）
   * @param format 时间字符串格式，默认为'YYYY-MM-DD HH:mm:ss'
   * @returns 格式化后的时间字符串
   */
  static formatDate(date: Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
    const year = date.getFullYear().toString()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')

    const formatMap: { [key: string]: string } = {
      YYYY: year,
      MM: month,
      DD: day,
      HH: hours,
      mm: minutes,
      ss: seconds,
    }

    return format.replace(/YYYY|MM|DD|HH|mm|ss/g, (matched) => {
      return formatMap[matched]
    })
  }

  /**
   * 计算相对时间（几秒前、几分钟前等）
   * @param timestamp 时间戳，单位为毫秒
   * @returns 相对时间字符串
   */
  static getRelativeTime(timestamp: string): string {
    const now = Date.now()
    const time = parseInt(timestamp)
    const diff = now - time

    // 不到1秒
    if (diff < 1000) {
      return '刚刚'
    }

    // 秒级别
    if (diff < 60 * 1000) {
      return Math.floor(diff / 1000) + '秒前'
    }

    // 分钟级别
    if (diff < 60 * 60 * 1000) {
      return Math.floor(diff / (60 * 1000)) + '分钟前'
    }

    // 小时级别
    if (diff < 24 * 60 * 60 * 1000) {
      return Math.floor(diff / (60 * 60 * 1000)) + '小时前'
    }

    // 天级别
    if (diff < 30 * 24 * 60 * 60 * 1000) {
      return Math.floor(diff / (24 * 60 * 60 * 1000)) + '天前'
    }

    // 月级别
    if (diff < 12 * 30 * 24 * 60 * 60 * 1000) {
      const months = Math.floor(diff / (30 * 24 * 60 * 60 * 1000))
      return months === 6 ? '半年前' : months + '个月前'
    }

    // 年级别
    return Math.floor(diff / (12 * 30 * 24 * 60 * 60 * 1000)) + '年前'
  }
}
export default DateTime
