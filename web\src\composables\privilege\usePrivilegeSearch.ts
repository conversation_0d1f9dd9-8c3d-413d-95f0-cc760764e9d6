import { useMessage } from 'naive-ui'
import { ref, computed, type Ref, type ComputedRef } from 'vue'

import { PRIVILEGE_SEARCH_CONDITION } from '@/constants/privilege/storage.constants'
import type { PrivilegeListRef } from '@/types/component/privilege-list-ref.types'
import type { PrivilegeSearchCondition } from '@/types/privilege/privilege-search-condition.types'
import localStorage from '@/utils/storage/local-storage'

/**
 * 特权搜索组合式函数返回值类型
 */
interface UsePrivilegeSearchReturn {
  /** 搜索状态 */
  isLoading: Ref<boolean>
  isSearching: Ref<boolean>
  /** 搜索条件 */
  searchCondition: Ref<PrivilegeSearchCondition>
  /** 是否有搜索条件 */
  hasSearchCondition: ComputedRef<boolean>
  /** 搜索方法 */
  search: (privilegeListRef: PrivilegeListRef | null, loadMore?: boolean) => Promise<void>
  /** 处理标签选择 */
  handleTagSelected: (tagName: string, privilegeListRef: PrivilegeListRef | null) => Promise<void>
  /** 保存搜索条件 */
  saveSearchCondition: () => void
  /** 加载搜索条件 */
  loadSearchCondition: () => void
  /** 清理资源 */
  cleanup: () => void
}

/**
 * 特权搜索管理组合式函数
 * 提供特权搜索功能
 */
export function usePrivilegeSearch(): UsePrivilegeSearchReturn {
  const message = useMessage()

  // 搜索状态
  const isLoading = ref(false)
  const isSearching = ref(false)
  const currentRequestController = ref<AbortController | null>(null)

  // 搜索条件
  const searchCondition = ref<PrivilegeSearchCondition>({
    searchKey: '',
    owner: false,
    interaction: false,
    favorite: false,
    tag: '',
  })

  // 是否有搜索条件
  const hasSearchCondition = computed((): boolean => {
    return (
      searchCondition.value.searchKey.trim() !== '' ||
      searchCondition.value.owner ||
      searchCondition.value.interaction ||
      searchCondition.value.favorite ||
      (!!searchCondition.value.tag && searchCondition.value.tag.trim() !== '')
    )
  })

  /**
   * 执行搜索
   */
  const search = async (
    privilegeListRef: PrivilegeListRef | null,
    loadMore: boolean = false,
  ): Promise<void> => {
    if (!privilegeListRef) {
      console.warn('特权列表组件引用为空，跳过搜索')
      return
    }

    // 取消之前的请求
    if (currentRequestController.value) {
      currentRequestController.value.abort()
    }

    // 创建新的请求控制器
    currentRequestController.value = new AbortController()

    isLoading.value = true
    isSearching.value = true

    try {
      await privilegeListRef.search(loadMore)
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('特权搜索失败:', error)
        message.error('搜索失败，请稍后重试')
      }
    } finally {
      isLoading.value = false
      isSearching.value = false
      currentRequestController.value = null
    }
  }

  /**
   * 处理标签选择
   */
  const handleTagSelected = async (
    tagName: string,
    privilegeListRef: PrivilegeListRef | null,
  ): Promise<void> => {
    searchCondition.value.tag = tagName
    saveSearchCondition()
    await search(privilegeListRef)
  }

  /**
   * 保存搜索条件到本地存储
   */
  const saveSearchCondition = (): void => {
    localStorage.set(PRIVILEGE_SEARCH_CONDITION, searchCondition.value)
  }

  /**
   * 从本地存储加载搜索条件
   */
  const loadSearchCondition = (): void => {
    const saved = localStorage.get<PrivilegeSearchCondition>(PRIVILEGE_SEARCH_CONDITION)
    if (saved) {
      searchCondition.value = { ...searchCondition.value, ...saved }
    }
  }

  /**
   * 清理资源
   */
  const cleanup = (): void => {
    if (currentRequestController.value) {
      currentRequestController.value.abort()
      currentRequestController.value = null
    }
  }

  return {
    isLoading,
    isSearching,
    searchCondition,
    hasSearchCondition,
    search,
    handleTagSelected,
    saveSearchCondition,
    loadSearchCondition,
    cleanup,
  }
}
