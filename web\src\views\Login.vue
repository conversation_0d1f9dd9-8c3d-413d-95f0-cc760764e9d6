<!--
  登录注册页面组件

  功能说明：
  - 提供用户登录和注册功能
  - 支持手机号和邮箱两种登录方式
  - 集成 Turnstile 人机验证
  - 提供忘记密码功能
  - 包含动态背景动画效果

  主要业务逻辑：
  1. 登录功能：支持手机号+密码和邮箱+验证码两种登录方式
  2. 注册功能：完整的用户注册流程，包含邮箱验证
  3. 表单验证：实时表单验证和错误提示
  4. 安全验证：集成 Cloudflare Turnstile 防机器人验证
  5. 密码找回：通过邮箱验证码重置密码

  交互特性：
  - 3D 翻转卡片动画效果
  - 验证码倒计时功能
  - 表单自动验证和提示
  - 响应式设计适配移动端
  - 主题切换支持
-->
<template>
  <div class="layout-container">
    <BackgroundAnimation :particleCount="40" />
    <div class="header-container">
      <NImage width="200" preview-disabled :src="logo" :loading="true" :fallback-src="logo" />
    </div>
    <div class="card-container">
      <NCard hoverable :style="cardStyle">
        <!-- 登录表单 -->
        <LoginForm
          v-show="!pageState.isFlipped"
          v-model:form="loginForm"
          v-model:login-mode="pageState.loginMode"
          :email-code-state="emailCodeState"
          :is-valid-email="isValidLoginEmail"
          :is-turnstile-verified="isLoginTurnstileVerified"
          :loading="pageState.loginLoading"
          @login="handleLogin"
          @flip-card="flipCard"
          @show-forgot-password="pageState.showForgotPassword = true"
          @send-email-code="sendEmailCode"
          @turnstile-success="handleTurnstileSuccess"
          @turnstile-error="handleTurnstileError"
          ref="loginFormRef"
        />

        <!-- 注册表单 -->
        <RegisterForm
          v-show="pageState.isFlipped"
          v-model:form="registerForm"
          :email-code-state="emailCodeState"
          :is-valid-email="isValidRegisterEmail"
          :is-turnstile-verified="isRegisterTurnstileVerified"
          @register="handleRegister"
          @flip-card="flipCard"
          @send-email-code="sendEmailCode"
          @turnstile-success="handleTurnstileSuccess"
          @turnstile-error="handleTurnstileError"
          ref="registerFormRef"
        />
      </NCard>
    </div>
    <div class="footer-container">
      <ThemeToggle />
    </div>

    <!-- 忘记密码模态框 -->
    <ForgotPasswordModal
      v-model:show="pageState.showForgotPassword"
      @success="handleForgotPasswordSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { NCard, NImage } from 'naive-ui'
import { ref, computed, watch } from 'vue'

// 导入 API 和工具
import authApi from '@/api/auth'
import logo from '@/assets/logo.png'
// 导入组件
import LoginForm from '@/components/auth/LoginForm.vue'
import RegisterForm from '@/components/auth/RegisterForm.vue'
import BackgroundAnimation from '@/components/background/BackgroundAnimation.vue'
import ThemeToggle from '@/components/theme/ThemeToggle.vue'
import { ForgotPasswordModal } from '@/components/user'
// 导入常量和枚举
import { HOME_CARD } from '@/constants/home/<USER>'
import router from '@/router'
import type { LoginParams } from '@/types/auth/login-params.types'
import type { EmailCodeState } from '@/types/component/email-code-state.types'
import type { LoginFormData } from '@/types/component/login-form-data.types'
import type { LoginPageEmits } from '@/types/component/login-page-emits.types'
import type { LoginPageExpose } from '@/types/component/login-page-expose.types'
import type { LoginPageState } from '@/types/component/login-page-state.types'
import type { LoginPageProps } from '@/types/component/login-page.types'
import type { RegisterFormData } from '@/types/component/register-form-data.types'
import type { TurnstileState } from '@/types/component/turnstile-state.types'
import { EmailCodeType } from '@/types/email/email-code-type.types'
import type { LoginUser } from '@/types/user/login-user.types'
import logger from '@/utils/log/log'
import localStorage from '@/utils/storage/local-storage'
import message from '@/utils/ui/message'

// 定义组件 Props
const props = withDefaults(defineProps<LoginPageProps>(), {
  defaultLoginMode: 'phone',
  showRegisterForm: false,
})

// 定义组件 Emits
const emit = defineEmits<LoginPageEmits>()

// 页面状态管理
const pageState = ref<LoginPageState>({
  isFlipped: props.showRegisterForm,
  loginMode: props.defaultLoginMode,
  loginLoading: false,
  showForgotPassword: false,
})

// 登录表单数据
const loginForm = ref<LoginFormData>({
  phone: '',
  email: '',
  emailCode: '',
  password: '',
})

// 注册表单数据
const registerForm = ref<RegisterFormData>({
  username: '',
  phone: '',
  email: '',
  emailCode: '',
  password: '',
  reenteredPassword: '',
  job: '',
})

// 邮箱验证码状态
const emailCodeState = ref<EmailCodeState>({
  sendCodeDisabled: false,
  sendCodeText: '发送验证码',
  countdown: 0,
})

// Turnstile 验证状态
const turnstileState = ref<TurnstileState>({
  loginTurnstileToken: '',
  registerTurnstileToken: '',
})

// 组件引用
const loginFormRef = ref()
const registerFormRef = ref()

/**
 * 翻转卡片，在登录和注册表单之间切换
 */
const flipCard = (): void => {
  pageState.value.isFlipped = !pageState.value.isFlipped
}

/**
 * 处理 Turnstile 验证成功
 * @param token - 验证成功后返回的令牌
 */
const handleTurnstileSuccess = (token: string): void => {
  logger.debug('Turnstile 验证成功:', token)
  // 根据当前表单状态保存 token
  if (!pageState.value.isFlipped) {
    turnstileState.value.loginTurnstileToken = token
  } else {
    turnstileState.value.registerTurnstileToken = token
  }
}

/**
 * 处理 Turnstile 验证失败
 */
const handleTurnstileError = (): void => {
  logger.debug('Turnstile 验证失败')
  // 清空对应的 token
  if (!pageState.value.isFlipped) {
    turnstileState.value.loginTurnstileToken = ''
  } else {
    turnstileState.value.registerTurnstileToken = ''
  }
  message.warning('验证失败，请重试')
}

/**
 * 判断登录邮箱是否有效
 */
const isValidLoginEmail = computed((): boolean => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailPattern.test(loginForm.value.email)
})

/**
 * 判断注册邮箱是否有效
 */
const isValidRegisterEmail = computed((): boolean => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailPattern.test(registerForm.value.email)
})

/**
 * 判断登录表单的 Turnstile 是否已验证
 */
const isLoginTurnstileVerified = computed((): boolean => {
  return !!turnstileState.value.loginTurnstileToken
})

/**
 * 判断注册表单的 Turnstile 是否已验证
 */
const isRegisterTurnstileVerified = computed((): boolean => {
  return !!turnstileState.value.registerTurnstileToken
})

/**
 * 监听登录模式变化
 * 当登录模式改变时，清空表单数据和重置验证码状态
 */
watch(
  () => pageState.value.loginMode,
  (): void => {
    // 清空表单数据
    loginForm.value = {
      phone: '',
      email: '',
      emailCode: '',
      password: '',
    }
    // 重置验证码发送状态
    emailCodeState.value = {
      sendCodeDisabled: false,
      sendCodeText: '发送验证码',
      countdown: 0,
    }
  },
)

/**
 * 发送邮箱验证码
 * @param type - 验证码类型（登录或注册）
 */
const sendEmailCode = async (type: EmailCodeType): Promise<void> => {
  let email = ''
  let isTurnstileVerified = false

  // 根据类型获取对应的邮箱和验证状态
  if (type === EmailCodeType.LOGIN) {
    email = loginForm.value.email
    isTurnstileVerified = isLoginTurnstileVerified.value
  } else if (type === EmailCodeType.REGISTER) {
    email = registerForm.value.email
    isTurnstileVerified = isRegisterTurnstileVerified.value
  }

  // 验证邮箱地址
  if (!email) {
    message.warning('请先输入邮箱地址')
    return
  }

  // 验证 Turnstile 状态
  if (!isTurnstileVerified) {
    message.warning('请先通过验证哦~')
    return
  }

  // 防止重复发送
  if (emailCodeState.value.sendCodeDisabled) {
    return
  }

  try {
    emailCodeState.value.sendCodeDisabled = true
    const response = await authApi.sendEmailCode({
      email,
      type,
    })

    if (response.success) {
      message.success('验证码发送成功，请查收邮件')
      startCountdown()
    } else {
      message.error(response.message || '验证码发送失败')
      emailCodeState.value.sendCodeDisabled = false
    }
  } catch {
    // 发送失败时重置 Turnstile 验证
    if (type === EmailCodeType.LOGIN) {
      turnstileState.value.loginTurnstileToken = ''
      loginFormRef.value?.resetTurnstile()
    } else {
      turnstileState.value.registerTurnstileToken = ''
      registerFormRef.value?.resetTurnstile()
    }
    emailCodeState.value.sendCodeDisabled = false
  }
}

/**
 * 开始验证码发送倒计时
 * 60秒倒计时，期间禁用发送按钮
 */
const startCountdown = (): void => {
  emailCodeState.value.countdown = 60
  emailCodeState.value.sendCodeText = `${emailCodeState.value.countdown}s后重发`

  const timer = setInterval(() => {
    emailCodeState.value.countdown--
    if (emailCodeState.value.countdown > 0) {
      emailCodeState.value.sendCodeText = `${emailCodeState.value.countdown}s后重发`
    } else {
      emailCodeState.value.sendCodeText = '发送验证码'
      emailCodeState.value.sendCodeDisabled = false
      clearInterval(timer)
    }
  }, 1000)
}

/**
 * 处理登录操作
 * 验证表单后执行登录请求
 */
const handleLogin = (): void => {
  const formRef = loginFormRef.value
  if (!formRef) return

  formRef.validate((error: boolean) => {
    if (!error) {
      const cftt = turnstileState.value.loginTurnstileToken
      if (!cftt) {
        message.warning('请先通过验证哦~')
        return
      }

      pageState.value.loginLoading = true

      // 构建登录参数
      let loginParams: LoginParams = {
        cftt: cftt,
      }

      if (pageState.value.loginMode === 'phone') {
        loginParams = {
          cftt: cftt,
          phone: loginForm.value.phone,
          password: loginForm.value.password,
        }
      } else if (pageState.value.loginMode === 'email') {
        loginParams = {
          cftt: cftt,
          email: loginForm.value.email,
          emailCode: loginForm.value.emailCode,
        }
      }

      authApi
        .login(loginParams)
        .then((res) => {
          if (res?.data) {
            // TODO: 修复API响应类型与LoginUser类型的不匹配问题
            localStorage.setLoginUser(res.data as unknown as LoginUser)
            // 设置为true，确保默认显示文字
            localStorage.set(HOME_CARD, true)
            router.push('/')

            // 触发登录成功事件
            emit('login-success', res.data)
          }
        })
        .catch(() => {
          // 登录失败时重置 Turnstile 验证
          turnstileState.value.loginTurnstileToken = ''
          loginFormRef.value?.resetTurnstile()
        })
        .finally(() => {
          pageState.value.loginLoading = false
        })
    }
  })
}

/**
 * 处理注册操作
 * 验证表单后执行注册请求
 */
const handleRegister = (): void => {
  const formRef = registerFormRef.value
  if (!formRef) return

  formRef.validate((error: boolean) => {
    if (!error) {
      const cftt = turnstileState.value.registerTurnstileToken
      if (!cftt) {
        message.warning('请先通过验证哦~')
        return
      }

      authApi
        .register({
          ...registerForm.value,
          cftt: cftt,
          confirmPassword: registerForm.value.reenteredPassword,
        })
        .then((res) => {
          if (res.data) {
            // TODO: 修复API响应类型与LoginUser类型的不匹配问题
            localStorage.setLoginUser(res.data as unknown as LoginUser)
            // 设置为true，确保默认显示文字
            localStorage.set(HOME_CARD, true)
            router.push('/')

            // 触发注册成功事件
            emit('register-success', res.data)
          }
        })
        .catch(() => {
          // 注册失败时重置 Turnstile 验证
          turnstileState.value.registerTurnstileToken = ''
          registerFormRef.value?.resetTurnstile()
        })
    }
  })
}

/**
 * 处理忘记密码成功
 * 切换到手机号登录模式
 */
const handleForgotPasswordSuccess = (): void => {
  // 切换到手机号登录模式
  pageState.value.loginMode = 'phone'

  // 触发忘记密码成功事件
  emit('forgot-password-success')
}

/**
 * 监听登录模式变化，清空表单验证
 */
watch(
  () => pageState.value.loginMode,
  (): void => {
    const formRef = loginFormRef.value
    if (formRef) {
      formRef.restoreValidation()
    }
    // 重置 Turnstile 验证
    turnstileState.value.loginTurnstileToken = ''
    loginFormRef.value?.resetTurnstile()
  },
)

/**
 * 动态计算卡片样式
 * 根据翻转状态调整卡片的变换效果
 */
const cardStyle = computed(() => {
  return {
    width: '23rem',
    transition: 'transform 0.6s, box-shadow 0.3s',
    transformStyle: 'preserve-3d',
    transform: pageState.value.isFlipped ? 'rotateY(180deg)' : 'none',
    backgroundColor: 'var(--creamy-white-1)',
    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
    backdropFilter: 'blur(5px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    opacity: 0.8,
  }
})

/**
 * 切换到登录模式
 * 暴露给父组件的方法
 */
const switchToLogin = (): void => {
  pageState.value.isFlipped = false
}

/**
 * 切换到注册模式
 * 暴露给父组件的方法
 */
const switchToRegister = (): void => {
  pageState.value.isFlipped = true
}

/**
 * 重置所有表单
 * 暴露给父组件的方法
 */
const resetAllForms = (): void => {
  // 重置登录表单
  loginForm.value = {
    phone: '',
    email: '',
    emailCode: '',
    password: '',
  }

  // 重置注册表单
  registerForm.value = {
    username: '',
    phone: '',
    email: '',
    emailCode: '',
    password: '',
    reenteredPassword: '',
    job: '',
  }

  // 重置验证码状态
  emailCodeState.value = {
    sendCodeDisabled: false,
    sendCodeText: '发送验证码',
    countdown: 0,
  }

  // 重置 Turnstile 状态
  turnstileState.value = {
    loginTurnstileToken: '',
    registerTurnstileToken: '',
  }

  // 重置表单验证
  loginFormRef.value?.restoreValidation()
  registerFormRef.value?.restoreValidation()
  loginFormRef.value?.resetTurnstile()
  registerFormRef.value?.resetTurnstile()
}

// 暴露组件方法给父组件
defineExpose<LoginPageExpose>({
  switchToLogin,
  switchToRegister,
  resetAllForms,
})
</script>
<style lang="scss" scoped>
@use '@/styles/layout/login-view';
</style>
