<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{subjectPrefix} - 邮箱验证码</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            line-height: 1.6;
            color: #1a202c;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        .logo {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }
        .title {
            font-size: 18px;
            margin: 0;
            font-weight: 400;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        .content {
            padding: 50px 40px;
        }
        .greeting {
            font-size: 20px;
            margin-bottom: 24px;
            color: #2d3748;
            font-weight: 500;
        }
        .operation-text {
            font-size: 16px;
            margin-bottom: 40px;
            color: #4a5568;
            line-height: 1.7;
        }
        .code-container {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 32px;
            text-align: center;
            margin: 40px 0;
            position: relative;
            overflow: hidden;
        }
        .code-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(79, 70, 229, 0.05), transparent);
            animation: shimmer 3s infinite;
        }
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        .code-label {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 16px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            z-index: 1;
        }
        .verification-code {
            font-size: 36px;
            font-weight: 800;
            color: #4f46e5;
            letter-spacing: 8px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 1;
        }
        .info-box {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #bae6fd;
            border-radius: 12px;
            padding: 24px;
            margin: 32px 0;
        }
        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            font-size: 15px;
            color: #0c4a6e;
        }
        .info-item:last-child {
            margin-bottom: 0;
        }
        .icon {
            margin-right: 12px;
            font-size: 18px;
            width: 24px;
            text-align: center;
        }
        .warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            color: #92400e;
            padding: 20px;
            border-radius: 12px;
            margin-top: 32px;
            font-size: 14px;
            text-align: center;
            font-weight: 500;
        }
        .footer {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 32px 40px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        .company-name {
            font-size: 18px;
            font-weight: 700;
            color: #4f46e5;
            margin-bottom: 8px;
        }
        .website {
            color: #64748b;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: color 0.2s ease;
        }
        .website:hover {
            color: #4f46e5;
        }
        @media (max-width: 640px) {
            body {
                padding: 10px;
            }
            .content {
                padding: 30px 20px;
            }
            .header {
                padding: 30px 20px;
            }
            .footer {
                padding: 24px 20px;
            }
            .verification-code {
                font-size: 28px;
                letter-spacing: 4px;
            }
            .code-container {
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">{subjectPrefix}</div>
            <h1 class="title">邮箱验证码</h1>
        </div>

        <div class="content">
            <div class="greeting">您好！👋</div>

            <div class="operation-text">
                您正在进行<strong>【{operation}】</strong>操作，为了确保账户安全，请使用以下验证码完成身份验证：
            </div>

            <div class="code-container">
                <div class="code-label">您的验证码</div>
                <div class="verification-code">{code}</div>
            </div>

            <div class="info-box">
                <div class="info-item">
                    <span class="icon">⏰</span>
                    <span>验证码有效期：<strong>{codeExpireMinutes}分钟</strong></span>
                </div>
                <div class="info-item">
                    <span class="icon">🔒</span>
                    <span>为保护您的账户安全，请勿将验证码分享给任何人</span>
                </div>
                <div class="info-item">
                    <span class="icon">📱</span>
                    <span>请在相应页面输入此验证码以继续操作</span>
                </div>
            </div>

            <div class="warning">
                🚨 如果这不是您本人的操作，请立即忽略此邮件并检查账户安全。
            </div>
        </div>

        <div class="footer">
            <div class="company-name">{subjectPrefix} 团队</div>
            <a href="{websiteDomain}" class="website">{websiteDomain}</a>
        </div>
    </div>
</body>
</html>
