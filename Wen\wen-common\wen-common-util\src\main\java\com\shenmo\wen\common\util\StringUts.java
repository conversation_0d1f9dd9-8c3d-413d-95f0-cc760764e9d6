package com.shenmo.wen.common.util;

import com.google.common.base.CaseFormat;
import com.shenmo.wen.common.constant.StringConstant;
import com.shenmo.wen.common.exception.BaseException;
import com.shenmo.wen.common.exception.enumeration.BaseExceptionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class StringUts {


    /**
     * 模糊匹配
     *
     * @param key     匹配的键
     * @param element 匹配的列表
     * @return 是否匹配成功
     * <AUTHOR>
     */
    public static boolean fuzzyMatch(String key, String... element) {

        for (String ignoreName : element) {
            if (ignoreName.startsWith("*") && ignoreName.endsWith("*")) {
                if (key.contains(ignoreName.substring(1, ignoreName.length() - 1))) {
                    return true;
                }
            } else if (ignoreName.startsWith("*")) {
                if (key.endsWith(ignoreName.substring(1))) {
                    return true;
                }
            } else if (ignoreName.endsWith("*")) {
                if (key.startsWith(ignoreName.substring(0, ignoreName.length() - 1))) {
                    return true;
                }
            } else if (ignoreName.equals(key)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查找指定字符串是否匹配指定字符串列表中的任意一个字符串
     *
     * @param str        指定字符串
     * @param stringList 需要检查的字符串数组
     * @return 是否匹配
     * <AUTHOR>
     */
    public static boolean matches(String str, List<String> stringList) {
        if (isBlank(str) || CollectionUtils.isEmpty(stringList)) {
            return false;
        }
        for (String pattern : stringList) {
            if (isMatch(pattern, str)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断url是否与规则配置:
     * ? 表示单个字符;
     * * 表示一层路径内的任意字符串，不可跨层级;
     * ** 表示任意层路径;
     *
     * @param pattern 匹配规则
     * @param url     需要匹配的url
     * @return 是否匹配
     * <AUTHOR>
     */
    public static boolean isMatch(String pattern, String url) {
        AntPathMatcher matcher = new AntPathMatcher();
        return matcher.match(pattern, url);
    }

    /**
     * 为字符串中每个字符前拼接上指定字符
     *
     * @param source 原字符串
     * @param c      指定字符
     * @return 拼接后的字符串
     * <AUTHOR>
     */
    public static String appendChar(String source, final char c) {

        if (isBlank(source)) {
            return source;
        }
        final StringBuilder sb = new StringBuilder();
        for (int i = 0; i < source.length(); i++) {

            sb.append(c).append(source.charAt(i));
        }
        return sb.toString();
    }

    /**
     * 大驼峰转小驼峰
     *
     * @param str 字符串
     * @return 小驼峰字符串
     * <AUTHOR>
     */
    public static String upperToLower(String str) {

        return CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_CAMEL, str);
    }

    /**
     * 小驼峰转下划线
     *
     * @param str 字符串
     * @return 小驼峰字符串
     * <AUTHOR>
     */
    public static String lowerToUnderscore(String str) {
        return CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, str);
    }

    /**
     * 获取url参数map
     *
     * @param url url字符串
     * @return url参数map
     * <AUTHOR>
     */
    public static Map<String, String> getUrlParams(String url) {
        if (!url.contains(StringConstant.QUESTION_MARK)) {
            return Collections.emptyMap();
        }
        String[] urlParts = url.split("\\?");
        //有参数
        String[] params = urlParts[1].split("&");
        Map<String, String> hashMap = new HashMap<>(params.length);
        for (String param : params) {
            String[] keyValue = param.split("=");
            hashMap.put(keyValue[0], keyValue[1]);
        }
        return hashMap;
    }

    /**
     * 判断字符串是否是"{"、"["开头，以"}"、"]"结束的字符串
     *
     * @param value 字符串
     * @return 是否是"{"、"["开头，以"}"、"]"结束的字符串
     * <AUTHOR>
     */
    public static boolean isJsonStartEnd(@NonNull String value) {
        return isJsonObjectStartEnd(value) || isJsonArrayStartEnd(value);
    }

    /**
     * 判断字符串是否是"{"开头，以"}"结束的字符串
     *
     * @param value 字符串
     * @return 判断字符串是否是"{"开头，以"}"结束的字符串
     * <AUTHOR>
     */
    public static boolean isJsonObjectStartEnd(@NonNull String value) {
        if (isBlank(value)) {
            return false;
        }
        return startsWith(value, StringConstant.LEFT_BRACE) && endsWith(value, StringConstant.RIGHT_BRACE);
    }

    /**
     * 判断字符串是否是"["开头，以"]"结束的字符串
     *
     * @param value 字符串
     * @return 是否是"["开头，以"]"结束的字符串
     * <AUTHOR>
     */
    public static boolean isJsonArrayStartEnd(@NonNull String value) {
        if (isBlank(value)) {
            return false;
        }
        return startsWith(value, StringConstant.LEFT_SQ_BRACKET) && endsWith(value, StringConstant.RIGHT_SQ_BRACKET);
    }

    /**
     * 获取Utf8字符编码的字符串
     *
     * @param value 字符串值
     * @return Utf8字符编码的字符串
     * <AUTHOR>
     */
    public static String fromUtf8(String value) {

        return fromUtf8(value, HttpServletUtils.getRequest().getCharacterEncoding());
    }

    /**
     * 获取Utf8字符编码的字符串
     *
     * @param value         字符串值
     * @param originCharset 源编码
     * @return Utf8字符编码的字符串
     * <AUTHOR>
     */
    public static String fromUtf8(String value, String originCharset) {

        return fromCharsetEncoding(value, originCharset, StandardCharsets.UTF_8.displayName());
    }

    /**
     * 获取指定字符编码的字符串
     *
     * @param value         字符串值
     * @param targetCharset 目标编码
     * @return 指定字符编码的字符串
     * <AUTHOR>
     */
    public static String fromCharsetEncoding(String value, String targetCharset) {

        return fromCharsetEncoding(value, HttpServletUtils.getRequest().getCharacterEncoding(), targetCharset);
    }

    /**
     * 获取指定字符编码的字符串
     *
     * @param value         字符串值
     * @param originCharset 源编码
     * @param targetCharset 目标编码
     * @return 指定字符编码的字符串
     * <AUTHOR>
     */
    public static String fromCharsetEncoding(String value, String originCharset, String targetCharset) {

        try {
            return isAnyBlank(value, originCharset, targetCharset) ? value : new String(value.getBytes(originCharset), targetCharset);
        } catch (UnsupportedEncodingException e) {

            log.error("获取指定字符编码的字符串失败", e);
            throw new BaseException(BaseExceptionEnum.UN_SUPPORTED);
        }
    }

    /**
     * 小写首字母<br>
     * 例如：str = Name, return name
     *
     * @param str 字符串
     * @return 字符串
     */
    public static String lowerFirst(String str) {
        if (null == str) {
            return null;
        }
        if (!str.isEmpty()) {
            char firstChar = str.charAt(0);
            if (Character.isUpperCase(firstChar)) {
                return Character.toLowerCase(firstChar) + substring(str, 1);
            }
        }
        return str;
    }
}
