import type { ImageClickCallback } from '@/types/image/image-click-callback.types'

import type { UseImagePreviewDragStateReturn } from './useImagePreviewDragState'
import type { UseImagePreviewDragTransformReturn } from './useImagePreviewDragTransform'

/**
 * 图片预览拖拽鼠标事件管理组合式函数返回值类型
 */
export interface UseImagePreviewDragMouseReturn {
  /** 鼠标拖拽开始 */
  handleMouseDown: (e: MouseEvent) => void
  /** 鼠标拖拽移动 */
  handleMouseMove: (e: MouseEvent) => void
  /** 鼠标拖拽结束 */
  handleMouseUp: () => void
  /** 添加鼠标事件监听器 */
  addMouseEventListeners: () => void
  /** 移除鼠标事件监听器 */
  removeMouseEventListeners: () => void
}

/**
 * 图片预览拖拽鼠标事件管理组合式函数
 * 提供鼠标拖拽功能
 */
export function useImagePreviewDragMouse(
  imageElement: HTMLImageElement,
  stateReturn: UseImagePreviewDragStateReturn,
  transformReturn: UseImagePreviewDragTransformReturn,
  onImageClick?: ImageClickCallback,
): UseImagePreviewDragMouseReturn {
  const { state, dragVars } = stateReturn
  const { updateTransform } = transformReturn

  /**
   * 鼠标拖拽开始
   */
  const handleMouseDown = (e: MouseEvent): void => {
    if (e.button !== 0) return // 只处理左键
    e.preventDefault()
    e.stopPropagation()

    state.isDragging = true
    dragVars.hasDragged = false // 重置拖拽标记
    dragVars.dragStartX = e.clientX
    dragVars.dragStartY = e.clientY
    dragVars.startTranslateX = state.translateX
    dragVars.startTranslateY = state.translateY

    document.addEventListener('mousemove', handleMouseMove, { passive: false })
    document.addEventListener('mouseup', handleMouseUp, { passive: true })
  }

  /**
   * 鼠标拖拽移动
   */
  const handleMouseMove = (e: MouseEvent): void => {
    if (!state.isDragging) return
    e.preventDefault()

    const deltaX = e.clientX - dragVars.dragStartX
    const deltaY = e.clientY - dragVars.dragStartY

    // 检查是否超过拖拽阈值
    if (
      !dragVars.hasDragged &&
      (Math.abs(deltaX) > dragVars.dragThreshold || Math.abs(deltaY) > dragVars.dragThreshold)
    ) {
      dragVars.hasDragged = true
      imageElement.style.cursor = 'grabbing'
      imageElement.classList.add('dragging')
    }

    if (dragVars.hasDragged) {
      state.translateX = dragVars.startTranslateX + deltaX
      state.translateY = dragVars.startTranslateY + deltaY
      updateTransform()
    }
  }

  /**
   * 鼠标拖拽结束
   */
  const handleMouseUp = (): void => {
    const wasClick = !dragVars.hasDragged // 如果没有拖拽，则认为是点击

    state.isDragging = false
    imageElement.style.cursor = 'grab'
    imageElement.classList.remove('dragging')
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)

    // 如果是点击（没有拖拽），触发点击回调
    if (wasClick && onImageClick) {
      onImageClick()
    }
  }

  /**
   * 添加鼠标事件监听器
   */
  const addMouseEventListeners = () => {
    imageElement.addEventListener('mousedown', handleMouseDown, { passive: false })
    imageElement.style.cursor = 'grab'
  }

  /**
   * 移除鼠标事件监听器
   */
  const removeMouseEventListeners = () => {
    imageElement.removeEventListener('mousedown', handleMouseDown)
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  return {
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    addMouseEventListeners,
    removeMouseEventListeners,
  }
}
