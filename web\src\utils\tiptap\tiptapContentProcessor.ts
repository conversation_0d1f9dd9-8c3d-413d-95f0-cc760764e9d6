import {
  extractRelativePath,
  getFullImageUrl,
} from '@/components/tiptap/extensions/image/ImageResourceManager'
import Json from '@/utils/data/json'
import logger from '@/utils/log/log'

import type { JSONContent } from '@tiptap/vue-3'

/**
 * TipTap内容处理工具
 * 提供内容转换、图片URL处理等功能
 */
export const tiptapContentProcessor = {
  /**
   * 替换内容中的图片URL
   * @param content 要处理的内容
   * @param domain 是否使用完整域名URL
   */
  replaceImageUrls: (content: JSONContent, domain: boolean = false) => {
    content?.content?.forEach((node: JSONContent) => {
      // 检查是否是图片节点
      if (node.type === 'image' && node.attrs && node.attrs.src) {
        // 根据domain参数决定是替换为完整URL还是相对路径
        if (domain) {
          // 使用完整URL（只在显示时需要）
          node.attrs.src = getFullImageUrl(node.attrs.src, false)
        } else {
          // 转换为相对路径（保存或其他场景使用）
          node.attrs.src = extractRelativePath(node.attrs.src)
        }
      }
      // 递归遍历子节点
      if (node.content) {
        tiptapContentProcessor.replaceImageUrls(node, domain)
      }
    })
  },

  /**
   * 将JSONContent转换为JSON字符串
   * @param content 要转换的内容
   * @returns JSON字符串
   */
  toJsonString: (content: JSONContent): string => {
    // 在转为JSON字符串前处理内容中的图片URL
    const contentCopy = JSON.parse(JSON.stringify(content)) // 深拷贝避免修改原对象

    // 递归处理图片节点
    const processImageUrls = (node: JSONContent) => {
      if (!node) return

      // 处理图片节点
      if (node.type === 'image' && node.attrs && node.attrs.src) {
        const originalSrc = node.attrs.src

        // 检查是否需要恢复缩略图：如果DOM中的图片显示原图但原始src是缩略图
        const shouldRestoreThumbnail = (() => {
          if (originalSrc.includes('/thumbnail')) return false // 已经是缩略图

          // 查找对应的DOM图片元素
          const imgElements = document.querySelectorAll('img[data-relative-src]')
          for (const img of imgElements) {
            const relativeSrc = (img as HTMLImageElement).dataset.relativeSrc
            if (
              relativeSrc &&
              relativeSrc.includes('/thumbnail') &&
              relativeSrc.replace('/thumbnail', '') === originalSrc
            ) {
              return true // 找到对应的缩略图源
            }
          }
          return false
        })()

        if (shouldRestoreThumbnail) {
          // 恢复为缩略图路径
          node.attrs.src = '/thumbnail' + originalSrc
          logger.debug('Restored thumbnail path:', {
            original: originalSrc,
            thumbnail: node.attrs.src,
          })
        } else {
          // 正常的路径转换
          node.attrs.src = extractRelativePath(originalSrc)
          logger.debug('Image URL transformed:', {
            original: originalSrc,
            transformed: node.attrs.src,
          })
        }
      }

      // 递归处理子节点
      if (node.content && Array.isArray(node.content)) {
        node.content.forEach(processImageUrls)
      }
    }

    // 处理所有节点
    processImageUrls(contentCopy)

    return Json.stringify(contentCopy)
  },

  /**
   * 将JSON字符串转换为JSONContent对象
   * 增强容错处理，防止无效JSON导致解析失败
   * @param content 要解析的JSON字符串
   * @returns JSONContent对象
   */
  toJsonObject: (content: string): JSONContent => {
    if (!content) {
      logger.warn('Empty content passed to toJsonObject')
      return { type: 'doc', content: [{ type: 'paragraph', content: [] }] }
    }

    try {
      // 首先检查是否已经是JSONContent对象
      if (typeof content === 'object') {
        return content as JSONContent
      }

      // 尝试解析JSON字符串
      const parsed = Json.parse(content) as JSONContent

      // 如果解析失败，返回一个基本的空文档结构
      if (!parsed) {
        logger.warn('Failed to parse content in toJsonObject, creating fallback structure')
        return {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              content: [
                {
                  type: 'text',
                  text: typeof content === 'string' ? content : '无法显示内容',
                },
              ],
            },
          ],
        }
      }

      return parsed
    } catch (error) {
      logger.error('Error in toJsonObject:', error as Error)
      // 返回一个包含原文本的基本结构
      return {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: typeof content === 'string' ? content : '解析错误',
              },
            ],
          },
        ],
      }
    }
  },

  /**
   * 序列化内容为纯文本
   * @param content 要序列化的内容
   * @returns 纯文本字符串
   */
  serializeContent: (content: JSONContent | null): string => {
    let result = ''
    if (!content) return result

    // Helper function to process nodes recursively
    function processNode(node: JSONContent) {
      if (node.type === 'text') {
        // Append text content
        result += node.text
      } else if (node.type === 'mention') {
        // Append mention label
        result += '@' + node.attrs?.label
      } else if (node.type === 'image') {
        // Append [图片]
        result += '[图片]'
      } else if (node.content && Array.isArray(node.content)) {
        // Recursively process child nodes
        node.content.forEach((child: JSONContent) => processNode(child))
      }
    }

    // Start processing from the root content
    processNode(content)
    return result
  },
}
