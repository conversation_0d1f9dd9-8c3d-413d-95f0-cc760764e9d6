/**
 * 弹幕媒体相关类型定义
 */

/**
 * 图片预览配置接口
 */
export interface ImagePreviewConfig {
  /** 最小加载时间（毫秒） */
  MIN_LOADING_TIME: number
  /** 加载时的透明度 */
  LOADING_OPACITY: number
  /** 完全显示时的透明度 */
  FULL_OPACITY: number
  /** 拖拽配置 */
  DRAG_CONFIG: {
    minScale: number
    maxScale: number
    scaleStep: number
  }
}

/**
 * 图片数据接口
 */
export interface ImageData {
  /** 是否已经是原图 */
  isOriginal: boolean
  /** 缩略图地址 */
  thumbnailSrc: string
  /** 是否为外部图片 */
  isExternal: boolean
}

/**
 * 扩展的图片元素接口
 */
export interface ExtendedImageElement extends HTMLImageElement {
  dataset: {
    isOriginal?: string
    thumbnailSrc?: string
    originalSrc?: string
    originalFullUrl?: string
  }
}

/**
 * 图片预览事件类型
 */
export type ImagePreviewEvent = 'image-preview-open' | 'image-preview-close'

/**
 * 图片预览发射器类型
 */
export type ImagePreviewEmitter = (event: ImagePreviewEvent) => void
