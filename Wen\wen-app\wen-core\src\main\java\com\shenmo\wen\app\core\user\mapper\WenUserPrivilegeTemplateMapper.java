package com.shenmo.wen.app.core.user.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilegeTemplate;

public interface WenUserPrivilegeTemplateMapper extends BaseMapper<WenUserPrivilegeTemplate> {

    default WenUserPrivilegeTemplate byId(Long id) {
        return selectOne(Wrappers.<WenUserPrivilegeTemplate>lambdaQuery().eq(WenUserPrivilegeTemplate::getId, id));
    }

    default WenUserPrivilegeTemplate byName(String name) {
        return selectOne(Wrappers.<WenUserPrivilegeTemplate>lambdaQuery().eq(WenUserPrivilegeTemplate::getName, name));
    }

    default List<WenUserPrivilegeTemplate> searchByNameLike(String name) {
        return selectList(Wrappers.<WenUserPrivilegeTemplate>lambdaQuery()
                .like(WenUserPrivilegeTemplate::getName, name)
                .orderBy(true, true, WenUserPrivilegeTemplate::getName));
    }

    @Select("SELECT id FROM wen_user_privilege_template WHERE denomination <= #{denomination} ORDER BY denomination DESC")
    List<Long> listIdsByLeDenomination(@Param("denomination") Integer denomination);

    /**
     * 根据模板ID列表批量查询模板信息
     *
     * @param templateIds 模板ID列表
     * @return 模板信息列表
     */
    default List<WenUserPrivilegeTemplate> listByIds(List<Long> templateIds) {
        if (templateIds == null || templateIds.isEmpty()) {
            return List.of();
        }
        return selectList(Wrappers.<WenUserPrivilegeTemplate>lambdaQuery()
                .in(WenUserPrivilegeTemplate::getId, templateIds));
    }
}
