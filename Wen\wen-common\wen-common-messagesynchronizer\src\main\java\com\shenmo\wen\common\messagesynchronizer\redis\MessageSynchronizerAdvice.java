package com.shenmo.wen.common.messagesynchronizer.redis;

import com.shenmo.wen.common.constant.StringConstant;
import com.shenmo.wen.common.messagesynchronizer.*;
import com.shenmo.wen.common.util.spring.SpelUtils;
import com.shenmo.wen.common.util.spring.SpringAopUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

import java.lang.reflect.Method;
import java.util.Deque;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 消息同步器增强
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@RequiredArgsConstructor
public class MessageSynchronizerAdvice implements InitializingBean {

    /**
     * 单实例同步map
     */
    private final ConcurrentHashMap<String, MessageSynchronizerMethod> singleInstanceSyncMap = new ConcurrentHashMap<>();

    private final RedisConnectionFactory connectionFactory;
    private final RedisTemplate<String, MessageData<MessageSynchronizerMethod>> redisTemplate;
    private final MessageSynchronizerListener messageSynchronizerListener;


    @Override
    public void afterPropertiesSet() {
        RedisMessageListenerContainerFactory.of(connectionFactory).start(messageSynchronizerListener);
    }

    /**
     * 对@MessagePublishListener注解标注的方法进行环绕增强
     * <p>
     * {@link MessageSynchronizer}
     * <p>
     * {@link ProceedingJoinPoint}
     *
     * @param pjp aop环绕连接点
     * @return 执行结果
     * @throws Throwable {@link ProceedingJoinPoint#proceed(Object[])}可能抛出的异常
     * <AUTHOR>
     */
    @Around("@annotation(com.shenmo.wen.common.messagesynchronizer.MessageSynchronizer)")
    public Object messagePublishListener(ProceedingJoinPoint pjp) throws Throwable {
        final Object[] args = pjp.getArgs();
        final MessageSynchronizer messageSynchronizer = SpringAopUtils.getCurrentMethodAnnotation(pjp, MessageSynchronizer.class);
        final Method method = SpringAopUtils.acquireCurrentMethod(pjp, pjp.getTarget().getClass());
        String id = messageSynchronizer.value();
        id = String.valueOf(SpelUtils.getMethodArgValue(id, pjp.getTarget(), method, args));
        final String channel = messageSynchronizerListener.getChannel();
        id = channel + StringConstant.COLON + id;
        final MessageSynchronizerMethod messageSynchronizerMethod = new MessageSynchronizerMethod();
        messageSynchronizerMethod.setSource(AopUtils.getTargetClass(pjp.getThis()));
        messageSynchronizerMethod.setMethodName(method.getName());
        messageSynchronizerMethod.setParameterTypes(method.getParameterTypes());
        final MessageData<MessageSynchronizerMethod> md = DefaultMessageData.of(id, messageSynchronizerMethod, args);
        // 本地队列过滤
        final Deque<String> messageIdDeque = AbstractRedisMessageListener.getMessageIdDeque();
        if (messageIdDeque.contains(id)) {

            messageIdDeque.remove(id);
        } else {
            messageIdDeque.addLast(id);
            // redis message send
            redisTemplate.convertAndSend(channel, md);
        }
        log.info("Publish message: {}", ToStringBuilder.reflectionToString(md));
        final boolean singleInstanceSync = messageSynchronizer.singleInstanceLock();
        final boolean syncWait = messageSynchronizer.syncWait();
        try {
            singleInstanceSyncMap.put(id, messageSynchronizerMethod);
            if (singleInstanceSync) {
                if (syncWait) {
                    synchronized (singleInstanceSyncMap.get(id)) {
                        return pjp.proceed(pjp.getArgs());
                    }
                } else {
                    if (!method.getReturnType().equals(Void.TYPE)) {
                        log.warn("The parameter 'MessagePublishListener#syncWait' is false, and yet lock is currently used in the non void method, maybe occur 'NullPointException'");
                    }
                    return null;
                }
            } else {
                return pjp.proceed();
            }
        } finally {
            singleInstanceSyncMap.remove(id);
        }
    }
}
