import { type Ref } from 'vue'

import { type Article } from '@/types/article/article.types'

/**
 * 文章时间组合式函数返回值类型
 */
interface UseArticleTimeReturn {
  /** 切换时间显示格式 */
  toggleTimeFormat: (type: 'publish' | 'modify') => void
}

/**
 * 文章时间管理组合式函数
 * 提供文章时间显示格式的切换功能
 * @param article 文章数据的响应式引用
 */
export function useArticleTime(article: Ref<Article>): UseArticleTimeReturn {
  // 切换时间显示格式
  const toggleTimeFormat = (type: 'publish' | 'modify'): void => {
    if (type === 'publish') {
      if (article.value.showExactPublishTime === undefined) {
        article.value.showExactPublishTime = true
      } else {
        article.value.showExactPublishTime = !article.value.showExactPublishTime
      }
    } else {
      if (article.value.showExactModifyTime === undefined) {
        article.value.showExactModifyTime = true
      } else {
        article.value.showExactModifyTime = !article.value.showExactModifyTime
      }
    }
  }

  return {
    toggleTimeFormat,
  }
}
