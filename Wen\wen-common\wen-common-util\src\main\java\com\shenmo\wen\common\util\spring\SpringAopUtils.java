package com.shenmo.wen.common.util.spring;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.aop.support.AopUtils;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.lang.NonNull;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Objects;

/**
 * SpringAop帮助
 *
 * <AUTHOR>
 * @see ProceedingJoinPoint
 */
public abstract class SpringAopUtils extends AopUtils {

    /**
     * 获取当前方法对象
     *
     * @param joinPoint   织入点
     * @param targetClass 被代理类
     * @return 方法对象
     * <AUTHOR>
     */
    @NonNull
    public static Method acquireCurrentMethod(JoinPoint joinPoint, Class<?> targetClass) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        try {
            final Method method = targetClass.getDeclaredMethod(methodSignature.getName(), methodSignature.getParameterTypes());
            method.setAccessible(true);
            return method;
        } catch (NoSuchMethodException e) {
            return acquireCurrentMethod(joinPoint, ClassUtils.getUserClass(targetClass));
        }
    }

    /**
     * 获取main方法所在class上的指定注解
     *
     * @param annotation 注解class
     * @return 注解
     * <AUTHOR>
     */
    public static Annotation acquireAnnotationForMainClass(Class<? extends Annotation> annotation) {

        Class<?> mainClass = ReflectUtils.acquireMainApplicationClass();
        return AnnotationUtils.findAnnotation(Objects.requireNonNull(mainClass), annotation);
    }

    /**
     * 获取当前方法上的注解
     *
     * @param joinPoint       aop连接点
     * @param annotationClass 注解class对象
     * @return 注解
     * <AUTHOR>
     */
    public static <T extends Annotation> T getCurrentMethodAnnotation(JoinPoint joinPoint, Class<T> annotationClass) {
        return SpringAopUtils.acquireCurrentMethod(joinPoint, joinPoint.getTarget().getClass()).getAnnotation(annotationClass);
    }
}
