/**
 * 文章摘要类型定义
 * 用于文章列表、搜索结果等场景的文章基本信息
 */

import type { ArticleAuthor } from './article-author.types'

/**
 * 文章摘要类型
 * 包含文章的基本信息，用于列表展示
 */
export interface ArticleSummary {
  /** 文章ID */
  id: string
  /** 文章标题 */
  title: string
  /** 文章标签 */
  tag?: string
  /** 作者信息 */
  author: ArticleAuthor
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
  /** 发布范围 */
  publishedScope: number
}
