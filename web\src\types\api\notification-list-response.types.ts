/**
 * 通知列表响应类型定义
 * 定义通知列表API的响应数据结构
 */

import type { NotificationListItem } from './notification-list-item.types'

// 重新导出NotificationListItem以保持向后兼容性
export type { NotificationListItem }

/**
 * 通知列表响应接口
 * 包含分页信息和通知数据
 */
export interface NotificationListResponse {
  /** 通知数据数组 */
  rows: NotificationListItem[]
  /** 总记录数 */
  totalRows: number
  /** 总页数 */
  totalPage: number
  /** 当前页码 */
  pageNo: number
  /** 每页记录数 */
  pageSize: number
}
