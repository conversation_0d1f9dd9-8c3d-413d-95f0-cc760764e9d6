import { useMessage } from 'naive-ui'
import { ref, computed } from 'vue'

import { HOME_SEARCH_CONDITION } from '@/constants/home/<USER>'
import type { ArticleListRef } from '@/types/component/article-list-ref.types'
import type { CommentDanmakuRef } from '@/types/component/comment-danmaku-ref.types'
import type { SearchHistoryItem } from '@/types/home/<USER>'
import type { UseHomeSearchReturn } from '@/types/home/<USER>'
import type { SearchCondition } from '@/types/search/search-condition.types'
import logger from '@/utils/log/log'
import frequencyLimit from '@/utils/performance/frequency-limit'
import localStorage from '@/utils/storage/local-storage'

import { useHomeSearchOperations } from './useHomeSearchOperations'

/**
 * 首页搜索管理组合式函数
 * 提供首页的搜索功能，包括文章搜索和评论搜索
 */
export function useHomeSearch(): UseHomeSearchReturn {
  const message = useMessage()

  // 搜索状态
  const isLoading = ref(false)
  const isSearching = ref(false)
  const currentRequestController = ref<AbortController | null>(null)

  // 统一的搜索防抖key
  const UNIFIED_SEARCH_KEY = 'unified_search'

  // 搜索历史记录
  const searchHistory = ref<SearchHistoryItem[]>([])

  // 搜索条件
  const searchCondition = ref<SearchCondition>({
    searchKey: '',
    owner: false,
    interaction: false,
    favorite: false,
    tag: '',
  })

  // 检查是否有搜索条件
  const hasSearchCondition = computed(() => {
    return Object.entries(searchCondition.value).some(([key, value]) => {
      if (key === 'tag') return !!value
      if (typeof value === 'boolean') return value
      if (key === 'searchKey') return !!value
      return false
    })
  })

  // 使用搜索操作功能
  const { searchComments, searchArticles } = useHomeSearchOperations(
    isLoading,
    isSearching,
    searchCondition,
    hasSearchCondition,
    currentRequestController,
  )

  /**
   * 获取搜索占位符文本
   * 根据当前视图类型返回相应的占位符
   * @param isCardVisible 是否为卡片视图（文章视图）
   * @returns 占位符文本
   */
  const getSearchPlaceholder = (isCardVisible: boolean): string => {
    return isCardVisible ? '感兴趣的文章' : '有意思的评论'
  }

  /**
   * 从本地存储加载搜索条件
   * 恢复用户之前保存的搜索设置
   */
  const loadSearchCondition = (): void => {
    const savedCondition = localStorage.get(HOME_SEARCH_CONDITION)
    if (savedCondition) {
      searchCondition.value = savedCondition as SearchCondition
      logger.debug('加载搜索条件:', savedCondition)
    }
  }

  /**
   * 保存搜索条件到本地存储
   * 持久化用户的搜索设置
   */
  const saveSearchCondition = (): void => {
    localStorage.set(HOME_SEARCH_CONDITION, searchCondition.value)
    logger.debug('保存搜索条件:', searchCondition.value)
  }

  /**
   * 统一搜索函数
   * 根据当前视图类型执行相应的搜索操作，包含严格的状态管理和防抖控制
   * @param isCardVisible 是否为卡片视图（文章视图）
   * @param articleListRef 文章列表组件引用
   * @param commentDanmakuRef 评论弹幕组件引用
   * @param loadMore 是否为加载更多操作
   */
  const search = (
    isCardVisible: boolean,
    articleListRef: ArticleListRef | null,
    commentDanmakuRef: CommentDanmakuRef | null,
    loadMore: boolean = false,
  ): void => {
    // 如果正在搜索中，直接返回
    if (isSearching.value) {
      logger.warn('搜索被阻止：正在进行其他搜索')
      return
    }

    // 如果正在加载中，取消之前的请求
    if (currentRequestController.value) {
      currentRequestController.value.abort()
      currentRequestController.value = null
    }

    // 创建新的请求控制器
    currentRequestController.value = new AbortController()

    // 记录搜索历史
    searchHistory.value.push({
      timestamp: Date.now(),
      condition: { ...searchCondition.value },
      type: isCardVisible ? 'article' : 'comment',
    })

    // 限制历史记录长度
    if (searchHistory.value.length > 10) {
      searchHistory.value.shift()
    }

    // 日志追踪
    logger.debug('触发搜索:', {
      isCardVisible,
      condition: searchCondition.value,
      loadMore,
    })

    // 使用统一的防抖，增加更精细的控制
    frequencyLimit.debounce(
      UNIFIED_SEARCH_KEY,
      () => {
        isSearching.value = true
        try {
          if (isCardVisible) {
            if (articleListRef) {
              searchArticles(articleListRef, loadMore)
            } else {
              logger.warn('articleListRef.value is null/undefined')
              isSearching.value = false
            }
          } else {
            if (commentDanmakuRef) {
              searchComments(commentDanmakuRef)
            } else {
              logger.warn('commentDanmakuRef.value is null/undefined')
              isSearching.value = false
            }
          }
        } catch (error) {
          logger.error('搜索过程中发生错误:', error as Error)
          message.error('搜索失败，请稍后重试')
        } finally {
          // 确保在任何情况下都重置搜索状态
          setTimeout(() => {
            isSearching.value = false
          }, 500)
        }
      },
      200, // 适当增加防抖延迟，减少不必要的搜索
    )
  }

  /**
   * 处理标签选择事件
   * 更新搜索条件中的标签并触发搜索
   * @param tagName 选中的标签名称
   * @param isCardVisible 是否为卡片视图（文章视图）
   * @param articleListRef 文章列表组件引用
   * @param commentDanmakuRef 评论弹幕组件引用
   */
  const handleTagSelected = (
    tagName: string,
    isCardVisible: boolean,
    articleListRef: ArticleListRef | null,
    commentDanmakuRef: CommentDanmakuRef | null,
  ): void => {
    // 更新搜索条件的标签
    searchCondition.value.tag = tagName

    // 日志追踪
    logger.debug('标签选择:', {
      tagName,
      isCardVisible,
      currentCondition: searchCondition.value,
    })

    // 保存搜索条件到localStorage
    saveSearchCondition()

    // 强制触发搜索，确保文章列表更新
    search(isCardVisible, articleListRef, commentDanmakuRef)
  }

  /**
   * 清理函数
   * 取消正在进行的请求并清理相关状态
   */
  const cleanup = (): void => {
    if (currentRequestController.value) {
      currentRequestController.value.abort()
      currentRequestController.value = null
    }
    // 清理搜索历史
    searchHistory.value = []
    logger.debug('清理搜索状态')
  }

  return {
    // 状态
    isLoading,
    isSearching,
    searchCondition,
    hasSearchCondition,
    searchHistory,

    // 方法
    getSearchPlaceholder,
    loadSearchCondition,
    saveSearchCondition,
    search,
    handleTagSelected,
    cleanup,
  }
}
