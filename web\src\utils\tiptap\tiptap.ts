import { tiptapContentProcessor } from './tiptapContentProcessor'
import { createExtensionMap } from './tiptapExtensions'

/**
 * TipTap编辑器工具集
 * 提供扩展配置和内容处理功能的统一接口
 */
const tiptap = {
  extensionMap: createExtensionMap(),
  replaceImageUrls: tiptapContentProcessor.replaceImageUrls,
  toJsonString: tiptapContentProcessor.toJsonString,
  toJsonObject: tiptapContentProcessor.toJsonObject,
  serializeContent: tiptapContentProcessor.serializeContent,
}
export default tiptap
