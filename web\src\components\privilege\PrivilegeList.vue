<!--
  特权列表组件
  
  功能说明：
  - 显示特权卡片列表
  - 支持无限滚动加载
  - 提供特权时间格式切换
  - 支持搜索和筛选
-->
<template>
  <div class="article-container" ref="containerRef">
    <NInfiniteScroll
      @load="loadMore"
      :distance="100"
      class="infinite-scroll-container"
      ref="scrollContainerRef"
    >
      <NRow
        :gutter="20"
        style="
          width: 100%;
          box-sizing: border-box;
          margin: 0 auto;
          padding: 0 0.25rem;
          flex: 1;
          overflow-y: auto;
        "
      >
        <NCol v-for="(privilege, index) in privileges" :key="privilege.id" :span="cardColSpan">
          <NCard
            class="card-item cursor-pointer privilege-card compact-card"
            @click="handlePrivilegeClick(privilege)"
            header-style="padding-bottom:0.125rem;border-bottom: var(--border-1);"
            :style="{ backgroundColor: getCardColor(privilege.id.toString(), index) }"
          >
            <template #header>
              <div class="article-header">
                <div class="article-title" @click.stop="handlePrivilegeClick(privilege)">
                  {{ privilege.name }}
                </div>
              </div>
            </template>

            <template #header-extra>
              <NAvatar
                round
                :size="32"
                :src="privilege.icon || '/default-privilege-icon.png'"
                object-fit="cover"
                class="article-avatar"
              />
            </template>

            <div class="flex-between-center">
              <div>
                <NTag
                  :type="privilege.verificationType === 0 ? 'info' : 'success'"
                  class="card-tag"
                >
                  {{ getVerificationTypeLabel(privilege.verificationType) }}
                </NTag>
              </div>
              <div class="privilege-expire-time">
                <NTag
                  type="warning"
                  size="small"
                  class="time-clickable"
                  @click.stop="privilegeTime.toggleTimeFormat(privilege)"
                >
                  {{
                    privilege.showExactExpireTime
                      ? privilege.exactExpireTime
                      : privilege.relativeExpireTime
                  }}
                </NTag>
              </div>
            </div>

            <div class="article-content">
              <NScrollbar style="padding-right: 0.5rem">
                <div class="privilege-description">
                  {{ privilege.description }}
                </div>
              </NScrollbar>
            </div>
          </NCard>
        </NCol>
      </NRow>
      <div class="infinite-load-info">
        <NSpin v-if="isLoading" class="display-flex" />
        <NEmpty v-if="privileges.length === 0 && !isLoading" description="暂无特权数据" />
        <NEmpty v-if="!hasMore && privileges.length > 0" description="没有更多特权了..." />
      </div>
    </NInfiniteScroll>
  </div>
</template>

<script lang="ts" setup>
import {
  NSpin,
  NTag,
  NCard,
  NScrollbar,
  NInfiniteScroll,
  NRow,
  NCol,
  NAvatar,
  NEmpty,
  useMessage,
} from 'naive-ui'
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'

import privilegeApi from '@/api/privilege'
import { usePrivilegeTime } from '@/composables/privilege/usePrivilegeTime'
import { useTheme } from '@/composables/theme/useTheme'
import { VERIFICATION_TYPE_LABELS } from '@/constants/privilege/verification-type.constants'
import type { PrivilegeResponse } from '@/types/privilege/privilege-response.types'
import type { PrivilegeSearchCondition } from '@/types/privilege/privilege-search-condition.types'
import { ThemeType } from '@/types/theme/theme.types'
import { formatRelativeTime, formatExactTime } from '@/utils/date/format'

import type { Span } from 'naive-ui/es/legacy-grid/src/interface'

const props = defineProps<{
  searchCondition: { value: PrivilegeSearchCondition }
}>()

const emit = defineEmits<{
  reset: []
  startVerification: [privilege: PrivilegeResponse]
}>()

const message = useMessage()

// 响应式状态
const privileges = ref<PrivilegeResponse[]>([])
const isLoading = ref(false)
const isLoadingMore = ref(false)
const hasMore = ref(true)
const lastId = ref<number | undefined>(undefined)

// 容器引用
const containerRef = ref<HTMLElement>()
const scrollContainerRef = ref<HTMLElement>()

// 主题相关
const { currentTheme } = useTheme()

// 卡片颜色配置
const lightColors = [
  'rgba(255, 182, 193, 0.3)', // 浅粉色
  'rgba(173, 216, 230, 0.3)', // 浅蓝色
  'rgba(144, 238, 144, 0.3)', // 浅绿色
  'rgba(255, 218, 185, 0.3)', // 桃色
  'rgba(221, 160, 221, 0.3)', // 梅红色
  'rgba(255, 255, 224, 0.3)', // 浅黄色
]

const darkColors = [
  'rgba(139, 69, 19, 0.3)', // 深棕色
  'rgba(25, 25, 112, 0.3)', // 深蓝色
  'rgba(0, 100, 0, 0.3)', // 深绿色
  'rgba(128, 0, 128, 0.3)', // 紫色
  'rgba(165, 42, 42, 0.3)', // 深红色
  'rgba(184, 134, 11, 0.3)', // 深黄色
]

// 判断当前是否为暗色主题
const isDarkTheme = computed(() => currentTheme.value === ThemeType.DARK)

// 按彩虹顺序获取卡片颜色
const getCardColor = (id: string, index: number): string => {
  const colorSet = isDarkTheme.value ? darkColors : lightColors
  return colorSet[index % colorSet.length]
}

// 响应式布局
const cardColSpan = ref<Span>(24)
const cardsPerRow = ref(1)

// 根据窗口宽度动态调整卡片列数，支持最多8列
const updateColSpan = (): void => {
  const width = window.innerWidth
  let span: Span = 24
  let newCardsPerRow = 1

  if (width >= 2400) {
    span = 3 // 8列 (24/3 = 8)
    newCardsPerRow = 8
  } else if (width >= 1800) {
    span = 4 // 6列 (24/4 = 6)
    newCardsPerRow = 6
  } else if (width >= 1400) {
    span = 6 // 4列 (24/6 = 4)
    newCardsPerRow = 4
  } else if (width >= 1000) {
    span = 8 // 3列 (24/8 = 3)
    newCardsPerRow = 3
  } else if (width >= 600) {
    span = 12 // 2列 (24/12 = 2)
    newCardsPerRow = 2
  } else {
    span = 24 // 1列 (24/24 = 1)
    newCardsPerRow = 1
  }

  cardColSpan.value = span
  cardsPerRow.value = newCardsPerRow
}

// 使用特权时间管理
const privilegeTime = usePrivilegeTime(privileges)

// 获取验证类型标签
const getVerificationTypeLabel = (type: number): string => {
  return VERIFICATION_TYPE_LABELS[type as keyof typeof VERIFICATION_TYPE_LABELS] || '未知'
}

/**
 * 处理特权卡片点击事件
 */
const handlePrivilegeClick = (privilege: PrivilegeResponse): void => {
  // 触发特权验证流程
  emit('startVerification', privilege)
}

/**
 * 加载更多数据
 */
const loadMore = async (): Promise<void> => {
  if (!hasMore.value || isLoading.value || isLoadingMore.value) {
    return
  }
  await search(true)
}

/**
 * 搜索特权
 */
const search = async (loadMore: boolean = false): Promise<void> => {
  if (isLoading.value || (loadMore && isLoadingMore.value)) {
    return
  }

  if (loadMore) {
    isLoadingMore.value = true
  } else {
    isLoading.value = true
    privileges.value = []
    lastId.value = undefined
    hasMore.value = true
  }

  try {
    const searchParams = {
      ...props.searchCondition.value,
      id: loadMore ? lastId.value : undefined,
      loadSize: 20,
    }

    const response = await privilegeApi.search(searchParams)

    if (response.success && response.data) {
      const newPrivileges = response.data.map((privilege) => ({
        ...privilege,
        exactExpireTime: formatExactTime(privilege.expireTime),
        relativeExpireTime: formatRelativeTime(privilege.expireTime),
      }))

      if (loadMore) {
        privileges.value.push(...newPrivileges)
      } else {
        privileges.value = newPrivileges
      }

      // 更新分页信息
      if (newPrivileges.length > 0) {
        lastId.value = newPrivileges[newPrivileges.length - 1].id
      }

      // 检查是否还有更多数据
      hasMore.value = newPrivileges.length === 20
    } else {
      message.error('获取特权列表失败')
    }
  } catch (error) {
    console.error('搜索特权失败:', error)
    message.error('搜索失败，请稍后重试')
  } finally {
    isLoading.value = false
    isLoadingMore.value = false
  }
}

/**
 * 重置列表
 */
const reset = (): void => {
  privileges.value = []
  lastId.value = undefined
  hasMore.value = true
  emit('reset')
}

/**
 * 获取加载状态
 */
const getLoadingState = (): boolean => {
  return isLoading.value || isLoadingMore.value
}

onMounted(async () => {
  await nextTick()

  // 初始化响应式布局
  updateColSpan()

  // 监听窗口大小变化
  window.addEventListener('resize', updateColSpan)
})

onUnmounted(() => {
  // 清理窗口大小变化监听器
  window.removeEventListener('resize', updateColSpan)
})

// 暴露方法给父组件
defineExpose({
  search,
  reset,
  getLoadingState,
})
</script>

<style lang="scss" scoped>
@use '@/styles/article/article-modal';

// 紧凑卡片样式 - 高度和宽度减半
.compact-card {
  // 设置卡片固定高度（文章卡片是19rem，减半约为9.5rem）
  height: 9.5rem;

  :deep(.n-card) {
    height: 100%;
    min-height: auto;
  }

  :deep(.n-card__header) {
    padding: 0.5rem 0.75rem;
    min-height: auto;
    flex-shrink: 0;
  }

  :deep(.n-card__content) {
    padding: 0.5rem 0.75rem;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  // 标题样式，适当调整大小
  .article-title {
    font-size: 1.1rem;
    font-weight: 500;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // 标签适当调整
  .card-tag {
    font-size: 13px;
    margin: 0.3rem 0.1rem;
  }

  // 描述文字区域 - 添加滚动条
  .privilege-description {
    font-size: 14px;
    padding: 0.3rem 0;
    line-height: 1.4;
    flex: 1;
    overflow-y: auto;
    margin-bottom: 0.3rem;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }

  // 过期时间标签
  .privilege-expire-time {
    flex-shrink: 0;

    .n-tag {
      font-size: 12px;
    }
  }

  // 确保内容区域的布局
  .flex-between-center {
    margin-bottom: 0.3rem;
    flex-shrink: 0;
  }
}

.privilege-description {
  color: var(--text-color-2);
  font-size: 14px;
  line-height: 1.5;
  padding: 0.5rem 0;
}

.privilege-expire-time {
  .time-clickable {
    cursor: pointer;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
