{"groups": [{"name": "article", "type": "com.shenmo.wen.app.core.config.properties.ArticleConfigProperties", "sourceType": "com.shenmo.wen.app.core.config.properties.ArticleConfigProperties"}, {"name": "privilege.verification", "type": "com.shenmo.wen.app.core.config.properties.PrivilegeVerificationProperties", "sourceType": "com.shenmo.wen.app.core.config.properties.PrivilegeVerificationProperties"}], "properties": [{"name": "article.check-detail-view", "type": "java.lang.String", "sourceType": "com.shenmo.wen.app.core.config.properties.ArticleConfigProperties"}, {"name": "article.content-max-length", "type": "java.lang.Integer", "sourceType": "com.shenmo.wen.app.core.config.properties.ArticleConfigProperties"}, {"name": "privilege.verification.daily-limit", "type": "java.lang.Integer", "description": "每日申请次数限制", "sourceType": "com.shenmo.wen.app.core.config.properties.PrivilegeVerificationProperties"}, {"name": "privilege.verification.expire-minutes", "type": "java.lang.Integer", "description": "验证流程过期时间（分钟）", "sourceType": "com.shenmo.wen.app.core.config.properties.PrivilegeVerificationProperties"}, {"name": "privilege.verification.step-timeout-minutes", "type": "java.lang.Integer", "description": "每步骤超时时间（分钟）", "sourceType": "com.shenmo.wen.app.core.config.properties.PrivilegeVerificationProperties"}, {"name": "privilege.verification.verification-url-prefix", "type": "java.lang.String", "description": "验证页面URL路径前缀", "sourceType": "com.shenmo.wen.app.core.config.properties.PrivilegeVerificationProperties"}], "hints": [], "ignored": {"properties": []}}