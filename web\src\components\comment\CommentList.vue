<template>
  <div
    ref="commentListContainerRef"
    class="comment-list-container"
    :class="{ 'has-input-box': commentInputVisible == '-1' }"
  >
    <NInfiniteScroll @load="handleLoadMore" :distance="50" :trigger="commentScrollTrigger">
      <div class="comment-scroll">
        <TransitionGroup name="smooth" appear tag="div">
          <div
            v-for="commentItem in commentList"
            :key="commentItem.id"
            :ref="
              (el) => {
                if (el) updateCommentRef(commentItem.id, el as HTMLElement)
              }
            "
          >
            <CommentListItem
              :comment="commentItem"
              :flash-comment-id="flashCommentId"
              :show-reply-list-btn="showReplyListBtn"
              :comment-input-visible="commentInputVisible"
              :quick-reply-loading="quickReplyLoading"
              @show-reply-list="$emit('showReplyList', $event)"
              @handle-comment-reply-click="$emit('handleCommentReplyClick', $event)"
              @interaction-btn="handleInteractionBtn"
              @favorite-btn="$emit('favoriteBtn', $event)"
              @quick-reply-comment="$emit('quickReplyComment', $event)"
              @update-editor="handleUpdateEditor"
            />
          </div>
        </TransitionGroup>
        <div class="comment-list-footer">
          <NSpin v-if="commentLoading" class="display-flex" />
          <NEmpty
            v-else-if="!commentLoading && (commentNoMore || commentList.length === 0)"
            :description="hasCommentPermission ? '没有更多评论了...' : '您没有权限查看评论'"
          />
        </div>
      </div>
    </NInfiniteScroll>
  </div>
</template>

<script lang="ts" setup>
import { NInfiniteScroll, NSpin, NEmpty } from 'naive-ui'
import { ref } from 'vue'

import CommentListItem from '@/components/comment/CommentListItem.vue'
import type { Comment } from '@/types/comment/comment.types'
import type { EditorWithFormatPainter } from '@/types/tiptap/editor-with-format-painter.types'

// 容器引用
const commentListContainerRef = ref<HTMLElement | null>(null)

const props = defineProps<{
  commentList: Comment[]
  flashCommentId: string
  showReplyListBtn: boolean
  commentInputVisible: string
  commentScrollTrigger: string
  commentLoading: boolean
  commentNoMore: boolean
  hasCommentPermission: boolean
  quickReplyLoading: Map<string, boolean>
}>()

const emit = defineEmits<{
  (e: 'loadMoreComments'): void
  (e: 'showReplyList', comment: Comment): void
  (e: 'handleCommentReplyClick', comment: Comment): void
  (e: 'interactionBtn', comment: Comment, actionType: number): void
  (e: 'favoriteBtn', comment: Comment): void
  (e: 'quickReplyComment', comment: Comment): void
  (e: 'updateEditor', commentId: string, editor: EditorWithFormatPainter): void
  (e: 'updateCommentRef', commentId: string, el: HTMLElement): void
}>()

const updateCommentRef = (commentId: string, el: HTMLElement) => {
  emit('updateCommentRef', commentId, el)
}

// 处理互动按钮点击
const handleInteractionBtn = (comment: Comment, actionType: number) => {
  emit('interactionBtn', comment, actionType)
}

// 处理编辑器更新
const handleUpdateEditor = (commentId: string, editor: EditorWithFormatPainter) => {
  emit('updateEditor', commentId, editor)
}

// 处理加载更多评论
const handleLoadMore = () => {
  // 当评论列表为空且已标记无更多评论时，不触发加载
  if (props.commentList.length === 0 && props.commentNoMore) {
    return
  }
  emit('loadMoreComments')
}

// 暴露容器引用供父组件使用
defineExpose({
  commentListContainerRef,
})
</script>

<style lang="scss" scoped>
@use '@/styles/comment/comment-list';
</style>
