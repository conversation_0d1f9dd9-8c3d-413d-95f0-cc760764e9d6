package com.shenmo.wen.common.config.exception;

import com.shenmo.wen.common.constant.StringConstant;
import com.shenmo.wen.common.exception.BaseException;
import com.shenmo.wen.common.exception.enumeration.BaseExceptionEnum;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnumOption;
import com.shenmo.wen.common.pojo.response.ResponseData;
import com.shenmo.wen.common.util.FeignUtils;
import feign.FeignException;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.net.UnknownHostException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
public abstract class BasicGlobalExceptionHandler {

    /**
     * 通用异常
     *
     * @param response      {@link HttpServletResponse}
     * @param baseException {@link BaseException}
     * @return 失败响应结果
     * <AUTHOR>
     */
    @ExceptionHandler(BaseException.class)
    @ResponseBody
    public ResponseData<?> generic(HttpServletResponse response, BaseException baseException) {

        log.error(">>> 基础异常", baseException);
        return renderJson(response, baseException.getExceptionEnum());
    }

    /**
     * feign调用异常
     *
     * @param feignException {@link FeignException}
     * @return 失败响应结果
     * <AUTHOR>
     */
    @ExceptionHandler(FeignException.class)
    @ResponseBody
    public ResponseData<?> feign(HttpServletResponse response, FeignException feignException) {

        log.error(">>> feign调用异常", feignException);
        final int status = feignException.status();
        response.setStatus(status);
        final ResponseData<?> errorResponseData = FeignUtils.parseFeignResponse(feignException);
        errorResponseData.setCode(status);
        if (feignException.getCause() instanceof UnknownHostException) {
            response.setStatus(HttpStatus.SERVICE_UNAVAILABLE.value());
            errorResponseData.setCode(status);
        }
        return errorResponseData;
    }

    /**
     * 缺少请求参数异常
     *
     * @param response                                {@link HttpServletResponse}
     * @param missingServletRequestParameterException {@link MissingServletRequestParameterException}
     * @return 失败响应结果
     * <AUTHOR>
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseBody
    public ResponseData<?> missingServletRequestParameter(HttpServletResponse response, MissingServletRequestParameterException missingServletRequestParameterException) {

        log.error(">>> 请求参数异常", missingServletRequestParameterException);
        String parameterType = missingServletRequestParameterException.getParameterType();
        String parameterName = missingServletRequestParameterException.getParameterName();
        return renderJson(response, ExceptionEnumOption.of(HttpStatus.BAD_REQUEST, String.format("缺少请求参数: %s:%s", parameterType, parameterName)));
    }

    /**
     * 参数转换异常
     *
     * @param response                        {@link HttpServletResponse}
     * @param httpMessageNotReadableException {@link HttpMessageNotReadableException}
     * @return 失败响应结果
     * <AUTHOR>
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseBody
    public ResponseData<?> httpMessageNotReadable(HttpServletResponse response, HttpMessageNotReadableException httpMessageNotReadableException) {

        log.error(">>> 参数格式传递异常", httpMessageNotReadableException);
        return renderJson(response, BaseExceptionEnum.HTTP_MESSAGE_NOT_READABLE);
    }

    /**
     * 不支持的类型异常
     *
     * @param response                           {@link HttpServletResponse}
     * @param httpMediaTypeNotSupportedException {@link HttpMediaTypeNotSupportedException}
     * @return 失败响应结果
     * <AUTHOR>
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @ResponseBody
    public ResponseData<?> httpMediaTypeNotSupported(HttpServletResponse response, HttpMediaTypeNotSupportedException httpMediaTypeNotSupportedException) {

        log.error(">>> 参数格式传递异常", httpMediaTypeNotSupportedException);
        return renderJson(response, BaseExceptionEnum.HTTP_MEDIA_TYPE_NOT_SUPPORTED);
    }

    /**
     * 请求方法不支持异常
     *
     * @param request                                {@link HttpServletRequest}
     * @param response                               {@link HttpServletResponse}
     * @param httpRequestMethodNotSupportedException {@link HttpMediaTypeNotSupportedException}
     * @return 失败响应结果
     * <AUTHOR>
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseBody
    public ResponseData<?> httpRequestMethodNotSupported(HttpServletRequest request, HttpServletResponse response, HttpRequestMethodNotSupportedException httpRequestMethodNotSupportedException) {

        log.error(">>> 请求方法异常, 请求方法: {}, 支持的请求方法: {}", request.getMethod(), httpRequestMethodNotSupportedException.getSupportedHttpMethods());
        return renderJson(response, BaseExceptionEnum.HTTP_REQUEST_METHOD);
    }

    /**
     * 资源不存在异常
     *
     * @param response                {@link HttpServletResponse}
     * @param noHandlerFoundException {@link NoHandlerFoundException}
     * @return 失败响应结果
     * <AUTHOR>
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseBody
    public ResponseData<?> noHandlerFound(HttpServletResponse response, NoHandlerFoundException noHandlerFoundException) {

        log.error(">>> 资源不存在异常", noHandlerFoundException);
        return renderJson(response, BaseExceptionEnum.NO_HANDLER_FOUND);
    }

    /**
     * 参数校验失败异常
     *
     * @param response                        {@link HttpServletResponse}
     * @param methodArgumentNotValidException {@link MethodArgumentNotValidException}
     * @return 失败响应结果
     * <AUTHOR>
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public ResponseData<?> methodArgumentNotValid(HttpServletResponse response, MethodArgumentNotValidException methodArgumentNotValidException) {

        String argNotValidMessage = getArgNotValidMessage(methodArgumentNotValidException.getBindingResult());
        log.error(">>> 参数校验错误异常，具体信息为：{}", argNotValidMessage);
        return renderJson(response, ExceptionEnumOption.of(HttpStatus.FORBIDDEN, argNotValidMessage));
    }

    /**
     * 参数绑定失败异常
     *
     * @param response      {@link HttpServletResponse}
     * @param bindException {@link BindException}
     * @return 失败响应结果
     * <AUTHOR>
     */
    @ExceptionHandler(BindException.class)
    @ResponseBody
    public ResponseData<?> bind(HttpServletResponse response, BindException bindException) {

        String argNotValidMessage = getArgNotValidMessage(bindException.getBindingResult());
        log.error(">>> 参数绑定错误异常，具体信息为：{}", argNotValidMessage);
        return renderJson(response, BaseExceptionEnum.BIND);
    }

    /**
     * 参数类型不匹配异常
     *
     * @param response                            {@link HttpServletResponse}
     * @param methodArgumentTypeMismatchException {@link MethodArgumentTypeMismatchException}
     * @return 失败响应结果
     * <AUTHOR>
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseBody
    public ResponseData<?> methodArgumentTypeMismatch(HttpServletResponse response, MethodArgumentTypeMismatchException methodArgumentTypeMismatchException) {

        final String typeMismatchMessage = getTypeMismatchMessage(methodArgumentTypeMismatchException);
        log.error(">>> 参数类型不匹配异常，具体信息为：{}", typeMismatchMessage);
        return renderJson(response, ExceptionEnumOption.of(HttpStatus.BAD_REQUEST, typeMismatchMessage));
    }

    /**
     * 文件超过最大上传大小异常
     *
     * @param response                       {@link HttpServletResponse}
     * @param maxUploadSizeExceededException {@link MaxUploadSizeExceededException}
     * @return 失败响应结果
     * <AUTHOR>
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseBody
    public ResponseData<?> maxUploadSizeExceeded(HttpServletResponse response, MaxUploadSizeExceededException maxUploadSizeExceededException) {

        log.error(">>> 文件超过最大上传大小", maxUploadSizeExceededException);
        return renderJson(response, BaseExceptionEnum.MAX_UPLOAD_SIZE_EXCEEDED);
    }

    /**
     * servlet异常
     *
     * @param response {@link HttpServletResponse}
     * @return 失败响应结果
     * <AUTHOR>
     */
    @ExceptionHandler(ServletException.class)
    @ResponseBody
    public ResponseData<?> servletException(HttpServletResponse response, ServletException servletException) {

        log.error(">>> servlet 异常", servletException);
        final Optional<ResponseData<?>> genericOpt = checkGeneric(response, servletException);
        if (genericOpt.isPresent()) {
            return genericOpt.get();
        }
        final FeignException feignException = FeignUtils.findFeignException(servletException);
        if (Objects.nonNull(feignException)) {

            return feign(response, feignException);
        }
        return serverError(response, servletException);
    }

    /**
     * 数据访问异常
     *
     * @param response            {@link HttpServletResponse}
     * @param dataAccessException {@link DataAccessException}
     * @return 失败响应结果
     * <AUTHOR>
     */
    @ExceptionHandler(DataAccessException.class)
    @ResponseBody
    public ResponseData<?> badSqlGrammar(HttpServletResponse response, DataAccessException dataAccessException) {

        log.error(">>> 数据访问异常", dataAccessException);
        return renderJson(response, BaseExceptionEnum.DATA_ACCESS);
    }

    /**
     * 断言错误
     *
     * @param response       {@link HttpServletResponse}
     * @param assertionError {@link AssertionError}
     * @return 失败响应结果
     * <AUTHOR>
     */
    @ExceptionHandler(AssertionError.class)
    @ResponseBody
    public ResponseData<?> assertion(HttpServletResponse response, AssertionError assertionError) {

        log.error(">>> 断言错误", assertionError);
        final Optional<ResponseData<?>> genericOpt = checkGeneric(response, assertionError);
        if (genericOpt.isPresent()) {
            return genericOpt.get();
        }
        return renderJson(response, BaseExceptionEnum.SERVER_ERROR);
    }

    /**
     * 服务器运行异常
     * <p>
     * 拦截所有错误与异常
     *
     * @param response  {@link HttpServletResponse}
     * @param throwable {@link Throwable}
     * @return 失败响应结果
     * <AUTHOR>
     */
    @ExceptionHandler(Throwable.class)
    @ResponseBody
    public ResponseData<?> serverError(HttpServletResponse response, Throwable throwable) {

        log.error(">>> 服务器运行异常", throwable);
        return renderJson(response, BaseExceptionEnum.SERVER_ERROR);
    }

    /**
     * 渲染异常json
     *
     * @param response      http响应
     * @param exceptionEnum 异常枚举
     * <AUTHOR>
     */
    protected ResponseData<?> renderJson(HttpServletResponse response, ExceptionEnum exceptionEnum) {

        int value = exceptionEnum.getStatus().value();
        response.setStatus(value);
        return ResponseData.error(exceptionEnum.getCode(), exceptionEnum.getMessage());
    }

    /**
     * 获取请求参数不正确的提示信息
     * <p>
     * 多个信息，拼接成用逗号分隔的形式
     *
     * @param bindingResult 参数绑定结果
     * <AUTHOR>
     */
    protected String getArgNotValidMessage(BindingResult bindingResult) {
        if (bindingResult == null) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();

        //多个错误用逗号分隔
        List<ObjectError> allErrorInfos = bindingResult.getAllErrors();
        for (ObjectError error : allErrorInfos) {
            stringBuilder.append(StringConstant.COMMA).append(error.getDefaultMessage());
        }

        //最终把首部的逗号去掉
        return StringUtils.removeStart(stringBuilder.toString(), StringConstant.COMMA);
    }

    /**
     * 获取参数类型不匹配提示信息
     *
     * @param e 参数类型不匹配异常
     * <AUTHOR>
     */
    protected String getTypeMismatchMessage(MethodArgumentTypeMismatchException e) {

        final String name = e.getName();
        final Object value = e.getValue();
        final Class<?> requiredType = e.getRequiredType();
        final boolean hasParameter = Objects.nonNull(value) && Objects.nonNull(requiredType);
        return hasParameter
                ? String.format("参数 %s:%s 类型不匹配, 当前: %s, 需要: %s", name, value, value.getClass().getSimpleName(), requiredType.getSimpleName())
                : "参数类型不匹配";
    }

    /**
     * 检查通用异常
     *
     * @param response  {@link HttpServletResponse}
     * @param throwable {@link Throwable}
     * @return 响应结果数据
     * <AUTHOR>
     */
    protected Optional<ResponseData<?>> checkGeneric(HttpServletResponse response, Throwable throwable) {

        if (throwable instanceof BaseException baseException) {
            return Optional.ofNullable(generic(response, baseException));
        } else {
            if (Optional.ofNullable(throwable).map(Throwable::getCause).isPresent()) {

                return checkGeneric(response, throwable.getCause());
            } else {
                return Optional.empty();
            }
        }
    }
}
