import { type Ref } from 'vue'

import type { DanmuItem } from '@/types/danmaku/danmu-item.types'

export function useResizeHandler(
  container: Ref<HTMLDivElement>,
  dmContainer: Ref<HTMLDivElement>,
  containerWidth: Ref<number>,
  containerHeight: Ref<number>,
  danmu: DanmuItem,
) {
  /**
   * 初始化核心尺寸
   */
  function initCore() {
    containerWidth.value = container.value.offsetWidth
    containerHeight.value = container.value.offsetHeight
    if (containerWidth.value === 0 || containerHeight.value === 0) {
      throw new Error('获取不到容器宽高')
    }
  }

  /**
   * 重置尺寸
   */
  function resize() {
    initCore()
    const items = dmContainer.value.getElementsByClassName('dm')

    for (let i = 0; i < items.length; i++) {
      const el = items[i] as HTMLDivElement

      // 检测是否包含图片，为图片弹幕添加宽度缓冲
      const hasImages = el.querySelector('img') !== null
      let elementWidth = el.offsetWidth
      if (hasImages) {
        elementWidth += 100 // 为包含图片的弹幕添加缓冲
      }

      const totalDistance = containerWidth.value + elementWidth
      el.style.setProperty('--dm-scroll-width', `-${totalDistance}px`)
      el.style.left = `${containerWidth.value}px`
      el.style.animationDuration = `${totalDistance / danmu.speeds}s`
    }
  }

  return {
    initCore,
    resize,
  }
}
