/**
 * 自定义弹幕类型定义
 * 用于扩展弹幕的自定义属性
 */

/**
 * 自定义弹幕数据
 * 定义弹幕对象的具体属性结构
 */
export interface CustomDanmu {
  /** 弹幕文本内容 */
  text?: string
  /** 弹幕内容（可以是字符串或对象） */
  content?: string | CustomDanmu
  /** 弹幕颜色 */
  color?: string
  /** 弹幕大小 */
  size?: number
  /** 弹幕类型 */
  type?: string
  /** 弹幕时间戳 */
  time?: number
  /** 弹幕唯一标识 */
  id?: string
  /** 弹幕用户信息 */
  user?: {
    id: string
    name: string
    avatar?: string
  }
  /** 弹幕位置信息 */
  position?: {
    x: number
    y: number
  }
  /** 弹幕动画配置 */
  animation?: {
    duration: number
    speed: number
  }
}
