import { useDialog } from 'naive-ui'
import { type Ref } from 'vue'

import articleApi from '@/api/article'
import { createDialogOptions } from '@/config/naive-ui-config'
import {
  ArticlePublishedScope,
  ARTICLE_PUBLISHED_SCOPE_LABEL,
} from '@/constants/article/article-published-scope.constants'
import type { Article } from '@/types/article/article.types'
import message from '@/utils/ui/message'

/**
 * 文章列表操作组合式函数返回值类型
 */
export interface UseArticleListOperationsReturn {
  /** 处理切换发布范围 */
  handleToggleScope: (article: Article) => void
  /** 处理删除文章 */
  handleDeleteArticle: (article: Article) => void
  /** 处理文章重新排序 */
  handleReorderArticles: (draggedId: string, targetId: string, position: 'before' | 'after') => void
}

/**
 * 文章列表操作组合式函数
 * 提供文章的各种操作功能（删除、切换范围、重排序）
 */
export function useArticleListOperations(
  articleList: Ref<Article[]>,
): UseArticleListOperationsReturn {
  // 对话框
  const dialog = useDialog()

  // 切换文章发布范围
  const handleToggleScope = (article: Article): void => {
    const currentScope = article.publishedScope
    const targetScope =
      currentScope === ArticlePublishedScope.PERSONAL
        ? ArticlePublishedScope.PUBLIC
        : ArticlePublishedScope.PERSONAL

    dialog.warning(
      createDialogOptions({
        title: '切换发布范围',
        content: `确定要将文章《${article.title}》切换为${ARTICLE_PUBLISHED_SCOPE_LABEL[targetScope]}可见吗？`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          articleApi.togglePublishedScope(article.id).then((res) => {
            if (res.code === 200) {
              article.publishedScope = targetScope
              message.success(`文章已切换为${ARTICLE_PUBLISHED_SCOPE_LABEL[targetScope]}可见`)
            } else {
              message.error(res.message || '操作失败')
            }
          })
        },
      }),
    )
  }

  // 处理删除文章
  const handleDeleteArticle = (article: Article): void => {
    dialog.warning(
      createDialogOptions({
        title: '删除文章',
        content: `确定要删除文章《${article.title}》吗？此操作不可恢复。`,
        positiveText: '删了',
        negativeText: '算了',
        onPositiveClick: () => {
          articleApi
            .delete(article.id)
            .then((res) => {
              if (res.code === 200) {
                const index = articleList.value.findIndex((item) => item.id === article.id)
                if (index > -1) {
                  articleList.value.splice(index, 1)
                }
                message.success('文章已删除')
              } else {
                message.error(res.message || '删除失败')
              }
            })
            .catch(() => {
              message.error('删除失败，请稍后再试')
            })
        },
      }),
    )
  }

  // 处理文章重新排序
  const handleReorderArticles = (
    draggedId: string,
    targetId: string,
    position: 'before' | 'after',
  ): void => {
    const draggedIndex = articleList.value.findIndex((item) => item.id === draggedId)
    const targetIndex = articleList.value.findIndex((item) => item.id === targetId)

    if (draggedIndex === -1 || targetIndex === -1) return

    const [draggedArticle] = articleList.value.splice(draggedIndex, 1)

    let newIndex = targetIndex
    if (position === 'after') {
      newIndex = draggedIndex < targetIndex ? targetIndex : targetIndex + 1
    } else {
      newIndex = draggedIndex > targetIndex ? targetIndex : targetIndex
    }

    articleList.value.splice(newIndex, 0, draggedArticle)
    message.success('移动成功！')
  }

  return {
    handleToggleScope,
    handleDeleteArticle,
    handleReorderArticles,
  }
}
