import logger from '@/utils/log/log'

// 拖拽克隆工具函数

export function createDragClone(
  cardElement: HTMLElement,
  rect: DOMRect,
  position: { x: number; y: number },
): HTMLElement {
  // 深度克隆整个卡片元素
  const clonedCard = cardElement.cloneNode(true) as HTMLElement

  // 内联所有样式
  inlineAllStyles(cardElement, clonedCard)

  // 创建包装容器
  const clonedElement = document.createElement('div')
  clonedElement.style.cssText = `
    position: fixed;
    left: ${position.x}px;
    top: ${position.y}px;
    width: ${rect.width * 0.5}px;
    height: ${rect.height * 0.5}px;
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
    z-index: 9999;
    pointer-events: none !important;
    transition: none;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border-radius: 0.5rem;
    overflow: hidden;
    transform-origin: center center;
  `

  // 设置克隆卡片的样式
  clonedCard.style.cssText = `
    width: ${rect.width}px;
    height: ${rect.height}px;
    transform: scale(0.5);
    transform-origin: top left;
    margin: 0;
    position: relative;
    pointer-events: none !important;
  `

  // 移除交互元素
  removeInteractiveElements(clonedCard)

  clonedElement.appendChild(clonedCard)
  document.body.appendChild(clonedElement)

  logger.debug('DOM克隆元素已创建')
  return clonedElement
}

export function cleanupDragElement(element: HTMLElement): void {
  if (element && element.parentNode) {
    element.parentNode.removeChild(element)
    logger.debug('克隆元素已清理')
  }
}

// 内联所有样式
function inlineAllStyles(originalElement: HTMLElement, clonedElement: HTMLElement): void {
  // 内联根元素样式
  const originalStyle = window.getComputedStyle(originalElement)
  inlineElementStyle(originalElement, clonedElement, originalStyle)

  // 递归内联所有子元素样式
  const originalChildren = originalElement.querySelectorAll('*')
  const clonedChildren = clonedElement.querySelectorAll('*')

  for (let i = 0; i < originalChildren.length && i < clonedChildren.length; i++) {
    const originalChild = originalChildren[i] as HTMLElement
    const clonedChild = clonedChildren[i] as HTMLElement
    const childStyle = window.getComputedStyle(originalChild)
    inlineElementStyle(originalChild, clonedChild, childStyle)
  }
}

// 内联单个元素的样式
function inlineElementStyle(
  originalEl: HTMLElement,
  clonedEl: HTMLElement,
  computedStyle: CSSStyleDeclaration,
): void {
  // 关键样式属性列表
  const importantStyles = [
    'background',
    'background-color',
    'background-image',
    'background-size',
    'background-position',
    'color',
    'font-family',
    'font-size',
    'font-weight',
    'font-style',
    'border',
    'border-radius',
    'border-color',
    'border-width',
    'border-style',
    'padding',
    'margin',
    'width',
    'height',
    'max-width',
    'max-height',
    'min-width',
    'min-height',
    'display',
    'position',
    'top',
    'left',
    'right',
    'bottom',
    'flex',
    'flex-direction',
    'flex-wrap',
    'justify-content',
    'align-items',
    'text-align',
    'text-decoration',
    'text-transform',
    'line-height',
    'opacity',
    'visibility',
    'box-shadow',
    'text-shadow',
    'transform',
    'transition',
  ]

  let styleText = ''
  importantStyles.forEach((prop) => {
    const value = computedStyle.getPropertyValue(prop)
    if (value && value !== 'none' && value !== 'auto' && value !== 'initial') {
      styleText += `${prop}: ${value} !important; `
    }
  })

  // 基础禁用样式
  styleText += `
    pointer-events: none !important;
    user-select: none !important;
  `

  // 特殊处理不同类型的元素
  if (clonedEl.classList.contains('article-content')) {
    styleText += `
      overflow: hidden !important;
      height: auto !important;
      max-height: none !important;
    `
  } else if (clonedEl.classList.contains('n-scrollbar-content')) {
    styleText += `
      overflow: visible !important;
      height: auto !important;
      max-height: none !important;
    `
  } else {
    styleText += `
      overflow: hidden !important;
    `
  }

  if (styleText) {
    clonedEl.style.cssText = (clonedEl.style.cssText || '') + styleText
  }
}

// 移除交互元素
function removeInteractiveElements(element: HTMLElement): void {
  // 彻底禁用根元素的所有交互
  element.style.pointerEvents = 'none !important'
  element.style.userSelect = 'none !important'
  element.style.setProperty('-webkit-user-select', 'none', 'important')
  element.style.setProperty('-moz-user-select', 'none', 'important')
  element.style.setProperty('-ms-user-select', 'none', 'important')

  // 移除所有事件监听器相关的属性
  const eventAttrs = [
    'onclick',
    'onmousedown',
    'onmouseup',
    'ontouchstart',
    'ontouchend',
    'onscroll',
    'onwheel',
  ]
  eventAttrs.forEach((attr) => element.removeAttribute(attr))

  // 递归处理所有子元素
  const allElements = element.querySelectorAll('*')
  allElements.forEach((child) => {
    const childEl = child as HTMLElement

    // 彻底禁用所有交互
    childEl.style.pointerEvents = 'none !important'
    childEl.style.userSelect = 'none !important'
    childEl.style.setProperty('-webkit-user-select', 'none', 'important')
    childEl.style.setProperty('-moz-user-select', 'none', 'important')
    childEl.style.setProperty('-ms-user-select', 'none', 'important')

    // 移除事件属性
    eventAttrs.forEach((attr) => childEl.removeAttribute(attr))
  })

  // 特别处理滚动相关元素
  const scrollElements = element.querySelectorAll(
    '.n-scrollbar, .article-content, [style*="overflow"]',
  )
  scrollElements.forEach((scrollEl) => {
    const el = scrollEl as HTMLElement
    el.style.overflow = 'hidden !important'
    el.style.pointerEvents = 'none !important'

    if (el.classList.contains('article-content')) {
      el.style.height = 'auto !important'
      el.style.maxHeight = 'none !important'
    }
  })

  // 移除滚动条轨道和处理内容容器
  const scrollRails = element.querySelectorAll('.n-scrollbar-rail')
  scrollRails.forEach((rail) => rail.remove())

  const scrollContents = element.querySelectorAll('.n-scrollbar-content')
  scrollContents.forEach((content) => {
    const contentEl = content as HTMLElement
    contentEl.style.overflow = 'visible !important'
    contentEl.style.height = 'auto !important'
    contentEl.style.maxHeight = 'none !important'
    contentEl.style.pointerEvents = 'none !important'
  })

  // 移除可能导致问题的元素
  const problematicElements = element.querySelectorAll('iframe, script, noscript, object, embed')
  problematicElements.forEach((el) => el.remove())
}
