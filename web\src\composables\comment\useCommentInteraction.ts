import type { Comment } from '@/types/comment/comment.types'
import type { EditorWithFormatPainter } from '@/types/tiptap/editor-with-format-painter.types'

import { useCommentInteractionActions } from './useCommentInteractionActions'
import { useCommentInteractionReply } from './useCommentInteractionReply'
import { useCommentInteractionState } from './useCommentInteractionState'

import type { Editor, JSONContent } from '@tiptap/vue-3'
import type { Ref } from 'vue'

// 定义获取文章ID的函数类型
type GetArticleIdFunction = () => string

// 定义评论输入引用类型
interface CommentMainInputRef {
  sendTiptapEditorRef?: Editor & {
    clearContent: () => void
  }
}

// 定义评论交互状态类型
interface CommentInteractionState {
  quickReplyLoading: Ref<Map<string, boolean>>
  sendCommentLoading: Ref<boolean>
  commentInputVisible: Ref<string>
  commentReply: Ref<JSONContent | undefined>
  quickReplyTiptapEditorMap: Ref<Map<string, EditorWithFormatPainter>>
  updateEditor: (commentId: string, editor: EditorWithFormatPainter) => void
  interactionBtn: (comment: Comment, actionType: number) => void
  favoriteBtn: (comment: Comment) => void
  clearAllQuickReplyContent: () => (params: { commentList: Comment[] }) => void
  handleCommentReplyClick: (comment: Comment, options: { isLastBreadcrumb: boolean }) => void
  debouncedQuickReplyComment: (
    comment: Comment,
    options: { isLastBreadcrumb: boolean; onSuccess?: (commentId: string) => void },
  ) => void
  debouncedSendComment: (
    commentMainInputRef: CommentMainInputRef,
    options: { lastBreadcrumbComment: Comment; onSuccess?: (commentId: string) => void },
  ) => void
}

export function useCommentInteraction(getArticleId: GetArticleIdFunction): CommentInteractionState {
  // 初始化状态管理
  const {
    quickReplyLoading,
    sendCommentLoading,
    commentInputVisible,
    commentReply,
    quickReplyTiptapEditorMap,
    updateEditor,
  } = useCommentInteractionState()

  // 初始化操作功能
  const { interactionBtn, favoriteBtn } = useCommentInteractionActions()

  // 初始化回复功能
  const {
    clearAllQuickReplyContent,
    handleCommentReplyClick,
    debouncedQuickReplyComment,
    debouncedSendComment,
  } = useCommentInteractionReply(
    quickReplyLoading,
    sendCommentLoading,
    commentInputVisible,
    commentReply,
    quickReplyTiptapEditorMap,
    getArticleId,
  )

  return {
    quickReplyLoading,
    sendCommentLoading,
    commentInputVisible,
    commentReply,
    quickReplyTiptapEditorMap,
    updateEditor,
    interactionBtn,
    favoriteBtn,
    clearAllQuickReplyContent,
    handleCommentReplyClick,
    debouncedQuickReplyComment,
    debouncedSendComment,
  }
}
