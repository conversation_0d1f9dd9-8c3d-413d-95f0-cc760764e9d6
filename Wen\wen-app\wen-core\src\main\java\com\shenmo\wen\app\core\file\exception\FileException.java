package com.shenmo.wen.app.core.file.exception;

import com.shenmo.wen.common.exception.BaseException;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import org.springframework.http.HttpStatus;

/**
 *
 * <AUTHOR>
 */
public class FileException extends BaseException {
    public FileException(ExceptionEnum exceptionEnum) {
        super(exceptionEnum);
    }

    public FileException(HttpStatus httpStatus, String message) {
        super(httpStatus, message);
    }

    public FileException(HttpStatus httpStatus, String description, Throwable throwable) {
        super(httpStatus, description, throwable);
    }

    public FileException(HttpStatus httpStatus, String description, String message) {
        super(httpStatus, description, message);
    }
}
