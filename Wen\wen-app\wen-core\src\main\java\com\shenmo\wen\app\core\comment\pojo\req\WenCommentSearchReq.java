package com.shenmo.wen.app.core.comment.pojo.req;

import com.shenmo.wen.app.core.pojo.req.WenSearchReq;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 评论搜索请求
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WenCommentSearchReq extends WenSearchReq {

    /**
     * 最后一条记录的ID，用于基于游标的分页
     */
    private Long id;

    /**
     * 加载数量限制，默认100条
     */
    private Integer loadSize = 100;
}
