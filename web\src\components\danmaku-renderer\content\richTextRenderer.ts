import fileApi from '@/api/file'
import { THUMBNAIL } from '@/constants/image/filepath.constants'

import type { JSONContent } from '@tiptap/vue-3'

/**
 * 将富文本JSON节点转换为HTML字符串
 * 此函数处理各种节点类型，包括文本、提及、图片等
 *
 * @param node 富文本节点对象
 * @returns 渲染后的HTML字符串
 */
export function renderDanmuNodeToHTML(node: JSONContent): string {
  if (!node) return ''

  let result = ''

  try {
    // 处理文本节点
    if (node.type === 'text' && node.text) {
      // 处理标记，如粗体、斜体等
      let text = node.text
      if (node.marks && node.marks.length > 0) {
        for (const mark of node.marks) {
          if (mark.type === 'bold') {
            text = `<strong>${text}</strong>`
          } else if (mark.type === 'italic') {
            text = `<em>${text}</em>`
          } else if (mark.type === 'underline') {
            text = `<u>${text}</u>`
          } else if (mark.type === 'strike') {
            text = `<s>${text}</s>`
          } else if (mark.type === 'code') {
            text = `<code>${text}</code>`
          } else if (mark.type === 'link' && mark.attrs?.href) {
            text = `<a href="${mark.attrs.href}" target="_blank">${text}</a>`
          } else if (mark.type === 'textStyle' && mark.attrs?.color) {
            text = `<span style="color: ${mark.attrs.color}">${text}</span>`
          }
        }
      }
      result += text
    }
    // 处理提及（@用户）- 保持与编辑器一致的HTML结构
    else if (node.type === 'mention' && node.attrs?.label) {
      try {
        // 获取头像URL并应用fileApi处理
        const avatarSrc = node.attrs.avatar ? fileApi.getResourceURL(node.attrs.avatar) : ''

        // 构建与编辑器一致的data属性
        const dataAttrs = [
          `data-type="mention"`,
          `data-id="${node.attrs.id || ''}"`,
          `data-label="${node.attrs.label}"`,
          `data-avatar="${node.attrs.avatar || ''}"`,
        ].join(' ')

        // 构建mention名称部分
        const mentionName = `<span class="mention-name">@${node.attrs.label}</span>`

        // 构建头像部分（如果存在）
        const mentionAvatar = avatarSrc
          ? `<img src="${avatarSrc}" alt="${node.attrs.label}" class="mention-avatar" loading="eager" decoding="async" referrerpolicy="no-referrer" onerror="this.style.display='none';" />`
          : ''

        // 生成与编辑器一致的HTML结构：名称在前，头像在后
        result += `<span class="mention" ${dataAttrs} contenteditable="false">${mentionName}${mentionAvatar}</span>`
      } catch (error) {
        console.error('处理mention时出错:', error)
        result += `<span class="mention" data-type="mention" data-label="${node.attrs.label}"><span class="mention-name">@${node.attrs.label}</span></span>`
      }
    }
    // 处理图片
    else if (node.type === 'image' && node.attrs?.src) {
      try {
        // 处理图片URL - 获取原始URL
        const originalSrc = node.attrs.src

        // 判断是否为外部链接
        const isExternalImage =
          originalSrc.startsWith('http://') || originalSrc.startsWith('https://')

        if (isExternalImage) {
          // 外部图片直接显示，不应用缩略图逻辑
          result += `<img src="${originalSrc}" class="danmaku-image"
                     loading="eager" decoding="async" alt="图片" referrerpolicy="no-referrer"
                     data-original-src="${originalSrc}"
                     data-is-original="true"
                     onerror="this.replaceWith(document.createTextNode('[图片]'));" />`
        } else {
          // 本地图片应用缩略图逻辑，与编辑器行为保持一致
          let imgSrc: string
          let thumbnailSrc: string

          if (originalSrc.includes(THUMBNAIL)) {
            // 如果原始路径包含缩略图标记，使用缩略图
            imgSrc = fileApi.getResourceURL(originalSrc)
            thumbnailSrc = originalSrc
          } else {
            // 如果原始路径不包含缩略图标记，生成缩略图路径
            const pathParts = originalSrc.split('/')
            const fileName = pathParts.pop()
            const basePath = pathParts.join('/')
            thumbnailSrc = `${basePath}${THUMBNAIL}/${fileName}`
            imgSrc = fileApi.getResourceURL(thumbnailSrc)
          }

          // 添加备用图片显示方案，当缩略图加载失败时尝试原图
          const fallbackSrc = fileApi.getResourceURL(originalSrc.replace(THUMBNAIL, ''))

          // 使用data-属性传递原始路径和缩略图路径，添加预览状态标记
          result += `<img src="${imgSrc}" class="danmaku-image"
                     loading="eager" decoding="async" alt="图片" referrerpolicy="no-referrer"
                     data-original-src="${originalSrc}"
                     data-thumbnail-src="${thumbnailSrc}"
                     data-is-original="false"
                     onerror="if (!this.dataset.tried) { this.dataset.tried = 'true'; this.src = '${fallbackSrc}'; this.dataset.isOriginal = 'true'; }
                     else { this.replaceWith(document.createTextNode('[图片]')); }" />`
        }
      } catch (error) {
        console.error('处理图片时出错:', error)
        result += '<span class="image-placeholder">[图片]</span>'
      }
    }
    // 处理硬换行 - 转换为空格
    else if (node.type === 'hardBreak') {
      result += ' '
    }
    // 处理段落和其他块级元素，将其内容内联显示
    else if (node.type && ['paragraph', 'heading', 'blockquote'].includes(node.type)) {
      // 如果有子内容，递归解析
      if (node.content && Array.isArray(node.content)) {
        const contentHTML = node.content
          .map((child: JSONContent) => renderDanmuNodeToHTML(child))
          .join('')
        // 不添加块级元素标签，只保留内容
        result += contentHTML + ' '
      }
    }
    // 递归处理其他有子节点的内容
    else if (node.content && Array.isArray(node.content)) {
      result += node.content.map((child: JSONContent) => renderDanmuNodeToHTML(child)).join('')
    }
  } catch (error) {
    console.error('渲染HTML时发生错误:', error, '节点:', node)
    // 发生错误时，尝试返回文本内容或者占位符
    if (node.type === 'text' && node.text) {
      return node.text
    } else if (node.type === 'image') {
      return '[图片]'
    }
  }

  return result
}

/**
 * 渲染完整的弹幕富文本内容为HTML
 * 处理完整的TipTap格式JSON文档
 *
 * @param content 完整的JSON内容对象
 * @returns 渲染后的HTML字符串
 */
export function renderDanmuContent(content: JSONContent): string {
  try {
    // 确保content是有效的JSONContent对象
    if (!content || typeof content !== 'object') {
      console.warn('无效的弹幕内容对象:', content)
      return '<span>[无法显示内容]</span>'
    }

    // 如果内容为空，显示空内容提示
    if (!content.content || !Array.isArray(content.content) || content.content.length === 0) {
      return '<span>[空内容]</span>'
    }

    return renderDanmuNodeToHTML(content)
  } catch (error) {
    console.error('渲染弹幕内容时出错:', error)
    return '<span>[渲染失败]</span>'
  }
}
