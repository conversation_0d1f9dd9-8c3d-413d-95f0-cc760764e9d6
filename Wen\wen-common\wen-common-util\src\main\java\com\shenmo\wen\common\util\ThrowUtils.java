package com.shenmo.wen.common.util;

import com.shenmo.wen.common.exception.BaseException;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;

import java.util.function.Supplier;

/**
 * 异常抛出工具类
 *
 * <AUTHOR>
 */
public abstract class ThrowUtils {

    /**
     * 基础异常抛出
     */
    private static final Throw<BaseException> THROW = new Throw<>() {

        @Override
        public BaseException badRequest(String message) {

            return new BaseException(HttpStatus.BAD_REQUEST, message);
        }

        @Override
        public BaseException badRequest(String description, String message) {

            return new BaseException(HttpStatus.BAD_REQUEST, description, message);
        }

        @Override
        public BaseException badRequest(String description, @NonNull Throwable throwable) {

            return new BaseException(HttpStatus.BAD_REQUEST, description, throwable);
        }

        @Override
        public BaseException forbidden(String message) {

            return new BaseException(HttpStatus.FORBIDDEN, message);
        }

        @Override
        public BaseException forbidden(String description, String message) {

            return new BaseException(HttpStatus.FORBIDDEN, description, message);
        }

        @Override
        public BaseException forbidden(String description, @NonNull Throwable throwable) {

            return new BaseException(HttpStatus.FORBIDDEN, description, throwable);
        }

        @Override
        public BaseException unauthorized(String message) {

            return new BaseException(HttpStatus.UNAUTHORIZED, message);
        }

        @Override
        public BaseException unauthorized(String description, String message) {

            return new BaseException(HttpStatus.UNAUTHORIZED, description, message);
        }

        @Override
        public BaseException unauthorized(String description, @NonNull Throwable throwable) {

            return new BaseException(HttpStatus.UNAUTHORIZED, description, throwable);
        }

        @Override
        public BaseException notFound(String message) {

            return new BaseException(HttpStatus.NOT_FOUND, message);
        }

        @Override
        public BaseException notFound(String description, String message) {

            return new BaseException(HttpStatus.NOT_FOUND, description, message);
        }

        @Override
        public BaseException notFound(String description, @NonNull Throwable throwable) {

            return new BaseException(HttpStatus.NOT_FOUND, description, throwable);
        }

        @Override
        public BaseException methodNotAllowed(String message) {

            return new BaseException(HttpStatus.METHOD_NOT_ALLOWED, message);
        }

        @Override
        public BaseException methodNotAllowed(String description, String message) {

            return new BaseException(HttpStatus.METHOD_NOT_ALLOWED, description, message);
        }

        @Override
        public BaseException methodNotAllowed(String description, @NonNull Throwable throwable) {

            return new BaseException(HttpStatus.METHOD_NOT_ALLOWED, description, throwable);
        }

        @Override
        public BaseException internalServerError(String message) {

            return new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, message);
        }

        @Override
        public BaseException internalServerError(String description, String message) {

            return new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, description, message);
        }

        @Override
        public BaseException internalServerError(String description, @NonNull Throwable throwable) {

            return new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, description, throwable);
        }

        @Override
        public BaseException serviceUnavailable(String message) {

            return new BaseException(HttpStatus.SERVICE_UNAVAILABLE, message);
        }

        @Override
        public BaseException serviceUnavailable(String description, String message) {

            return new BaseException(HttpStatus.SERVICE_UNAVAILABLE, description, message);
        }

        @Override
        public BaseException serviceUnavailable(String description, @NonNull Throwable throwable) {

            return new BaseException(HttpStatus.SERVICE_UNAVAILABLE, description, throwable);
        }
    };

    /**
     * 基础异常供应抛出
     * <p>
     * {@link Supplier}
     */
    private static final Throw<Supplier<BaseException>> THROW_SUPPLIER = new Throw<>() {

        @Override
        public Supplier<BaseException> badRequest(String message) {

            return () -> new BaseException(HttpStatus.BAD_REQUEST, message);
        }

        @Override
        public Supplier<BaseException> badRequest(String description, String message) {

            return () -> new BaseException(HttpStatus.BAD_REQUEST, description, message);
        }

        @Override
        public Supplier<BaseException> forbidden(String message) {

            return () -> new BaseException(HttpStatus.FORBIDDEN, message);
        }

        @Override
        public Supplier<BaseException> forbidden(String description, String message) {

            return () -> new BaseException(HttpStatus.FORBIDDEN, description, message);
        }

        @Override
        public Supplier<BaseException> forbidden(String description, @NonNull Throwable throwable) {

            return () -> new BaseException(HttpStatus.FORBIDDEN, description, throwable);
        }

        @Override
        public Supplier<BaseException> badRequest(String description, @NonNull Throwable throwable) {

            return () -> new BaseException(HttpStatus.BAD_REQUEST, description, throwable);
        }

        @Override
        public Supplier<BaseException> unauthorized(String message) {

            return () -> new BaseException(HttpStatus.UNAUTHORIZED, message);
        }

        @Override
        public Supplier<BaseException> unauthorized(String description, String message) {

            return () -> new BaseException(HttpStatus.UNAUTHORIZED, description, message);
        }

        @Override
        public Supplier<BaseException> unauthorized(String description, @NonNull Throwable throwable) {

            return () -> new BaseException(HttpStatus.UNAUTHORIZED, description, throwable);
        }

        @Override
        public Supplier<BaseException> notFound(String message) {

            return () -> new BaseException(HttpStatus.NOT_FOUND, message);
        }

        @Override
        public Supplier<BaseException> notFound(String description, String message) {

            return () -> new BaseException(HttpStatus.NOT_FOUND, description, message);
        }

        @Override
        public Supplier<BaseException> notFound(String description, @NonNull Throwable throwable) {

            return () -> new BaseException(HttpStatus.NOT_FOUND, description, throwable);
        }

        @Override
        public Supplier<BaseException> methodNotAllowed(String message) {

            return () -> new BaseException(HttpStatus.METHOD_NOT_ALLOWED, message);
        }

        @Override
        public Supplier<BaseException> methodNotAllowed(String description, String message) {

            return () -> new BaseException(HttpStatus.METHOD_NOT_ALLOWED, description, message);
        }

        @Override
        public Supplier<BaseException> methodNotAllowed(String description, @NonNull Throwable throwable) {

            return () -> new BaseException(HttpStatus.METHOD_NOT_ALLOWED, description, throwable);
        }

        @Override
        public Supplier<BaseException> internalServerError(String message) {

            return () -> new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, message);
        }

        @Override
        public Supplier<BaseException> internalServerError(String description, String message) {

            return () -> new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, description, message);
        }

        @Override
        public Supplier<BaseException> internalServerError(String description, @NonNull Throwable throwable) {

            return () -> new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, description, throwable);
        }

        @Override
        public Supplier<BaseException> serviceUnavailable(String message) {

            return () -> new BaseException(HttpStatus.SERVICE_UNAVAILABLE, message);
        }

        @Override
        public Supplier<BaseException> serviceUnavailable(String description, String message) {

            return () -> new BaseException(HttpStatus.SERVICE_UNAVAILABLE, description, message);
        }

        @Override
        public Supplier<BaseException> serviceUnavailable(String description, @NonNull Throwable throwable) {

            return () -> new BaseException(HttpStatus.SERVICE_UNAVAILABLE, description, throwable);
        }
    };

    /**
     * 获取公共异常抛出
     *
     * @return 公共异常抛出
     */
    public static Throw<BaseException> getThrow() {

        return THROW;
    }

    /**
     * 获取公共异常抛出供应
     * {@link Supplier}
     *
     * @return 公共异常抛出供应
     */
    public static Throw<Supplier<BaseException>> getThrowSupplier() {

        return THROW_SUPPLIER;
    }

    /**
     * 抛出接口
     * <p>
     * 用于异常以及函数式接口的包装
     *
     * @param <T> 异常以及函数式接口类型
     * <AUTHOR>
     * @version 1.0.0
     */
    public interface Throw<T> {


        /**
         * 获取{@link HttpStatus#BAD_REQUEST}异常
         *
         * @param message 错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T badRequest(String message);

        /**
         * 获取{@link HttpStatus#BAD_REQUEST}异常
         *
         * @param description 异常简述
         * @param message     错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T badRequest(String description, String message);

        /**
         * 获取{@link HttpStatus#BAD_REQUEST}异常
         *
         * @param description 异常简述
         * @param throwable   可抛出的错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T badRequest(String description, @NonNull Throwable throwable);

        /**
         * 获取{@link HttpStatus#FORBIDDEN}异常
         *
         * @param message 错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T forbidden(String message);

        /**
         * 获取{@link HttpStatus#FORBIDDEN}异常
         *
         * @param description 异常简述
         * @param message     错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T forbidden(String description, String message);

        /**
         * 获取{@link HttpStatus#FORBIDDEN}异常
         *
         * @param description 异常简述
         * @param throwable   可抛出的错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T forbidden(String description, @NonNull Throwable throwable);

        /**
         * 获取{@link HttpStatus#UNAUTHORIZED}异常
         *
         * @param message 错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T unauthorized(String message);

        /**
         * 获取{@link HttpStatus#UNAUTHORIZED}异常
         *
         * @param description 异常简述
         * @param message     错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T unauthorized(String description, String message);

        /**
         * 获取{@link HttpStatus#UNAUTHORIZED}异常
         *
         * @param description 异常简述
         * @param throwable   可抛出的错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T unauthorized(String description, @NonNull Throwable throwable);

        /**
         * 获取{@link HttpStatus#NOT_FOUND}异常
         *
         * @param message 错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T notFound(String message);

        /**
         * 获取{@link HttpStatus#NOT_FOUND}异常
         *
         * @param description 异常简述
         * @param message     错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T notFound(String description, String message);

        /**
         * 获取{@link HttpStatus#NOT_FOUND}异常
         *
         * @param description 异常简述
         * @param throwable   可抛出的错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T notFound(String description, @NonNull Throwable throwable);

        /**
         * 获取{@link HttpStatus#METHOD_NOT_ALLOWED}异常
         *
         * @param message 错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T methodNotAllowed(String message);

        /**
         * 获取{@link HttpStatus#METHOD_NOT_ALLOWED}异常
         *
         * @param description 异常简述
         * @param message     错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T methodNotAllowed(String description, String message);

        /**
         * 获取{@link HttpStatus#METHOD_NOT_ALLOWED}异常
         *
         * @param description 异常简述
         * @param throwable   可抛出的错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T methodNotAllowed(String description, @NonNull Throwable throwable);

        /**
         * 获取{@link HttpStatus#INTERNAL_SERVER_ERROR}异常
         *
         * @param message 错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T internalServerError(String message);

        /**
         * 获取{@link HttpStatus#INTERNAL_SERVER_ERROR}异常
         *
         * @param description 异常简述
         * @param message     错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T internalServerError(String description, String message);

        /**
         * 获取{@link HttpStatus#INTERNAL_SERVER_ERROR}异常
         *
         * @param description 异常简述
         * @param throwable   可抛出的错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T internalServerError(String description, @NonNull Throwable throwable);

        /**
         * 获取{@link HttpStatus#SERVICE_UNAVAILABLE}异常
         *
         * @param message 错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T serviceUnavailable(String message);

        /**
         * 获取{@link HttpStatus#SERVICE_UNAVAILABLE}异常
         *
         * @param description 异常简述
         * @param message     错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T serviceUnavailable(String description, String message);

        /**
         * 获取{@link HttpStatus#SERVICE_UNAVAILABLE}异常
         *
         * @param description 异常简述
         * @param throwable   可抛出的错误信息
         * @return 公共异常
         * <AUTHOR>
         */
        T serviceUnavailable(String description, @NonNull Throwable throwable);
    }
}
