package com.shenmo.wen.common.objectstorage.template;

import com.shenmo.wen.common.constant.StringConstant;
import com.shenmo.wen.common.objectstorage.properties.ObjectStorageProperties;
import com.shenmo.wen.common.objectstorage.response.*;
import com.shenmo.wen.common.util.AssertUtils;
import com.shenmo.wen.common.util.ThrowUtils;
import com.shenmo.wen.common.util.ZipUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.tika.Tika;
import org.springframework.core.io.ClassPathResource;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 抽象的对象存储模板
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractObjectStorageTemplate implements ObjectStorageTemplate {

    protected final ObjectStorageProperties props;

    protected AbstractObjectStorageTemplate(ObjectStorageProperties props) {
        this.props = props;
    }

    /**
     * Tika文件元数据提取工具
     */
    protected final Tika tika = new Tika();

    @Override
    public void initObjects(String bucket, String objectsZipPath) {
        File objectsZip = null;
        InputStream inputStream = null;
        try {
            final ClassPathResource classPathResource = new ClassPathResource(objectsZipPath);
            inputStream = classPathResource.getInputStream();
            objectsZip = new File(Optional.ofNullable(classPathResource.getFilename()).orElse(objectsZipPath));
            AssertUtils.isTrue(objectsZip.createNewFile(),
                    String.format("初始化对象zip包时文件创建失败, %s -> %s:%s", bucket, objectsZipPath, objectsZip));
            FileUtils.copyToFile(inputStream, objectsZip);
            final String md5Hex = DigestUtils.md5Hex(inputStream.readAllBytes());
            final String md5Object = "." + objectsZipPath;
            if (existObject(bucket, md5Object)) {
                final GetObjectResponse<?> object = getObject(bucket, md5Object);
                final InputStream objectInputStream = object.getInputStream();
                final byte[] objectBytes;
                if (Objects.isNull(objectInputStream)) {
                    objectBytes = new byte[] {};
                } else {
                    objectBytes = objectInputStream.readAllBytes();
                }
                if (new String(objectBytes, StandardCharsets.UTF_8).equals(md5Hex)) {
                    return;
                }
            }
            final byte[] bytes = md5Hex.getBytes(StandardCharsets.UTF_8);
            makeSetPublicReadableBucketPolicy(bucket);
            uploadObjects(bucket, null, objectsZip);
            putObject(bucket, md5Object, bytes, bytes.length, tika.detect(bytes));
        } catch (Exception e) {
            throw ThrowUtils.getThrow()
                    .internalServerError(String.format("初始化对象zip包时失败, %s -> %s", bucket, objectsZipPath), e);
        } finally {
            FileUtils.deleteQuietly(objectsZip);
            IOUtils.closeQuietly(inputStream, e -> log.error("InputStream close fail", e));
        }
    }

    /**
     * 获取Tika文件元数据提取工具
     *
     * @return Tika文件元数据提取工具
     * <AUTHOR>
     */
    @Override
    public Tika tika() {
        return this.tika;
    }

    @Override
    public boolean existPrefix(String bucket, String prefix) {
        try {
            for (String dir : listObjects(bucket, prefix, false).getPrefixes()) {
                if (Objects.equals(prefix, dir)) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.warn("对象目录不存在, {}", e.getMessage());
            return false;
        }
        return true;
    }

    @NonNull
    @Override
    public PutObjectResponse putObject(String bucket, String object, byte[] bytes, long size, String contentType) {

        return putObject(bucket, object, new ByteArrayInputStream(bytes), size, contentType);
    }

    @Override
    public void uploadObjects(String bucket, @Nullable String prefix, @Nullable File rootDir,
            List<String> filePathList) {

        AssertUtils.isTrue(bucketExists(bucket), String.format("上传对象时桶 %s 不存在", bucket));
        prefix = prefixFormatting(prefix);
        for (String filePath : filePathList) {
            final File file = new File(filePath);
            String object = Objects.nonNull(rootDir)
                    ? prefix + file.getAbsolutePath().replace(rootDir.getAbsolutePath() + File.separator,
                            StringConstant.EMPTY)
                    : prefix + file.getName();
            object = object.replace(File.separator, StringConstant.SLASH);
            uploadObject(bucket, object, filePath);
        }
    }

    @Override
    public void uploadObjects(String bucket, @Nullable String prefix, List<String> filePathList) {

        uploadObjects(bucket, prefix, null, filePathList);
    }

    @Override
    public void uploadObjects(String bucket, @Nullable String prefix, File objectsZip) {

        AssertUtils.isTrue(Objects.nonNull(objectsZip),
                String.format("上传对象包时文件为null, %s -> %s:%s", bucket, prefix, objectsZip));
        AssertUtils.isTrue(objectsZip.exists(), String.format("上传对象包时文件不存在, %s -> %s:%s", bucket, prefix, objectsZip));
        AssertUtils.isTrue(objectsZip.getName().endsWith(ZipUtils.ZIP_EXTENSION),
                String.format("上传对象包时文件必须是.zip压缩包, %s -> %s:%s", bucket, prefix, objectsZip));
        makeSetPublicReadableBucketPolicy(bucket);
        File unzip = null;
        try {
            unzip = ZipUtils.unzip(objectsZip);
            AssertUtils.isTrue(Objects.nonNull(unzip),
                    String.format("上传对象包时解压后文件为null, %s -> %s:%s:%s", bucket, objectsZip, prefix, unzip));
            uploadObjects(bucket, prefix, unzip,
                    FileUtils.listFiles(unzip, null, true).stream().map(File::getPath).toList());
        } catch (Exception e) {
            throw ThrowUtils.getThrow()
                    .internalServerError(String.format("上传对象包失败, %s -> %s:%s", bucket, prefix, objectsZip), e);
        } finally {
            FileUtils.deleteQuietly(unzip);
        }
    }

    @Override
    public void copyObjects(String originBucket, @Nullable String originPrefix, String targetBucket,
            @Nullable String targetPrefix) {

        originPrefix = prefixFormatting(originPrefix);
        targetPrefix = prefixFormatting(targetPrefix);
        for (String object : listObjects(originBucket, originPrefix, true).getNames()) {

            copyObject(originBucket, object, targetBucket, targetPrefix + object.replace(originPrefix, ""));
        }
    }

    @NonNull
    @Override
    public MoveObjectResponse moveObject(String originBucket, String originObject, String targetBucket,
            String targetObject) {

        final CopyObjectResponse copyObjectResponse = copyObject(originBucket, originObject, targetBucket,
                targetObject);
        removeObject(originBucket, originObject);
        return new MoveObjectResponse(copyObjectResponse.getOrigin());
    }

    @Override
    public void moveObjects(String originBucket, @Nullable String originPrefix, String targetBucket,
            @Nullable String targetPrefix) {

        copyObjects(originBucket, originPrefix, targetBucket, targetPrefix);
        removeObjects(originBucket, originPrefix);
    }

    @NonNull
    @Override
    public ListObjectResponse<?> listObjects(String bucket, @Nullable String prefix, boolean recursive) {

        return listObjects(bucket, prefix, -1, recursive);
    }

    @NonNull
    @Override
    public ListObjectResponse<?> listObjects(String bucket, @Nullable String prefix) {

        return listObjects(bucket, prefix, true);
    }

    @NonNull
    @Override
    public ListObjectResponse<?> listObjects(String bucket) {

        return listObjects(bucket, null, true);
    }

    @Override
    public void setPublicReadableBucketPolicy(String bucket) {
        setBucketPolicy(bucket, getPublicReadableBucketPolicy(bucket));
    }

    @Override
    public void removeObjects(String bucket, @Nullable String prefix, boolean recursive) {
        try {
            prefix = prefixFormatting(prefix);
            removeObject(bucket, listObjects(bucket, prefix, recursive).getNames().toArray(String[]::new));
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(
                    String.format("移除某个桶下指定前缀的所有对象时获取对象名失败: %s -> %s:%s", bucket, prefix, recursive), e);
        }
    }

    @Override
    public void removeObjects(String bucket, @Nullable String prefix) {

        removeObjects(bucket, prefix, true);
    }

    @Override
    public void removeObjects(String bucket) {

        removeObjects(bucket, null);
    }

    @Override
    public void downloadObjects(String bucket, String prefix, String fileDir, boolean prefixReserve) {

        prefix = prefixFormatting(prefix);
        for (String object : listObjects(bucket, prefix).getNames()) {
            downloadObject(bucket, object, fileDir + (prefixReserve ? object : object.replace(prefix, "")));
        }
    }

    @Override
    public void downloadObjects(String bucket, String prefix, String fileDir) {

        downloadObjects(bucket, prefix, fileDir, false);
    }

    @Override
    public void makeSetBucketPolicy(String bucket, String policyConfig) {
        if (!bucketExists(bucket)) {
            makeBucket(bucket);
        }
        setBucketPolicy(bucket, policyConfig);
    }

    @Override
    public void makeSetPublicReadableBucketPolicy(String bucket) {
        makeSetBucketPolicy(bucket, getPublicReadableBucketPolicy(bucket));
    }

    @NonNull
    @Override
    public PutObjectResponse makePutObject(String bucket, String object, InputStream inputStream, long size,
            String contentType) {
        makeBucket(bucket);
        return putObject(bucket, object, inputStream, size, contentType);
    }

    @NonNull
    @Override
    public PutObjectResponse makePutObject(String bucket, String object, byte[] bytes, long size, String contentType) {
        makeBucket(bucket);
        return putObject(bucket, object, bytes, size, contentType);
    }

    @NonNull
    @Override
    public UploadObjectResponse makeUploadObject(String bucket, String object, String filename) {
        makeBucket(bucket);
        return uploadObject(bucket, object, filename);
    }

    @Override
    public void makeUploadObjects(String bucket, @Nullable String prefix, @Nullable File rootDir,
            List<String> filePathList) {
        makeBucket(bucket);
        uploadObjects(bucket, prefix, rootDir, filePathList);
    }

    @Override
    public void makeUploadObjects(String bucket, @Nullable String prefix, List<String> filePathList) {

        makeUploadObjects(bucket, prefix, null, filePathList);
    }

    /**
     * 本地文件准备
     * <p>
     * 对象是前缀那么直接本地创建, 反之创建上级目录
     *
     * @param bucket   桶
     * @param object   对象
     * @param filePath 文件路径
     * @return 本地文件是否准备成功
     * <AUTHOR>
     */
    protected boolean localFilePrepare(String bucket, String object, String filePath) {
        final File file = new File(filePath);
        if (object.endsWith(StringConstant.SLASH) && !file.exists()) {
            AssertUtils.isTrue(file.mkdirs(), String.format("下载对象到本地时目录创建失败: %s -> %s:%s", bucket, object, filePath));
            return true;
        }
        File parentFile = file.getParentFile();
        if (!parentFile.exists()) {

            AssertUtils.isTrue(parentFile.mkdirs(),
                    String.format("下载对象到本地时目录创建失败: %s -> %s:%s", bucket, object, parentFile.getAbsoluteFile()));
        }
        return false;
    }
}
