/**
 * 弹幕模型属性类型定义
 * 定义弹幕模型的基础属性结构
 */

/**
 * 弹幕模型属性接口
 * 用于定义弹幕模型的基础属性
 */
export interface DanmakuModelProps {
  /** 弹幕唯一标识 */
  id?: string
  /** 弹幕内容 */
  content?: string
  /** 弹幕类型 */
  type?: string
  /** 弹幕时间 */
  time?: number
  /** 弹幕颜色 */
  color?: string
  /** 弹幕大小 */
  size?: number
  /** 弹幕位置 */
  position?: {
    x: number
    y: number
  }
  /** 弹幕用户信息 */
  user?: {
    id: string
    name: string
    avatar?: string
  }
  /** 索引签名，允许动态属性访问 */
  [key: string]: unknown
}
