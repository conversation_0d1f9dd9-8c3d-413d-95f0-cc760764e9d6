/*
Navicat Premium Data Transfer

Source Server         : *************
Source Server Type    : MySQL
Source Server Version : 80040 (8.0.40-0ubuntu0.24.04.1)
Source Host           : *************:3306
Source Schema         : sm_wen

Target Server Type    : MySQL
Target Server Version : 80040 (8.0.40-0ubuntu0.24.04.1)
File Encoding         : 65001

Date: 08/03/2025 19:00:28
*/

SET NAMES utf8mb4;

SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for wen_article
-- ----------------------------
DROP TABLE IF EXISTS `wen_article`;

CREATE TABLE `wen_article` (
    `id` bigint NOT NULL COMMENT '文章ID',
    `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文章标题',
    `tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文章标签',
    `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文章内容',
    `user_id` bigint NOT NULL COMMENT '文章发布人ID',
    `ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文章发布时的IP地址',
    `ip_location` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文章发布时的IP归属地',
    `published_at` timestamp NOT NULL COMMENT '发布时间',
    `like_count` int NULL DEFAULT 0 COMMENT '点赞数',
    `dislike_count` int NULL DEFAULT 0 COMMENT '点踩数',
    `favorite_count` int NULL DEFAULT 0 COMMENT '收藏数',
    `comment_count` int NULL DEFAULT 0 COMMENT '评论数',
    `operation_level` int NULL DEFAULT 0 COMMENT '操作等级，默认0',
    `published_scope` tinyint NOT NULL DEFAULT 1 COMMENT '文章发布范围，默认1',
    `ct_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `md_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文章表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wen_article_share
-- ----------------------------
CREATE TABLE `wen_article_share` (
    `id` bigint NOT NULL COMMENT '主键ID',
    `article_id` bigint NOT NULL COMMENT '文章ID',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `ct_tm` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_article_user` (`article_id`, `user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文章分享表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wen_comment
-- ----------------------------
DROP TABLE IF EXISTS `wen_comment`;

CREATE TABLE `wen_comment` (
    `id` bigint NOT NULL COMMENT '评论ID',
    `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '评论内容',
    `article_id` bigint NOT NULL COMMENT '目标文章ID',
    `user_id` bigint NOT NULL COMMENT '评论发布人ID',
    `parent_comment_id` bigint NULL DEFAULT NULL COMMENT '父评论ID（如果是回复评论）',
    `ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '评论发布时的IP地址',
    `ip_location` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '评论发布时的IP地址归属地',
    `mention_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '提及人ID',
    `mention_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '提及人用户名',
    `like_count` int NULL DEFAULT 0 COMMENT '点赞数',
    `dislike_count` int NULL DEFAULT 0 COMMENT '点踩数',
    `favorite_count` int NULL DEFAULT 0 COMMENT '收藏数',
    `reply_count` int NULL DEFAULT 0 COMMENT '回复数',
    `published_at` timestamp NOT NULL COMMENT '评论时间',
    `ct_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `md_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '评论表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wen_favorite
-- ----------------------------
DROP TABLE IF EXISTS `wen_favorite`;

CREATE TABLE `wen_favorite` (
    `id` bigint NOT NULL COMMENT '收藏ID',
    `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
    `target_type` int NULL DEFAULT NULL COMMENT '目标类型：评论（0），文章（1）',
    `target_id` bigint NULL DEFAULT NULL COMMENT '目标ID，文章ID或评论ID',
    `ct_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文章与评论的收藏表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wen_interaction
-- ----------------------------
DROP TABLE IF EXISTS `wen_interaction`;

CREATE TABLE `wen_interaction` (
    `id` bigint NOT NULL COMMENT '互动ID',
    `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
    `target_type` int NULL DEFAULT NULL COMMENT '目标类型：评论（0），文章（1）',
    `target_id` bigint NULL DEFAULT NULL COMMENT '目标ID，文章ID或评论ID',
    `action_type` int NULL DEFAULT NULL COMMENT '互动类型：点踩（0），点赞（1）',
    `ct_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '互动时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文章与评论的互动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wen_notification
-- ----------------------------
DROP TABLE IF EXISTS `wen_notification`;

CREATE TABLE `wen_notification` (
    `id` bigint NOT NULL COMMENT '通知ID',
    `user_id` bigint NOT NULL COMMENT '发布的用户ID',
    `article_id` bigint NOT NULL COMMENT '发布的文章ID',
    `comment_id` bigint NULL DEFAULT NULL COMMENT '发布的评论ID',
    `comment_notification_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '评论通知人ID',
    `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知内容，描述具体的操作',
    `type` tinyint NOT NULL DEFAULT 0 COMMENT '通知类型：0-系统通知，1-发布通知，2-修改通知，3-评论通知',
    `is_read` tinyint NOT NULL DEFAULT 0 COMMENT '全局已读标志：0-未全部已读，1-全部已读',
    `ct_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '通知时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通知表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wen_notification_user_read
-- ----------------------------
DROP TABLE IF EXISTS `wen_notification_user_read`;

CREATE TABLE `wen_notification_user_read` (
    `id` bigint NOT NULL COMMENT '主键ID',
    `notification_id` bigint NOT NULL COMMENT '通知ID',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `ct_tm` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_notification_user` (`notification_id`, `user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通知用户已读关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wen_third_party_login
-- ----------------------------
DROP TABLE IF EXISTS `wen_third_party_login`;

CREATE TABLE `wen_third_party_login` (
    `id` bigint NOT NULL COMMENT '第三方登录ID',
    `user_id` bigint NULL DEFAULT NULL COMMENT '关联的用户ID',
    `provider` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '第三方登录平台（如 QQ, WECHAT）',
    `provider_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '第三方平台的用户ID',
    `access_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '第三方登录的access token',
    `refresh_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '第三方登录的refresh token',
    `ct_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '第三方登录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wen_user
-- ----------------------------
DROP TABLE IF EXISTS `wen_user`;

CREATE TABLE `wen_user` (
    `id` bigint NOT NULL COMMENT '用户ID',
    `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
    `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
    `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像URL',
    `ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后登录IP',
    `job` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '职业信息',
    `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码（加密存储）',
    `level` int NULL DEFAULT 0 COMMENT '用户等级',
    `experience` int NULL DEFAULT 0 COMMENT '用户经验值',
    `ct_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `md_tm` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;