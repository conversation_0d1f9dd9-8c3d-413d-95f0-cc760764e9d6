<template>
  <n-config-provider>
    <div class="notification-container">
      <LongPress @long-press="handleLongPress" @click="handleClick">
        <NotificationButton
          :unread-count="Number(unreadCount)"
          :notification-receive-type="notificationReceiveType"
        />
      </LongPress>
      <n-modal
        class="notification-modal"
        style="width: 600px; max-width: 100%"
        v-model:show="showNotification"
        title="通知列表"
        preset="dialog"
        :auto-focus="false"
      >
        <template #header>
          <div
            style="display: flex; justify-content: space-between; align-items: center; width: 100%"
          >
            <span>通知列表</span>
            <NotificationReceiveTypeSelector
              v-model="notificationReceiveType"
              @update:model-value="updateNotificationReceiveType"
            />
          </div>
        </template>
        <NotificationList
          ref="notificationListRef"
          :visible="showNotification"
          @notification-click="handleNotificationClick"
        />
      </n-modal>
    </div>
  </n-config-provider>
</template>

<script lang="ts" setup>
import { NConfigProvider, NModal, NAvatar, NButton } from 'naive-ui'
import { ref, onMounted, onUnmounted, computed, h } from 'vue'

import fileApi from '@/api/file'
import notificationApi from '@/api/notification'
import userApi from '@/api/user'
import LongPress from '@/components/interaction/LongPress.vue'
import NotificationButton from '@/components/notification/NotificationButton.vue'
import NotificationList from '@/components/notification/NotificationList.vue'
import NotificationReceiveTypeSelector from '@/components/notification/NotificationReceiveTypeSelector.vue'
import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'
import { NOTIFICATIONS } from '@/constants/notification/destination.constants'
import { NOTIFICATION_RECEIVED } from '@/constants/notification/frequency-key.constants'
import { NotificationReceiveType } from '@/constants/notification/notification-receive-type.constants'
import { COMMENT_EXTENSIONS } from '@/constants/tiptap/tiptap.constants'
import { ArrowRight20Filled } from '@/icons'
import router from '@/router'
import { useArticleStore, useCommentStore } from '@/stores/index'
import { type NotificationListItem } from '@/types/api/notification-list-response.types'
import { type Notification } from '@/types/notification/notification.types'
import Json from '@/utils/data/json'
import webSocket from '@/utils/network/web-socket'
import frequencyLimit from '@/utils/performance/frequency-limit'
import localStorage from '@/utils/storage/local-storage'
import { activeTheme, ThemeType } from '@/utils/theme/theme'
import tiptap from '@/utils/tiptap/tiptap'
import message from '@/utils/ui/message'
import notification from '@/utils/ui/notification'

import type { JSONContent } from '@tiptap/vue-3'

const articleStore = useArticleStore()
const commentStore = useCommentStore()
const getArticleId = () => {
  return articleStore.getId
}

// 通知相关状态
const showNotification = ref<boolean>(false)
const unreadCount = ref(0)
// 定义通知列表引用类型
type NotificationListInstance = {
  loadNotificationPage: () => void
  resetPagination: () => void
}
const notificationListRef = ref<NotificationListInstance | null>(null)

// 添加主题监听
const isDarkTheme = computed(() => activeTheme.value === ThemeType.DARK)

// 用户相关
const user = localStorage.getLoginUser()

// 通知接收类型
const notificationReceiveType = ref(
  user?.notificationReceiveType ?? NotificationReceiveType.FAVORITE,
) // 默认为收藏

// 更新通知接收类型
const updateNotificationReceiveType = (value: number) => {
  notificationReceiveType.value = value
  // 更新用户信息中的通知接收类型
  userApi.updateNotificationReceiveType(value).then((res) => {
    if (res && res.data !== undefined) {
      // 更新本地存储的用户信息
      const loginUser = localStorage.getLoginUser()
      if (loginUser) {
        loginUser.notificationReceiveType = value
        localStorage.setLoginUser(loginUser)
      }

      message.success('通知接收类型已更新')

      // 只有在非 CLOSE 类型时才刷新通知数量
      if (value !== NotificationReceiveType.CLOSE) {
        loadNotificationPage()
      } else {
        // 如果是 CLOSE 类型，只更新未读数为 0
        unreadCount.value = 0
      }
    }
  })
}

// 处理长按事件 - 全部标记为已读
const handleLongPress = () => {
  notificationApi.readAll().then(() => {
    message.success('读完了！')
    loadNotificationPage()
  })
}

// 处理点击事件 - 显示通知列表
const handleClick = () => {
  showNotification.value = true
  loadNotificationPage()
}

// WebSocket相关
onMounted(() => {
  webSocket.connect()
  loadNotificationPage()
  subscribeNotification()
})

onUnmounted(() => {
  unsubscribeNotification()
  webSocket.disconnect()
})

const subscribeNotification = () => {
  if (user?.id) {
    webSocket.subscribe(`/user/${user.id}${NOTIFICATIONS}`, handleNotificationMessageReceived)
  }
}

const unsubscribeNotification = () => {
  if (user?.id) {
    webSocket.unsubscribe(`/user/${user.id}${NOTIFICATIONS}`)
  }
}

// 评论标记
const commentFlag = '评论了：'

// 处理接收到的通知消息
const handleNotificationMessageReceived = (newMessage: string) => {
  const messageObj = Json.parse(newMessage) as Notification
  if (!messageObj) return

  // 只有在通知接收类型不是CLOSE时才显示通知提醒
  if (notificationReceiveType.value !== NotificationReceiveType.CLOSE) {
    frequencyLimit.debounce(NOTIFICATION_RECEIVED + messageObj.id, () => {
      const commentId = messageObj.commentId
      const content = messageObj.content
      let contentObj: JSONContent
      let index = 0
      if (commentId && content) {
        index = content.indexOf(commentFlag)
        const commentContent = content.substring(index + commentFlag.length)
        contentObj = tiptap.toJsonObject(commentContent)
      }
      const n = notification.create({
        title:
          commentId && content ? content.substring(0, index + commentFlag.length) : '发来通知~',
        content: () => {
          return commentId
            ? h(TiptapEditor, {
                fileBucket: 'comment',
                modelValue: contentObj,
                extensions: [...COMMENT_EXTENSIONS],
                editable: false,
              })
            : h(
                'div',
                {
                  class: isDarkTheme.value ? 'dark-notification-content' : '',
                },
                messageObj.content,
              )
        },
        duration: 5000,
        keepAliveOnHover: true,
        closable: true,
        avatar: () => {
          return h(NAvatar, {
            size: 'small',
            objectFit: 'cover',
            round: true,
            src: fileApi.getResourceURL(messageObj.publisherAvatar || ''),
            class: isDarkTheme.value ? 'dark-notification-avatar' : '',
          })
        },
        action: () => {
          return h(
            NButton,
            {
              text: true,
              type: 'primary',
              class: isDarkTheme.value ? 'dark-notification-button' : '',
              onClick: () => {
                n.destroy()
                handleNotificationClick(messageObj)
              },
            },
            ['怎么个事？', h(ArrowRight20Filled, { size: 16 })],
          )
        },
      })
    })
  }

  // 无论是否显示提醒，都要刷新未读数
  loadNotificationPage()
}

// 加载通知页面
const loadNotificationPage = () => {
  // 加载未读消息数
  notificationApi.unreadCount().then((res) => {
    if (res?.data !== undefined) {
      // 当通知类型为CLOSE时，未读数显示为0，但UI仍显示
      unreadCount.value =
        notificationReceiveType.value === NotificationReceiveType.CLOSE ? 0 : Number(res.data)
    }
  })

  // 如果通知列表已打开，刷新列表
  if (showNotification.value && notificationListRef.value) {
    // 重置分页状态并加载第一页
    notificationListRef.value.resetPagination()
    notificationListRef.value.loadNotificationPage()
  }
}

// 处理通知点击（重载函数）
const handleNotificationClick = (notification: NotificationListItem | Notification) => {
  const articleId = notification.articleId
  const commentId = notification.commentId
  if (articleId !== getArticleId()) {
    // 文章id不一致时在新标签页中打开并自动切换过去
    const route = router.resolve({
      name: 'Article',
      params: { articleId: articleId, commentId: commentId },
    })
    const newWindow = window.open(route.href, '_blank')
    if (newWindow) {
      newWindow.focus()
    }
  } else {
    // 文章id一致时触发父组件的定位方法
    if (commentId) {
      // 更新commentStore的ID
      commentStore.setId(commentId)
      emit('locationComment', commentId)
    }
  }

  showNotification.value = false
  if (!notification.isRead) {
    notificationApi.read(notification.id)
  }
}

// 定义要触发的事件
const emit = defineEmits(['locationComment'])
</script>

<style lang="scss" scoped>
@use '@/styles/notification/notification-btn-modal';
</style>
