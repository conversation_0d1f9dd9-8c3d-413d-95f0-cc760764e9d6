@echo off
echo Starting Cursor Augment Code Automation GUI...

python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

pip show pyautogui >nul 2>&1
if errorlevel 1 (
    echo Installing dependencies...
    pip install pyautogui PyGetWindow opencv-python Pillow numpy pywin32
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        echo Please run install_manual.bat
        pause
        exit /b 1
    )
)

echo Starting GUI...
python gui_automation.py

if errorlevel 1 (
    echo GUI failed to start
    pause
)
