import type { Article } from '@/types/article/article.types'
import { createDragClone, cleanupDragElement } from '@/utils/drag/drag-clone'
import { setupDragEvents } from '@/utils/drag/drag-events'
import logger from '@/utils/log/log'

import type { Ref } from 'vue'

interface DragOptions {
  onDragStart?: (article: Article) => void
  onDragEnd?: () => void
  onDelete?: (article: Article) => void
  onReorder?: (draggedId: string, targetId: string, position: 'before' | 'after') => void
}

/**
 * 文章拖拽移动组合式函数返回值类型
 */
export interface UseArticleDragMoveReturn {
  /** 开始拖拽 */
  startDragging: (
    event: MouseEvent | TouchEvent,
    article: Article,
    avatarElement: HTMLElement,
  ) => void
  /** 处理拖拽移动 */
  handleDragMove: (event: MouseEvent | TouchEvent) => void
  /** 处理拖拽结束 */
  handleDragEnd: () => void
}

/**
 * 文章拖拽移动组合式函数
 * 提供拖拽移动和检测功能
 */
export function useArticleDragMove(
  isDragging: Ref<boolean>,
  draggedArticle: Ref<Article | null>,
  showTrashBin: Ref<boolean>,
  isOverTrashBin: Ref<boolean>,
  dragOverCardId: Ref<string | null>,
  dragOverPosition: Ref<'before' | 'after' | null>,
  isSingleCardRow: Ref<boolean>,
  dragPosition: Ref<{ x: number; y: number }>,
  isEnding: Ref<boolean>,
  clonedElement: Ref<HTMLElement | null>,
  eventHandlers: Ref<(() => void) | null>,
  resetDragState: () => void,
  options: DragOptions,
): UseArticleDragMoveReturn {
  // 获取事件坐标
  const getEventCoordinates = (event: MouseEvent | TouchEvent) => {
    if (event instanceof MouseEvent) {
      return { clientX: event.clientX, clientY: event.clientY }
    } else if (event instanceof TouchEvent) {
      // 优先使用 touches，如果为空则使用 changedTouches（适用于 touchend 事件）
      const touch = event.touches.length > 0 ? event.touches[0] : event.changedTouches[0]
      if (touch) {
        return { clientX: touch.clientX, clientY: touch.clientY }
      }
    }
    return { clientX: 0, clientY: 0 }
  }

  // 检查是否在垃圾篓上方
  const checkOverTrashBin = () => {
    const trashBin = document.querySelector('.trash-bin')
    if (!trashBin) return

    const rect = trashBin.getBoundingClientRect()
    const { x, y } = dragPosition.value

    isOverTrashBin.value = x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom
  }

  // 检查是否为行中唯一卡片
  const checkIfOnlyCardInRow = (targetCard: HTMLElement): boolean => {
    const allCards = document.querySelectorAll('.card-item:not(.dragging)')
    const targetRect = targetCard.getBoundingClientRect()

    // 检查容器宽度，如果容器宽度较小，很可能是单卡片行
    const container = document.querySelector('.article-container')
    const containerWidth = container ? container.getBoundingClientRect().width : window.innerWidth

    // 如果容器宽度小于等于768px，认为是窄屏，应该使用垂直指示器
    if (containerWidth <= 768) {
      return true
    }

    // 否则检查同一行中的卡片数量
    let cardsInSameRow = 0
    allCards.forEach((card) => {
      const cardRect = card.getBoundingClientRect()
      // 检查垂直重叠（同一行）
      const verticalOverlap = !(
        cardRect.bottom <= targetRect.top || cardRect.top >= targetRect.bottom
      )

      if (verticalOverlap) {
        cardsInSameRow++
      }
    })

    return cardsInSameRow === 1
  }

  // 检查是否在其他卡片上方
  const checkOverCard = () => {
    const cards = document.querySelectorAll('.card-item:not(.dragging)')
    dragOverCardId.value = null
    dragOverPosition.value = null
    isSingleCardRow.value = false

    cards.forEach((card) => {
      const rect = card.getBoundingClientRect()
      const { x, y } = dragPosition.value

      if (x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom) {
        const cardElement = card as HTMLElement
        const articleId = cardElement.dataset.articleId

        if (articleId && articleId !== draggedArticle.value?.id) {
          dragOverCardId.value = articleId

          // 检查是否为单卡片行
          const isOnlyCard = checkIfOnlyCardInRow(card as HTMLElement)
          isSingleCardRow.value = isOnlyCard

          logger.debug('拖拽悬停检测:', {
            articleId,
            isOnlyCard,
            containerWidth: document.querySelector('.article-container')?.getBoundingClientRect()
              .width,
          })

          // 确定插入位置
          if (isOnlyCard) {
            dragOverPosition.value = y < rect.top + rect.height / 2 ? 'before' : 'after'
            logger.debug('垂直插入位置:', dragOverPosition.value)
          } else {
            dragOverPosition.value = x < rect.left + rect.width / 2 ? 'before' : 'after'
            logger.debug('水平插入位置:', dragOverPosition.value)
          }
        }
      }
    })
  }

  // 节流检查函数
  let lastCheckTime = 0
  const throttledCheck = () => {
    const now = Date.now()
    if (now - lastCheckTime > 50) {
      lastCheckTime = now

      // 检查垃圾篓和卡片悬停
      if (draggedArticle.value?.isOwner) {
        checkOverTrashBin()
      }
      checkOverCard()
    }
  }

  // 开始拖拽
  const startDragging = (
    event: MouseEvent | TouchEvent,
    article: Article,
    avatarElement: HTMLElement,
  ) => {
    // 进入拖拽状态
    isDragging.value = true
    draggedArticle.value = article

    // 只有文章拥有者才显示垃圾篓
    if (article.isOwner) {
      showTrashBin.value = true
      logger.debug('显示垃圾篓 - 文章拥有者')
    }

    // 获取初始位置
    const { clientX, clientY } = getEventCoordinates(event)
    dragPosition.value = { x: clientX, y: clientY }

    // 创建克隆元素
    const cardElement = avatarElement.closest('.card-item') as HTMLElement
    if (cardElement) {
      const rect = cardElement.getBoundingClientRect()
      clonedElement.value = createDragClone(cardElement, rect, dragPosition.value)
    }

    // 设置事件监听器
    eventHandlers.value = setupDragEvents({
      onMove: handleDragMove,
      onEnd: handleDragEnd,
    })

    options.onDragStart?.(article)
  }

  // 处理拖拽移动
  const handleDragMove = (event: MouseEvent | TouchEvent) => {
    if (!isDragging.value) return

    event.preventDefault()
    const { clientX, clientY } = getEventCoordinates(event)
    dragPosition.value = { x: clientX, y: clientY }

    // 更新克隆元素位置
    if (clonedElement.value) {
      clonedElement.value.style.left = `${clientX}px`
      clonedElement.value.style.top = `${clientY}px`
    }

    // 节流检查
    throttledCheck()
  }

  // 处理拖拽结束
  const handleDragEnd = () => {
    if (!isDragging.value || isEnding.value) return

    isEnding.value = true
    logger.debug('拖拽结束')

    // 清理事件监听器
    if (eventHandlers.value) {
      eventHandlers.value()
      eventHandlers.value = null
    }

    // 处理拖拽结果
    if (isOverTrashBin.value && draggedArticle.value?.isOwner) {
      logger.debug('触发删除操作')
      options.onDelete?.(draggedArticle.value)
    } else if (dragOverCardId.value && dragOverPosition.value && draggedArticle.value) {
      logger.debug('触发重新排序操作')
      options.onReorder?.(draggedArticle.value.id, dragOverCardId.value, dragOverPosition.value)
    }

    // 清理克隆元素
    if (clonedElement.value) {
      cleanupDragElement(clonedElement.value)
      clonedElement.value = null
    }

    // 重置状态
    resetDragState()

    options.onDragEnd?.()

    // 延迟重置防抖标志
    setTimeout(() => {
      isEnding.value = false
    }, 100)
  }

  return {
    startDragging,
    handleDragMove,
    handleDragEnd,
  }
}
