package com.shenmo.wen.app.core.user.controller;

import com.shenmo.wen.app.core.user.pojo.req.WenUserNotificationSettingsReq;
import com.shenmo.wen.app.core.user.service.WenUserService;
import com.shenmo.wen.common.pojo.response.ResponseData;
import com.shenmo.wen.modules.user.pojo.domain.WenSearchUser;
import com.shenmo.wen.modules.user.pojo.req.WenUserSearchReq;
import com.shenmo.wen.modules.user.pojo.resp.WenUserResp;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("users")
@RequiredArgsConstructor
public class WenUserController {

    private final WenUserService service;

    @GetMapping("/online")
    public ResponseData<Long> online() {
        return ResponseData.success(service.online());
    }

    @GetMapping("/me")
    public ResponseData<WenUserResp> info() {
        return ResponseData.success(service.info());
    }

    @PutMapping("/me/avatar")
    public ResponseData<String> changeAvatar(@RequestParam("file") MultipartFile avatarFile) throws IOException {
        return ResponseData.success(service.changeAvatar(avatarFile));
    }

    /**
     * 更新当前用户的通知接收类型
     * @param req 通知设置请求
     * @return 是否更新成功
     */
    @PutMapping("/me/notification-settings")
    public ResponseData<Boolean> updateNotificationSettings(@RequestBody WenUserNotificationSettingsReq req) {
        return ResponseData.success(service.updateNotificationReceiveType(req.getType()));
    }

    @GetMapping
    public ResponseData<List<WenSearchUser>> search(WenUserSearchReq req) {
        return ResponseData.success(service.search(req.getUsername()));
    }
}
