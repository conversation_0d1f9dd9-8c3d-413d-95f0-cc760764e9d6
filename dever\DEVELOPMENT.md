# 开发指南

## 开发环境设置

### 依赖安装
```bash
pip install -r requirements.txt
```

### 主要依赖
- `tkinter` - GUI界面
- `pyautogui` - 自动化操作
- `pygetwindow` - 窗口管理
- `opencv-python` - 图像处理
- `pillow` - 图像操作
- `pyperclip` - 剪贴板操作

## 项目架构

### 核心模块
1. **GUI模块** (`gui_automation.py`)
   - 提供图形化配置界面
   - 管理用户设置和参数
   - 控制自动化脚本的启动和停止

2. **自动化模块** (`cursor_augment_automation.py`)
   - 窗口检测和激活
   - 按钮状态识别
   - 自动化操作执行

3. **测试模块** (`test_*.py`)
   - 按钮检测测试
   - 功能验证测试
   - 安装验证测试

### 配置系统
- `config.json` - 主配置文件
- `config_single.json` - 单次执行配置
- 支持运行时配置更新

## 开发流程

### 添加新功能
1. 在相应模块中实现功能
2. 更新配置文件结构（如需要）
3. 在GUI中添加相应控件
4. 编写测试用例
5. 更新文档

### 测试
```bash
# 测试按钮检测
python test_button_detection.py

# 测试新窗口功能
python test_new_window_feature.py

# 测试安装
python test_installation.py
```

### 提交规范
使用conventional commits格式：
- `feat:` 新功能
- `fix:` 修复bug
- `docs:` 文档更新
- `test:` 测试相关
- `refactor:` 重构代码

## 调试技巧

### 模板匹配调试
1. 使用`test_button_detection.py`验证模板匹配
2. 调整`match_threshold`参数
3. 检查模板图片质量和尺寸

### 窗口检测调试
1. 检查Cursor窗口标题
2. 验证窗口激活状态
3. 确认面板区域截取正确

### 自动化操作调试
1. 启用详细日志记录
2. 调整操作延迟时间
3. 验证热键和点击位置
