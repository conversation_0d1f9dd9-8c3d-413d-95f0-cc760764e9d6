package com.shenmo.wen.app.core.notification.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shenmo.wen.app.core.article.mapper.WenArticleShareMapper;
import com.shenmo.wen.app.core.article.pojo.entity.WenArticleShare;
import com.shenmo.wen.app.core.favorite.mapper.WenFavoriteMapper;
import com.shenmo.wen.app.core.notification.mapper.WenNotificationMapper;
import com.shenmo.wen.app.core.notification.mapper.WenNotificationUserReadMapper;
import com.shenmo.wen.app.core.notification.pojo.entity.WenNotification;
import com.shenmo.wen.app.core.notification.pojo.entity.WenNotificationUserRead;
import com.shenmo.wen.app.core.notification.pojo.req.WenNotificationLoadReq;
import com.shenmo.wen.app.core.notification.pojo.resp.WenNotificationResp;
import com.shenmo.wen.app.core.notification.service.WenNotificationService;
import com.shenmo.wen.app.core.user.exception.UserExceptionEnum;
import com.shenmo.wen.common.enumeration.NotificationReceiveTypeEnum;
import com.shenmo.wen.common.pojo.response.PageResult;
import com.shenmo.wen.common.util.AssertUtils;
import com.shenmo.wen.modules.user.mapper.WenUserMapper;
import com.shenmo.wen.modules.user.pojo.entity.WenUser;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class WenNotificationServiceImpl implements WenNotificationService {
    private final WenNotificationMapper mapper;
    private final WenNotificationUserReadMapper notificationUserReadMapper;
    private final WenUserMapper userMapper;
    private final WenArticleShareMapper articleShareMapper;
    private final WenFavoriteMapper favoriteMapper;

    @Override
    public PageResult<WenNotificationResp> load(WenNotificationLoadReq req) {
        // 获取当前登录用户信息
        final long loginId = StpUtil.getLoginIdAsLong();
        final WenUser user = userMapper.byId(loginId);
        final Long ctTm = user.getCtTm();
        AssertUtils.isTrue(Objects.nonNull(ctTm), UserExceptionEnum.USER_NOT_EXISTS);

        // 获取用户通知接收类型
        Integer notificationReceiveTypeCode = user.getNotificationReceiveType();
        if (Objects.isNull(notificationReceiveTypeCode)) {
            notificationReceiveTypeCode = NotificationReceiveTypeEnum.FAVORITE.getCode();
        }
        NotificationReceiveTypeEnum notificationReceiveType = NotificationReceiveTypeEnum
                .of(notificationReceiveTypeCode);

        // 获取分享和收藏的文章ID
        final List<Long> sharedArticleIds = new ArrayList<>();
        final List<Long> favoriteArticleIds = new ArrayList<>();
        final boolean isShare = notificationReceiveType == NotificationReceiveTypeEnum.SHARE;
        final boolean isFavorite = notificationReceiveType == NotificationReceiveTypeEnum.FAVORITE;
        final boolean isAll = notificationReceiveType == NotificationReceiveTypeEnum.ALL;

        // 对于"全部"和"分享"类型，都需要获取分享给自己的文章ID
        if (isShare || isAll) {
            sharedArticleIds.addAll(articleShareMapper.listArticleIdByUserId(loginId));
        }

        if (isFavorite) {
            favoriteArticleIds.addAll(favoriteMapper.listArticleIdByUserId(loginId));
        }

        if (isShare && CollectionUtils.isEmpty(sharedArticleIds)) {
            return new PageResult<>(new Page<>(req.getPageNum(), req.getPageSize()), List.of());
        }
        if (isFavorite && CollectionUtils.isEmpty(favoriteArticleIds)) {
            return new PageResult<>(new Page<>(req.getPageNum(), req.getPageSize()), List.of());
        }

        // 创建分页对象
        Page<WenNotificationResp> page = new Page<>(req.getPageNum(), req.getPageSize());

        // 执行查询
        IPage<WenNotificationResp> resultPage;
        if (notificationReceiveType == NotificationReceiveTypeEnum.CLOSE) {
            // 关闭类型下不获取任何通知
            resultPage = new Page<>(req.getPageNum(), req.getPageSize());
        } else {
            resultPage = mapper.pageFilteredVoByUser(
                    page,
                    loginId,
                    ctTm / 1000,
                    notificationReceiveTypeCode,
                    favoriteArticleIds,
                    sharedArticleIds);
        }

        // 返回结果
        return new PageResult<>(resultPage, resultPage.getRecords());
    }

    @Override
    public void read(Long id) {
        final long loginId = StpUtil.getLoginIdAsLong();

        // 获取通知详情
        WenNotification notification = mapper.selectById(id);
        if (notification == null) {
            return;
        }

        // 先检查通知是否已读，未读时才标记为已读
        Integer isRead = notificationUserReadMapper.checkRead(id, loginId);
        if (isRead == 0) {
            // 未读，标记为已读
            WenNotificationUserRead record = new WenNotificationUserRead();
            record.setId(IdWorker.getId());
            record.setNotificationId(id);
            record.setUserId(loginId);
            record.setCtTm(System.currentTimeMillis());
            notificationUserReadMapper.insert(record);

            // 检查是否所有目标用户都已读该通知
            checkAndUpdateGlobalReadStatus(notification);
        }
        // 已读则不做任何操作
    }

    @Override
    public void unread(Long id) {
        final long loginId = StpUtil.getLoginIdAsLong();

        // 删除已读记录
        notificationUserReadMapper.delete(
            new LambdaQueryWrapper<WenNotificationUserRead>()
                .eq(WenNotificationUserRead::getNotificationId, id)
                .eq(WenNotificationUserRead::getUserId, loginId)
        );

        // 如果通知之前是全部已读状态，现在需要更新为未全部已读
        WenNotification notification = mapper.selectById(id);
        if (notification != null && notification.getIsRead() == 1) {
            notification.setIsRead(0);
            mapper.updateById(notification);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void readAll() {
        // 获取用户信息
        final long userId = StpUtil.getLoginIdAsLong();
        final WenUser user = userMapper.byId(userId);
        final Long ctTm = user.getCtTm();
        AssertUtils.isTrue(Objects.nonNull(ctTm), UserExceptionEnum.USER_NOT_EXISTS);

        // 获取用户通知接收类型
        Integer notificationReceiveTypeCode = user.getNotificationReceiveType();
        if (Objects.isNull(notificationReceiveTypeCode)) {
            notificationReceiveTypeCode = NotificationReceiveTypeEnum.FAVORITE.getCode();
        }
        NotificationReceiveTypeEnum notificationReceiveType = NotificationReceiveTypeEnum
                .of(notificationReceiveTypeCode);

        // 获取分享和收藏的文章ID
        final List<Long> sharedArticleIds = new ArrayList<>();
        final List<Long> favoriteArticleIds = new ArrayList<>();
        final boolean isShare = notificationReceiveType == NotificationReceiveTypeEnum.SHARE;
        final boolean isFavorite = notificationReceiveType == NotificationReceiveTypeEnum.FAVORITE;
        final boolean isAll = notificationReceiveType == NotificationReceiveTypeEnum.ALL;

        // 对于"全部"和"分享"类型，都需要获取分享给自己的文章ID
        if (isShare || isAll) {
            sharedArticleIds.addAll(articleShareMapper.listArticleIdByUserId(userId));
        }

        if (isFavorite) {
            favoriteArticleIds.addAll(favoriteMapper.listArticleIdByUserId(userId));
        }

        if (isShare && CollectionUtils.isEmpty(sharedArticleIds)) {
            return;
        }
        if (isFavorite && CollectionUtils.isEmpty(favoriteArticleIds)) {
            return;
        }

        // 获取未读通知ID列表
        List<Long> unreadNotificationIds;
        if (notificationReceiveType == NotificationReceiveTypeEnum.CLOSE) {
            return; // 关闭类型下不处理任何通知
        } else {
            unreadNotificationIds = mapper.getUnreadNotificationIdsForReadAll(
                    userId,
                    ctTm / 1000,
                    notificationReceiveTypeCode,
                    favoriteArticleIds,
                    sharedArticleIds);
        }

        if (unreadNotificationIds.isEmpty()) {
            return;
        }

        // 批量生成已读记录
        List<WenNotificationUserRead> readRecords = unreadNotificationIds.stream()
            .map(notificationId -> {
                WenNotificationUserRead record = new WenNotificationUserRead();
                record.setId(IdWorker.getId());
                record.setNotificationId(notificationId);
                record.setUserId(userId);
                record.setCtTm(System.currentTimeMillis());
                return record;
            })
            .collect(Collectors.toList());

        // 批量插入已读记录
        for (WenNotificationUserRead record : readRecords) {
            notificationUserReadMapper.insert(record);
        }

        // 检查并更新通知的全局已读状态
        for (Long notificationId : unreadNotificationIds) {
            WenNotification notification = mapper.selectById(notificationId);
            if (notification != null) {
                checkAndUpdateGlobalReadStatus(notification);
            }
        }
    }

    @Override
    public Long totalUnread() {
        // 获取当前登录用户信息
        final long loginId = StpUtil.getLoginIdAsLong();
        final WenUser user = userMapper.byId(loginId);
        final Long ctTm = user.getCtTm();
        AssertUtils.isTrue(Objects.nonNull(ctTm), UserExceptionEnum.USER_NOT_EXISTS);

        // 获取用户通知接收类型
        Integer notificationReceiveTypeCode = user.getNotificationReceiveType();
        if (Objects.isNull(notificationReceiveTypeCode)) {
            notificationReceiveTypeCode = NotificationReceiveTypeEnum.FAVORITE.getCode();
        }
        NotificationReceiveTypeEnum notificationReceiveType = NotificationReceiveTypeEnum
                .of(notificationReceiveTypeCode);

        // 如果通知接收类型为关闭，直接返回0
        if (notificationReceiveType == NotificationReceiveTypeEnum.CLOSE) {
            return 0L;
        }

        // 获取分享和收藏的文章ID
        final List<Long> sharedArticleIds = (notificationReceiveType == NotificationReceiveTypeEnum.SHARE
                || notificationReceiveType == NotificationReceiveTypeEnum.ALL)
                ? articleShareMapper.listArticleIdByUserId(loginId)
                : null;
        final List<Long> favoriteArticleIds = notificationReceiveType == NotificationReceiveTypeEnum.FAVORITE
                ? favoriteMapper.listArticleIdByUserId(loginId)
                : null;

        if (notificationReceiveType == NotificationReceiveTypeEnum.SHARE && CollectionUtils.isEmpty(sharedArticleIds)) {
            return 0L;
        }
        if (notificationReceiveType == NotificationReceiveTypeEnum.FAVORITE
                && CollectionUtils.isEmpty(favoriteArticleIds)) {
            return 0L;
        }

        // 执行查询
        return mapper.countUnreadNotifications(
                loginId,
                ctTm / 1000,
                notificationReceiveTypeCode,
                favoriteArticleIds,
                sharedArticleIds);
    }

    /**
     * 检查并更新通知的全局已读状态
     * 如果所有目标用户都已读，则将通知标记为全局已读
     */
    private void checkAndUpdateGlobalReadStatus(WenNotification notification) {
        if (notification == null || notification.getIsRead() == 1) {
            return;
        }

        // 获取已读用户数量
        Long readUserCount = notificationUserReadMapper.selectCount(
            new LambdaQueryWrapper<WenNotificationUserRead>()
                .eq(WenNotificationUserRead::getNotificationId, notification.getId())
        );

        // 获取目标用户总数
        Long targetUserCount;

        if (notification.getCommentId() != null && notification.getCommentNotificationUserId() != null) {
            // 评论通知：计算@提及的用户数量
            String[] mentionUserIds = notification.getCommentNotificationUserId().split(",");
            targetUserCount = (long) mentionUserIds.length;
        } else if (notification.getArticleId() != null) {
            // 文章通知：计算文章作者和分享用户数量
            targetUserCount = 1L; // 文章作者

            // 加上分享用户数量
            Long sharedUserCount = articleShareMapper.selectCount(
                new LambdaQueryWrapper<WenArticleShare>()
                    .eq(WenArticleShare::getArticleId, notification.getArticleId())
            );

            targetUserCount += sharedUserCount;
        } else {
            // 系统通知：所有用户
            targetUserCount = userMapper.selectCount(null);
        }

        // 如果已读用户数量等于目标用户总数，则表示所有用户都已读
        if (readUserCount != null && targetUserCount != null && readUserCount.equals(targetUserCount)) {
            notification.setIsRead(1);
            mapper.updateById(notification);
        }
    }
}
