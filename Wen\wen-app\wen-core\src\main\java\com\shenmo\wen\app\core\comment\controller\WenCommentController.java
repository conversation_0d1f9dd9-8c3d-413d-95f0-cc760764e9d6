package com.shenmo.wen.app.core.comment.controller;

import com.shenmo.wen.app.core.comment.pojo.domain.WenCommonLocation;
import com.shenmo.wen.app.core.comment.pojo.req.WenCommentLoadReq;
import com.shenmo.wen.app.core.comment.pojo.req.WenCommentSaveReq;
import com.shenmo.wen.app.core.comment.pojo.req.WenCommentSearchReq;
import com.shenmo.wen.app.core.comment.pojo.resp.WenCommentResp;
import com.shenmo.wen.app.core.comment.service.WenCommentService;
import com.shenmo.wen.common.pojo.response.ResponseData;

import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/comments")
@RequiredArgsConstructor
public class WenCommentController {

    private final WenCommentService service;

    @PostMapping
    public ResponseData<Long> save(@Validated @RequestBody WenCommentSaveReq req) {
        return ResponseData.success(service.save(req));
    }

    @GetMapping
    public ResponseData<List<WenCommentResp>> load(@Validated WenCommentLoadReq req) {
        return ResponseData.success(service.load(req));
    }

    @GetMapping("/{id}")
    public ResponseData<WenCommentResp> loadById(@PathVariable("id") Long id) {
        return ResponseData.success(service.loadById(id));
    }

    @GetMapping("/{id}/parents")
    public ResponseData<List<WenCommentResp>> parents(@PathVariable("id") Long id) {
        return ResponseData.success(service.parents(id));
    }

    @GetMapping("/{id}/location")
    public ResponseData<WenCommonLocation> location(@PathVariable("id") Long id) {
        return ResponseData.success(service.location(id));
    }

    @GetMapping("/search")
    public ResponseData<List<WenCommentResp>> search(WenCommentSearchReq req) {
        return ResponseData.success(service.search(req));
    }
}
