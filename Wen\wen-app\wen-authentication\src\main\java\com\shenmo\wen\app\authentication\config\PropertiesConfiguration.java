package com.shenmo.wen.app.authentication.config;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.shenmo.wen.app.authentication.config.properties.CloudflareTurnstileProperties;
import com.shenmo.wen.app.authentication.config.properties.UserConfigProperties;
import com.shenmo.wen.modules.user.config.properties.EmailProperties;

/**
 *
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties({ UserConfigProperties.class, CloudflareTurnstileProperties.class, EmailProperties.class })
public class PropertiesConfiguration {
}
