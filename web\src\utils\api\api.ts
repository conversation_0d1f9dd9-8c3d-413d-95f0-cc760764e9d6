import { type AxiosRequestConfig, type AxiosResponse } from 'axios'

import service from '@/config/request'

// 定义通用的请求参数类型
type RequestParams = Record<string, string | number | boolean | null | undefined>

const api = {
  // GET 请求 - 返回完整的 AxiosResponse
  get: <T = unknown>(
    url: string,
    params?: RequestParams,
    config?: AxiosRequestConfig,
  ): Promise<AxiosResponse<T>> => {
    return service.get(url, { ...config, params })
  },

  // POST 请求 - 返回完整的 AxiosResponse
  post: <T = unknown>(url: string, data?: RequestParams): Promise<AxiosResponse<T>> => {
    return service.post(url, data)
  },

  // POST FormData 请求 - 返回完整的 AxiosResponse
  postFormData: <T = unknown>(
    url: string,
    data?: FormData,
    config?: AxiosRequestConfig,
  ): Promise<AxiosResponse<T>> => {
    return service.post(url, data, config)
  },

  // PUT 请求 - 返回完整的 AxiosResponse
  put: <T = unknown>(url: string, data?: RequestParams): Promise<AxiosResponse<T>> => {
    return service.put(url, data)
  },

  // PUT FormData 请求 - 返回完整的 AxiosResponse
  putFormData: <T = unknown>(
    url: string,
    data?: FormData,
    config?: AxiosRequestConfig,
  ): Promise<AxiosResponse<T>> => {
    return service.put(url, data, config)
  },

  // PATCH 请求 - 返回完整的 AxiosResponse
  patch: <T = unknown>(url: string, data?: RequestParams): Promise<AxiosResponse<T>> => {
    return service.patch(url, data)
  },

  // PATCH FormData 请求 - 返回完整的 AxiosResponse
  patchFormData: <T = unknown>(
    url: string,
    data?: FormData,
    config?: AxiosRequestConfig,
  ): Promise<AxiosResponse<T>> => {
    return service.patch(url, data, config)
  },

  // DELETE 请求 - 返回完整的 AxiosResponse
  del: <T = unknown>(url: string, params?: RequestParams): Promise<AxiosResponse<T>> => {
    return service.delete(url, { params })
  },
}

export default api
