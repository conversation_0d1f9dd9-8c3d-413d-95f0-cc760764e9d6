<template>
  <div
    class="long-press-wrapper"
    @mousedown="startPress"
    @mouseup="endPress"
    @mouseleave="cancelPress"
    @touchstart.passive="startPress"
    @touchend.passive="endPress"
    @touchcancel.passive="cancelPress"
  >
    <div :class="['slot-content', { 'long-press-active': isLongPress }]">
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

// 接收长按事件的触发时长（单位：毫秒）
const props = defineProps({
  duration: {
    type: Number,
    default: 500,
  },
})

// 定义事件发射器
const emit = defineEmits<{
  (e: 'long-press'): void // 长按事件
  (e: 'click'): void // 点击事件
}>()

// 定义计时器和时间记录
let pressTimer: number | ReturnType<typeof setTimeout> | null = null
let pressStartTime: number | null = null
let clickTimeout: number | ReturnType<typeof setTimeout> | null = null

// 定义长按状态
const isLongPress = ref(false)

// 开始长按计时
const startPress = () => {
  pressStartTime = Date.now()
  pressTimer = setTimeout(() => {
    isLongPress.value = true // 设置为长按状态
    emit('long-press') // 触发长按事件
  }, props.duration)
}

// 结束长按计时
const endPress = () => {
  if (pressTimer !== null) {
    clearTimeout(pressTimer) // 清除计时器
    const pressDuration = Date.now() - (pressStartTime ?? 0)

    if (pressDuration < props.duration) {
      // 延迟点击事件，避免与长按事件冲突
      if (clickTimeout !== null) {
        clearTimeout(clickTimeout)
      }
      clickTimeout = setTimeout(() => {
        emit('click') // 如果时间小于阈值，触发点击事件
      }, 200) // 延迟 200 毫秒触发点击事件
    }
  }
  // 重置长按状态
  isLongPress.value = false
}

// 取消长按
const cancelPress = () => {
  if (pressTimer !== null) {
    clearTimeout(pressTimer) // 清除计时器
  }
  isLongPress.value = false
}
</script>
<style lang="scss" scoped>
@use '@/styles/ui-elements/long-press';
</style>
