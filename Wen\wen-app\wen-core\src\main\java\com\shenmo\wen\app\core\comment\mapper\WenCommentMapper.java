package com.shenmo.wen.app.core.comment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.shenmo.wen.app.core.comment.pojo.entity.WenComment;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WenCommentMapper extends BaseMapper<WenComment> {

    default Boolean existsById(Long id) {
        return exists(Wrappers.<WenComment>lambdaQuery().eq(WenComment::getId, id));
    }


    default void incrementLikeCount(Long id) {
        update(Wrappers.<WenComment>lambdaUpdate().eq(WenComment::getId, id).setIncrBy(WenComment::getLikeCount, 1));
    }


    default void incrementDislikeCount(Long id) {
        update(Wrappers.<WenComment>lambdaUpdate().eq(WenComment::getId, id).setIncrBy(WenComment::getDislikeCount, 1));
    }


    default void incrementFavoriteCount(Long id) {
        update(Wrappers.<WenComment>lambdaUpdate().eq(WenComment::getId, id).setIncrBy(WenComment::getFavoriteCount, 1));
    }


    default void incrementReplyCount(Long id) {
        update(Wrappers.<WenComment>lambdaUpdate().eq(WenComment::getId, id).setIncrBy(WenComment::getReplyCount, 1));
    }


    default void decrementLikeCount(Long id) {
        update(Wrappers.<WenComment>lambdaUpdate().eq(WenComment::getId, id).gt(WenComment::getLikeCount, 0).setDecrBy(WenComment::getLikeCount, 1));
    }


    default void decrementDislikeCount(Long id) {
        update(Wrappers.<WenComment>lambdaUpdate().eq(WenComment::getId, id).gt(WenComment::getDislikeCount, 0).setDecrBy(WenComment::getDislikeCount, 1));
    }


    default void decrementFavoriteCount(Long id) {
        update(Wrappers.<WenComment>lambdaUpdate().eq(WenComment::getId, id).gt(WenComment::getFavoriteCount, 0).setDecrBy(WenComment::getFavoriteCount, 1));
    }


    default Integer favoriteCountById(Long id) {
        return selectOne(Wrappers.<WenComment>lambdaQuery().select(WenComment::getFavoriteCount).eq(WenComment::getId, id)).getFavoriteCount();
    }

    default WenComment likeAndDislikeCountById(Long id) {
        return selectOne(Wrappers.<WenComment>lambdaQuery()
                .select(WenComment::getLikeCount, WenComment::getDislikeCount)
                .eq(WenComment::getId, id));
    }

    @Select("""
            select distinct article_id from wen_comment where user_id = #{userId}
            """)
    List<Long> listArticleIdsByUserId(@Param("userId") Long userId);

    @Select("""
            select id from wen_comment where find_in_set(#{userId}, mention_user_id)
            """)
    List<Long> listIdsByMentionUserId(@Param("userId") Long userId);

    /**
     * 删除文章的所有评论
     * 
     * @param articleId 文章ID
     */
    default void deleteByArticleId(Long articleId) {
        delete(Wrappers.<WenComment>lambdaQuery().eq(WenComment::getArticleId, articleId));
    }
}
