/*
 * Login 视图组件样式
 * 登录页面布局样式定义，包括登录表单、注册表单和页面布局
 */

@use '../auth';

.layout-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  height: 100dvh;
  position: relative;
  z-index: 1;
}

.card-container {
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22.5rem;
  height: 200px;
  min-height: 200px;
  z-index: 2;
  filter: drop-shadow(0 0 10px rgba(0, 0, 0, 10%));

  @media (height <= 700px) {
    height: 120px;
    min-height: 120px;
  }
}

.footer-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1.5rem;
  z-index: 2;
}

// 登录模式切换
.login-mode-switch {
  margin-bottom: 0.5rem;
}

// 验证码输入容器
.email-code-container {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.email-code-input {
  width: 65%;
  flex-shrink: 0;
}

// 通用文本按钮样式
.send-code-btn {
  flex-shrink: 0;
  font-size: 13px;
  padding: 0;
  min-width: auto;
  height: auto;
}

// 忘记密码链接样式
.forgot-password-link {
  text-align: right;
}
