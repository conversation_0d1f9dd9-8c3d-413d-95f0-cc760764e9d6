package com.shenmo.wen.app.core.user.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shenmo.wen.common.mybatis.handler.TimestampToLongTypeHandler;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

/**
 * 用户特权验证流程实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName(value = "wen_user_privilege_verification", autoResultMap = true)
public class WenUserPrivilegeVerification {

    /**
     * 验证流程ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 申请用户ID
     */
    private Long userId;

    /**
     * 申请的特权ID
     */
    private Long privilegeId;

    /**
     * 当前步骤：1,2,3
     */
    private Integer currentStep;

    /**
     * 状态：0-进行中，1-成功，2-失败，3-超时
     */
    private Integer status;

    /**
     * 验证页面URL
     */
    private String pageUrl;

    /**
     * 验证内容(短信验证码/二维码解析内容)
     */
    private String content;

    /**
     * 流程过期时间
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long expireTime;

    /**
     * 创建时间
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long ctTm;

    /**
     * 页面访问时间戳（用户点击邮件链接的时间）
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long pageAccessTime;

    /**
     * 自动完成时间戳（页面访问时间+30秒）
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long autoCompleteTime;
}
