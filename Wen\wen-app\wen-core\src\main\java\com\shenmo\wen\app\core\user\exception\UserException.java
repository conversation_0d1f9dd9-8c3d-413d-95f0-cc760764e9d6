package com.shenmo.wen.app.core.user.exception;

import com.shenmo.wen.common.exception.BaseException;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import org.springframework.http.HttpStatus;

/**
 *
 * <AUTHOR>
 */
public class UserException extends BaseException {
    public UserException(ExceptionEnum exceptionEnum) {
        super(exceptionEnum);
    }

    public UserException(HttpStatus httpStatus, String message) {
        super(httpStatus, message);
    }

    public UserException(HttpStatus httpStatus, String description, Throwable throwable) {
        super(httpStatus, description, throwable);
    }

    public UserException(HttpStatus httpStatus, String description, String message) {
        super(httpStatus, description, message);
    }
}
