/**
 * TiptapEditor组件引用类型定义
 * 定义TipTap编辑器组件引用的类型
 */

import type { EditorWithFormatPainter } from '@/types/tiptap/editor-with-format-painter.types'

import type { TiptapEditorElement } from './tiptap-editor-element.types'
import type { JSONContent } from '@tiptap/vue-3'

/** TipTap编辑器引用类型 */
export type TiptapEditorRef = TiptapEditorElement | null

/**
 * TipTap编辑器组件引用接口
 * 包含编辑器实例和操作方法
 */
export interface TiptapEditorComponentRef {
  /** 编辑器实例（带格式刷功能） */
  editor: EditorWithFormatPainter
  /** 设置编辑器内容 */
  setContent: (content: JSONContent) => void
  /** 清空编辑器内容 */
  clearContent: () => void
  /** 获取Markdown格式内容 */
  getMarkdown: () => string | undefined
  /** 处理保存操作 */
  handleSave: () => void
}
