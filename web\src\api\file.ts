import config from '@/config'
import { type ResponseData } from '@/types/api/response-data.types'
import api from '@/utils/api/api'

/**
 * 文件相关API接口
 * 提供文件上传、图片上传和资源URL获取功能
 */
const fileApi = {
  /** API基础路径 */
  URL: '/core/files',

  /** 支持的图片文件类型 */
  imageTypes: ['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/webp'],

  /**
   * 上传文件
   * 将文件上传到指定的存储桶
   * @param file 要上传的文件对象
   * @param bucket 存储桶名称
   * @returns 返回上传成功的文件URI字符串
   */
  upload: async (file: File, bucket: string): Promise<ResponseData<string>> => {
    const formData = new FormData()
    formData.append('bucket', bucket)
    formData.append('file', file)
    const response = await api.postFormData<ResponseData<string>>(fileApi.URL, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    })
    return response.data
  },

  /**
   * 上传图片文件
   * 验证文件类型后上传图片到指定存储桶
   * @param file 要上传的图片文件
   * @param bucket 存储桶名称
   * @returns 返回上传成功的图片URI字符串
   * @throws 当文件类型不是图片时抛出错误
   */
  uploadImage: async (file: File, bucket: string): Promise<ResponseData<string>> => {
    if (fileApi.imageTypes.includes(file.type)) {
      return fileApi.upload(file, bucket)
    }
    return Promise.reject(new Error('文件类型不支持，请上传图片文件'))
  },

  /**
   * 获取资源完整URL
   * 将相对路径转换为完整的资源访问URL
   * @param uri 资源URI，可以是相对路径或完整URL
   * @returns 返回完整的资源访问URL
   */
  getResourceURL: (uri: string): string => {
    if (!uri) {
      return ''
    }
    if (uri.startsWith('http')) {
      return uri
    }
    return `${config.backend.resourceURL}${uri}`
  },
}

export default fileApi
