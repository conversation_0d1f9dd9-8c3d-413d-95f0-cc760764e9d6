// 上传成功效果动画
@keyframes upload-success {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }

  30% {
    opacity: 0.7;
    transform: scale(1);
  }

  100% {
    opacity: 0;
    transform: scale(1.05);
  }
}

// 选中效果动画
@keyframes selected-node-pulse {
  0% {
    box-shadow:
      0 0 0 2px rgba(45, 140, 240, 50%),
      0 0 5px rgba(45, 140, 240, 20%);
  }

  50% {
    box-shadow:
      0 0 0 2px rgba(45, 140, 240, 50%),
      0 0 10px rgba(45, 140, 240, 40%);
  }

  100% {
    box-shadow:
      0 0 0 2px rgba(45, 140, 240, 50%),
      0 0 5px rgba(45, 140, 240, 20%);
  }
}

// 确保动画在不支持Web Animations API的浏览器上也有基本效果
// 排除 B站播放器组件和图片包装器，避免蓝色边框
.ProseMirror-selectednode:not(.bilibili-container, .image-wrapper) {
  transition:
    outline 0.3s ease,
    box-shadow 0.3s ease;
  box-shadow:
    0 0 0 2px rgba(45, 140, 240, 50%),
    0 0 8px rgba(45, 140, 240, 30%);
}

// 图片包装器的特殊处理 - 边框应该贴合图片而不是包装器
.image-wrapper.ProseMirror-selectednode {
  box-shadow: none;

  img {
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.1),
      0 0 0 2px rgba(45, 140, 240, 50%),
      0 0 8px rgba(45, 140, 240, 30%);
  }
}

.image-wrapper.ProseMirror-selectednode .resize-handle {
  opacity: 1;
  transition: transform 0.2s ease;
}
