/* 暗色主题相关样式 */

/* 注意：颜色变量现在统一在 variables/_colors.scss 中管理 */

/* 这里只保留特定组件的暗色主题样式覆盖 */

/* 自定义一些特定组件在暗色模式下的样式 */
.dark-theme .article-content {
  background-color: var(--dark-gray);
  color: var(--black);
}

.dark-theme .card-item {
  background-color: var(--white-2);
}

.dark-theme .article-title {
  color: var(--black);
}

.dark-theme .article-title:hover {
  color: var(--blue);
}

.dark-theme .comment-danmaku-item {
  color: var(--black);
}

/* 文章详情页深色模式样式 */
.dark-theme .article-layout {
  background-color: var(--creamy-white-1);
}

.dark-theme .article-info-container {
  background-color: var(--dark-gray-1);
}

.dark-theme .article-info-container .article-header {
  color: var(--black);
}

.dark-theme .article-layout .article-info-container .article-content {
  background-color: var(--dark-gray);
}

/* ProseMirror编辑器在深色模式下的样式 */
.dark-theme .ProseMirror {
  color: var(--black);
}

.dark-theme .ProseMirror h1,
.dark-theme .ProseMirror h2,
.dark-theme .ProseMirror h3,
.dark-theme .ProseMirror h4,
.dark-theme .ProseMirror h5,
.dark-theme .ProseMirror h6 {
  color: var(--black);
}

.dark-theme .ProseMirror a {
  color: var(--blue);
}

[data-theme='dark'] .ProseMirror blockquote,
.dark-theme .ProseMirror blockquote,
.dark-theme .tiptap-editor-wrapper .ProseMirror blockquote,
.dark-theme .editor-content .ProseMirror blockquote {
  border-left: 3px solid var(--blockquote-border-dark);
  color: var(--gray-5);
}

.dark-theme .ProseMirror pre {
  background-color: var(--white-2);
}

.dark-theme .ProseMirror code {
  background-color: var(--white-2);
  color: var(--black);
}

/* 评论组件深色模式样式 */
.dark-theme .comment-info-container {
  background-color: var(--comment-info-bg);
  color: var(--black);
}

/* 确保评论列表容器背景色与评论信息容器一致 */
.dark-theme .comment-list-container {
  background-color: var(--comment-list-bg);
}

/* 普通评论容器背景色需要保持原有的白色2，在深色模式下有足够的对比度 */
.dark-theme .user-comment-container {
  background-color: var(--comment-container-bg);
  border-color: var(--gray-3);
}

.dark-theme .user-comment-container-fixed,
.dark-theme .comment-flash {
  background-color: var(--comment-fixed-bg);
  border-color: var(--gray-3);
  opacity: 1; /* 确保不透明 */
}

.dark-theme .user-nickname,
.dark-theme .user-extra-info,
.dark-theme .comment-content-row,
.dark-theme .comment-interaction-reply {
  color: var(--black);
}
