import fileApi from '@/api/file'
import { setupModalEvents } from '@/composables/image/useImageModalEvents'
import { useImagePreviewDrag } from '@/composables/image/useImagePreviewDrag'
import { THUMBNAIL } from '@/constants/image/filepath.constants'

/**
 * 弹幕图片预览事件发射器接口
 */
interface DanmakuImageEmit {
  (e: 'image-preview-open'): void
  (e: 'image-preview-close'): void
}

/**
 * 打开弹幕图片预览模态框
 * 实现图片点击放大查看功能，支持原图加载
 * 与编辑器图片行为保持一致：默认缩略图，预览展示原图，预览后缓存原图
 *
 * @param img 预览的图片元素
 * @param originalSrc 原始图片地址
 * @param emit 事件发射函数
 */
export function openDanmuImageViewer(
  img: HTMLImageElement,
  originalSrc: string,
  emit: DanmakuImageEmit,
  event?: Event,
): void {
  // 阻止事件冒泡
  event?.preventDefault()
  event?.stopPropagation()

  // 发送暂停弹幕事件
  emit('image-preview-open')

  // 创建模态框
  const modal = document.createElement('div')
  modal.classList.add('modal-overlay')

  // 创建预览图
  const previewImg = document.createElement('img')
  previewImg.alt = '图片预览'
  modal.appendChild(previewImg)
  document.body.appendChild(modal)

  // 触发模态框显示动画
  modal.classList.add('modal-overlay-active')

  // 检查图片是否已经是原图
  const isAlreadyOriginal = img.dataset.isOriginal === 'true'
  const thumbnailSrc = img.dataset.thumbnailSrc || originalSrc

  // 判断是否为外部图片
  const isExternalImage = originalSrc.startsWith('http://') || originalSrc.startsWith('https://')

  if (!isExternalImage && thumbnailSrc.includes(THUMBNAIL) && !isAlreadyOriginal) {
    // 处理缩略图预览
    previewImg.src = img.src
    previewImg.style.opacity = '0.5'

    // 添加加载动画
    const loadingSpinner = document.createElement('div')
    loadingSpinner.classList.add('loading-spinner')
    modal.appendChild(loadingSpinner)

    // 加载原图
    const originalImg = new Image()
    const animationStartTime = Date.now()
    const minDisplayTime = 500

    originalImg.onload = () => {
      const loadTime = Date.now() - animationStartTime

      const setOriginal = () => {
        loadingSpinner.style.display = 'none'
        previewImg.src = originalImg.src
        previewImg.style.opacity = '1'

        // 将原图URL保存到预览图，以便关闭模态框后更新
        previewImg.dataset.originalFullUrl = originalImg.src
      }

      if (loadTime < minDisplayTime) {
        setTimeout(setOriginal, minDisplayTime - loadTime)
      } else {
        setOriginal()
      }
    }

    originalImg.src = fileApi.getResourceURL(originalSrc.replace(THUMBNAIL, ''))
  } else {
    // 已经是原图，直接显示
    previewImg.src = img.src
    previewImg.style.opacity = '1'
  }

  // 关闭模态框函数
  const closeModal = () => {
    modal.classList.remove('modal-overlay-active')
    modal.addEventListener(
      'transitionend',
      () => {
        if (!modal.classList.contains('modal-overlay-active')) {
          // 如果已经加载了原图，则更新图片元素的src为原图URL（仅限本地图片）
          if (
            !isExternalImage &&
            previewImg.dataset.originalFullUrl &&
            thumbnailSrc.includes(THUMBNAIL) &&
            !isAlreadyOriginal
          ) {
            // 更新图片元素为原图
            img.src = previewImg.dataset.originalFullUrl

            // 标记图片已经是原图状态
            img.dataset.isOriginal = 'true'

            // 更新原始路径，移除thumbnail标记
            const newOriginalSrc = originalSrc.replace(THUMBNAIL, '')
            img.dataset.originalSrc = newOriginalSrc
          }

          document.body.removeChild(modal)
          cleanupModalEvents()
          dragHandler.cleanup()

          // 发送恢复弹幕事件
          emit('image-preview-close')
        }
      },
      { once: true },
    )
  }

  // 添加拖拽和缩放功能
  const dragHandler = useImagePreviewDrag(
    previewImg,
    {
      minScale: 0.5,
      maxScale: 3,
      scaleStep: 0.1,
    },
    closeModal,
    modal,
  ) // 传入关闭回调和模态框元素

  // 初始化拖拽功能
  dragHandler.initialize()

  // 设置模态框通用事件处理
  const cleanupModalEvents = setupModalEvents(modal, closeModal, dragHandler.handleWheelZoom)
}
