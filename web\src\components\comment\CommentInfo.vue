<template>
  <div ref="commentInfoRef" class="comment-info-container">
    <!-- 评论头部组件 -->
    <CommentHeader
      :breadcrumb="commentState.breadcrumb.value"
      :model-value="commentState.sortType.value"
      @update:model-value="handleSortChange"
      @breadcrumb-click="handleBreadcrumbClick"
      @location-comment="commentState.locationComment"
    />

    <!-- 评论列表组件 -->
    <CommentList
      ref="commentListRef"
      :comment-list="commentState.commentList.value"
      :flash-comment-id="commentState.flashCommentId.value"
      :show-reply-list-btn="commentState.showReplyListBtn.value"
      :comment-input-visible="commentInteraction.commentInputVisible.value"
      :comment-scroll-trigger="commentState.commentScrollTrigger.value"
      :comment-loading="commentState.commentLoading.value"
      :comment-no-more="commentState.commentNoMore.value"
      :has-comment-permission="commentState.hasCommentPermission.value"
      :quick-reply-loading="commentInteraction.quickReplyLoading.value"
      @load-more-comments="commentState.loadCurrentCommentList(false)"
      @show-reply-list="showReplyList"
      @handle-comment-reply-click="handleCommentReplyClick"
      @interaction-btn="commentInteraction.interactionBtn"
      @favorite-btn="commentInteraction.favoriteBtn"
      @quick-reply-comment="debouncedQuickReplyComment"
      @update-editor="commentInteraction.updateEditor"
      @update-comment-ref="commentState.updateCommentRef"
    />

    <!-- 评论输入组件 -->
    <Transition name="slide-up" appear>
      <CommentMainInput
        v-show="
          commentState.hasCommentPermission.value &&
          commentInteraction.commentInputVisible.value == '-1'
        "
        :comment-reply="commentInteraction.commentReply.value"
        @update:comment-reply="commentInteraction.commentReply.value = $event"
        :send-comment-loading="commentInteraction.sendCommentLoading.value"
        @send-comment="debouncedSendComment"
        ref="commentMainInputRef"
      />
    </Transition>
  </div>
</template>

<script lang="ts" setup>
// 引入Vue核心功能
import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'

// 引入composables
import CommentHeader from '@/components/comment/CommentHeader.vue'
import CommentList from '@/components/comment/CommentList.vue'
import CommentMainInput from '@/components/comment/CommentMainInput.vue'
import { useCommentDynamicLoad } from '@/composables/comment/useCommentDynamicLoad'
import { useCommentInteraction } from '@/composables/comment/useCommentInteraction'
import { useCommentState } from '@/composables/comment/useCommentState'
import type { Comment } from '@/types/comment/comment.types'
import { activeTheme } from '@/utils/theme/theme'

import type { JSONContent } from '@tiptap/vue-3'
import type { PropType } from 'vue'

// Props 和 Emits 定义
const props = defineProps({
  articleId: {
    type: Function as PropType<() => string>,
    required: true,
  },
})

const emit = defineEmits<{
  (e: 'sendEnd'): void
  (e: 'quickReplyEnd'): void
}>()

// Refs
const inputBoxHeight = ref(0)
const commentInfoRef = ref()
const commentMainInputRef = ref()
const commentListRef = ref()

// 获取评论列表容器引用
const commentListContainerRef = computed(() => {
  return commentListRef.value?.commentListContainerRef || null
})

// 动态加载逻辑
const commentDynamicLoad = useCommentDynamicLoad(
  computed(() => commentState.commentList.value),
  commentListContainerRef,
)

// 状态管理 - 传入动态加载量函数和加载完成回调
const commentState = useCommentState(
  props.articleId,
  () => commentDynamicLoad.calculatedLoadSize.value,
  () => commentDynamicLoad.markInitialLoadComplete(),
)
const commentInteraction = useCommentInteraction(props.articleId as () => string)

// 输入框高度管理 - 改为调整容器高度而不是padding
const updateInputBoxHeight = () => {
  nextTick(() => {
    const commentListContainer = document.querySelector(
      '.comment-list-container',
    ) as HTMLElement | null

    if (commentListContainer) {
      // 检查是否为移动端
      const isMobile = window.innerWidth <= 768

      // 检查是否有快速评论框展开
      const hasQuickReply = commentInteraction.commentInputVisible.value !== '-1'
      const hasMainInput = commentInteraction.commentInputVisible.value === '-1'

      if (isMobile) {
        // 移动端使用简化的高度管理策略
        if (hasQuickReply) {
          // 快捷回复展开时，主输入框隐藏，自动释放空间
          commentListContainer.style.paddingBottom = '20px'
        } else if (hasMainInput) {
          // 主输入框显示时，需要为其预留空间
          commentListContainer.style.paddingBottom = '150px' // 防止被主输入框遮挡
        } else {
          commentListContainer.style.paddingBottom = '150px'
        }
      } else {
        // 桌面端保持原有逻辑
        if (hasQuickReply) {
          commentListContainer.style.paddingBottom = '1.25rem'
        } else if (hasMainInput && commentMainInputRef.value?.commentInputWrapperRef) {
          inputBoxHeight.value = commentMainInputRef.value.commentInputWrapperRef.offsetHeight || 0
          commentListContainer.style.paddingBottom = `${inputBoxHeight.value + 12}px`
        } else {
          commentListContainer.style.paddingBottom = '1.25rem'
        }
      }
    }
  })
}

// 延迟更新高度，等待动画完成
const updateInputBoxHeightWithDelay = () => {
  // 立即更新一次
  updateInputBoxHeight()

  // 等待动画完成后再次更新，确保高度准确
  setTimeout(() => {
    updateInputBoxHeight()
  }, 350) // 350ms 对应快速评论框动画时长
}

// 在组件挂载时初始化
onMounted(() => {
  // 初始化权限状态（不再发送单独的权限检查请求）
  commentState.initCommentPermission()
  setAffixWidth()
  window.addEventListener('resize', setAffixWidth)

  // 加载评论（权限检查集成在加载过程中）
  const commentId = commentState.getCommentId()
  if (commentId) {
    commentState.locationComment(commentId)
  } else {
    commentState.loadCurrentCommentList()
  }
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', setAffixWidth)

  // 清理溢出控制类
  const body = document.body
  const commentInfoContainer = document.querySelector('.comment-info-container')
  body.classList.remove('comment-reply-active')
  if (commentInfoContainer) {
    commentInfoContainer.classList.remove('has-quick-reply')
  }
})

// 主题管理
const handleThemeChange = () => {
  nextTick(() => {
    const allEditors = document.querySelectorAll(
      '.ProseMirror, .editor-content, .tiptap-editor-wrapper',
    )
    allEditors.forEach((editor) => {
      if (editor instanceof HTMLElement) {
        const oldBackgroundColor = editor.style.backgroundColor
        editor.style.backgroundColor = 'transparent'
        void editor.offsetHeight
        editor.style.backgroundColor = oldBackgroundColor
      }
    })
  })
}

// Watch 监听器
watch(
  () => commentInteraction.commentInputVisible.value,
  (newValue) => {
    // 添加或移除防止滚动条的类
    const body = document.body
    const commentInfoContainer = document.querySelector('.comment-info-container')

    if (newValue !== '-1') {
      // 快速评论框展开时，添加防止滚动的类
      body.classList.add('comment-reply-active')
      if (commentInfoContainer) {
        commentInfoContainer.classList.add('has-quick-reply')
      }
    } else {
      // 快速评论框收起时，移除防止滚动的类
      body.classList.remove('comment-reply-active')
      if (commentInfoContainer) {
        commentInfoContainer.classList.remove('has-quick-reply')
      }
    }

    updateInputBoxHeightWithDelay()
  },
)

watch(
  () => commentState.hasCommentPermission.value,
  (newVal) => {
    if (newVal) {
      nextTick(updateInputBoxHeight)
    }
  },
)

watch(activeTheme, handleThemeChange)

watch(
  () => commentInteraction.commentReply.value,
  (newVal: JSONContent | undefined) => {
    // 只有在内容完全为空时才重置，避免在用户输入过程中误清空
    if (newVal && newVal.content && Array.isArray(newVal.content)) {
      // 检查是否所有段落都为空
      const hasContent = newVal.content?.some((paragraph: JSONContent) => {
        if (paragraph.content && Array.isArray(paragraph.content)) {
          return paragraph.content.some((item: JSONContent) => {
            // 检查文本内容
            if (item.type === 'text' && item.text && item.text.trim()) {
              return true
            }
            // 检查其他非文本内容（如图片、mention等）
            if (item.type !== 'text') {
              return true
            }
            return false
          })
        }
        return false
      })

      // 只有在确实没有任何内容时才重置
      if (!hasContent) {
        commentInteraction.commentReply.value = undefined
      }
    }
  },
)

// 布局管理
const setAffixWidth = () => {
  nextTick(() => {
    if (commentInfoRef.value && commentMainInputRef.value?.commentInputWrapperRef) {
      const parentWidth = commentInfoRef.value.offsetWidth
      commentMainInputRef.value.commentInputWrapperRef.style.width = `${parentWidth}px`
      updateInputBoxHeight()
    }
  })
}

// 评论交互处理
const handleBreadcrumbClick = (index: number) => {
  commentState.resetCommentList()
  commentDynamicLoad.resetToInitialLoad() // 重置动态加载状态
  if (index !== commentState.lastBreadcrumbIndex.value) {
    commentState.breadcrumb.value.splice(index + 1)
  }
  const clearAllReplies = commentInteraction.clearAllQuickReplyContent()
  clearAllReplies({ commentList: commentState.commentList.value })
  commentState.loadCommentList(commentState.breadcrumb.value[index])
}

const showReplyList = (comment: Comment) => {
  commentState.resetCommentList()
  commentDynamicLoad.resetToInitialLoad() // 重置动态加载状态
  const clearAllReplies = commentInteraction.clearAllQuickReplyContent()
  clearAllReplies({ commentList: commentState.commentList.value })
  commentState.addBreadcrumb(comment)
  commentState.loadCommentList(comment)
}

const handleCommentReplyClick = (comment: Comment) => {
  commentInteraction.handleCommentReplyClick(comment, {
    isLastBreadcrumb: commentState.isLastBreadcrumb.value,
  })
}

// 使用项目自带的防抖方法为快捷回复添加防抖
const debouncedQuickReplyComment = (comment: Comment) => {
  commentInteraction.debouncedQuickReplyComment(comment, {
    isLastBreadcrumb: commentState.isLastBreadcrumb.value,
    onSuccess: (commentId) => {
      commentState.locationComment(commentId)
      commentInteraction.commentInputVisible.value = '-1'
    },
  })
  emit('quickReplyEnd')
}

// 使用项目自带的防抖方法为发送评论添加防抖
const debouncedSendComment = () => {
  commentInteraction.debouncedSendComment(commentMainInputRef.value, {
    lastBreadcrumbComment: commentState.lastBreadcrumbComment.value,
    onSuccess: (commentId) => {
      commentState.locationComment(commentId)
    },
  })
  emit('sendEnd')
}

// 处理排序变化
const handleSortChange = (value: string) => {
  commentState.sortType.value = value
  commentState.resetCommentList()
  commentDynamicLoad.resetToInitialLoad() // 重置动态加载状态
  commentState.loadCurrentCommentList()
}

// 暴露方法
defineExpose({
  loadCurrentCommentList: commentState.loadCurrentCommentList,
})
</script>

<style lang="scss" scoped>
@use '@/styles/comment/comment-info';
</style>
