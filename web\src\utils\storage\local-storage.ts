import { LOGIN_USER } from '@/constants/user/storage.constants'
import type { JsonStringifyValue } from '@/types/json/json-stringify-value.types'
import type { LoginUserStorage } from '@/types/user/login-user-storage.types'
import { type LoginUser } from '@/types/user/login-user.types'
import Json from '@/utils/data/json'
import { toLoginUserStorage, fromLoginUserStorage } from '@/utils/user/login-user-storage-converter'

class LocalStorage {
  /**
   * 设置数据到 localStorage
   * @param key 存储的键
   * @param value 存储的值，可以是任意类型
   */
  static set<T extends JsonStringifyValue>(key: string, value: T): void {
    const jsonString = Json.stringify(value)
    localStorage.setItem(key, jsonString)
  }

  /**
   * 从 localStorage 获取数据
   * @param key 存储的键
   * @returns 返回解析后的值，若解析失败或不存在则返回 null
   */
  static get<T>(key: string): T | null {
    const jsonString = localStorage.getItem(key)
    if (!jsonString) return null
    return Json.parse(jsonString)
  }

  /**
   * 删除 localStorage 中的指定键
   * @param key 要删除的键
   */
  static remove(key: string): void {
    localStorage.removeItem(key)
  }

  /**
   * 清空 localStorage
   */
  static clear(): void {
    localStorage.clear()
  }

  static getLoginUser(): LoginUser | null {
    const storage = this.get<LoginUserStorage>(LOGIN_USER)
    return storage ? fromLoginUserStorage(storage) : null
  }

  static setLoginUser(user: LoginUser): void {
    const storage = toLoginUserStorage(user)
    this.set(LOGIN_USER, storage)
  }

  static removeLoginUser(): void {
    this.remove(LOGIN_USER)
  }
}

export default LocalStorage
