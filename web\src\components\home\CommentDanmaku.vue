<template>
  <div class="comment-container">
    <!-- 弹幕提示信息 -->
    <div v-if="commentList.length === 0" class="danmaku-empty-hint">
      <div class="hint-content">
        <h3>🎯 弹幕搜索</h3>
        <p>在搜索框中输入关键词，或选择筛选条件来查看相关评论弹幕：</p>
        <ul>
          <li><strong>我的</strong> - 查看我发布的评论</li>
          <li><strong>互动</strong> - 查看我互动过的评论</li>
          <li><strong>收藏</strong> - 查看我收藏的评论</li>
        </ul>
        <p class="hint-note">💡 也可以点击标签来筛选特定主题的评论</p>
      </div>
    </div>

    <Danmaku
      ref="danmakuRef"
      class="comment-danmaku"
      v-model:danmus="commentList"
      v-bind="danmakuConfig"
    >
      <template #dm="{ danmu }">
        <span class="comment-danmaku-item cursor-pointer" @dblclick="handleDanmuClick(danmu)">
          <span class="comment-danmaku-publisher">
            <NAvatar
              round
              :size="28"
              :src="danmu.publisherAvatar"
              object-fit="cover"
              :lazy="true"
            />
            <span>{{ danmu.publisher }}: </span>
          </span>
          <span class="comment-danmaku-content">
            <DanmakuRenderer
              :content="danmu.contentObj"
              @image-preview-open="handleImagePreviewOpen"
              @image-preview-close="handleImagePreviewClose"
            />
          </span>
        </span>
      </template>
    </Danmaku>
  </div>
</template>

<script lang="ts" setup>
import { NAvatar } from 'naive-ui'
import { onMounted, onUnmounted, toRef } from 'vue'

import Danmaku from '@/components/danmaku/Danmaku.vue'
import DanmakuRenderer from '@/components/danmaku/DanmakuRenderer.vue'
import { useCommentDanmakuOperations } from '@/composables/home/<USER>'
import { useCommentDanmakuState } from '@/composables/home/<USER>'

const props = defineProps({
  searchCondition: {
    type: Object,
    required: true,
  },
  loop: {
    type: Boolean,
    default: false,
  },
  pause: {
    type: Boolean,
    default: false,
  },
})

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const emit = defineEmits(['search', 'update:loop', 'update:pause'])

// 初始化状态管理
const { commentList, danmakuRef, danmakuConfig, isSubscribed } = useCommentDanmakuState(
  toRef(props, 'loop'),
  toRef(props, 'pause'),
)

// 初始化操作管理
const {
  clearDanmaku,
  addCommentList,
  handleDanmuClick,
  subscribeComment,
  unsubscribeComment,
  handleImagePreviewOpen,
  handleImagePreviewClose,
  resize,
  play,
  pause,
} = useCommentDanmakuOperations(commentList, danmakuRef, danmakuConfig, isSubscribed)

// 暴露方法给父组件
defineExpose({
  get danmakuLoop() {
    return danmakuConfig.loop
  },
  set danmakuLoop(value: boolean) {
    danmakuConfig.loop = value
  },
  clearDanmaku,
  addCommentList,
  subscribeComment,
  unsubscribeComment,
  resize,
  play,
  pause,
})

onMounted(() => {
  subscribeComment()
})

onUnmounted(() => {
  unsubscribeComment()
})
</script>

<style lang="scss" scoped>
@use '@/styles/comment/comment-list-item';

.comment-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.danmaku-empty-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
  background: var(--n-color);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: var(--n-box-shadow);
  border: 1px solid var(--n-border-color);
  max-width: 400px;
  width: 90%;

  .hint-content {
    h3 {
      margin: 0 0 1rem;
      color: var(--n-text-color);
      font-size: 1.2rem;
    }

    p {
      margin: 0.5rem 0;
      color: var(--n-text-color-2);
      line-height: 1.5;
    }

    ul {
      text-align: left;
      margin: 1rem 0;
      padding-left: 1.5rem;
      color: var(--n-text-color-2);

      li {
        margin: 0.5rem 0;
        line-height: 1.4;

        strong {
          color: var(--n-primary-color);
          font-weight: 600;
        }
      }
    }

    .hint-note {
      font-size: 0.9rem;
      color: var(--n-text-color-3);
      font-style: italic;
      margin-top: 1rem;
    }
  }
}
</style>
