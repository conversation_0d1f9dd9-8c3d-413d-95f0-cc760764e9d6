#!/usr/bin/env node

/**
 * 主题颜色同步脚本
 * 从 SCSS 变量文件生成对应的 TypeScript 类型和配置
 * 确保颜色定义的单一数据源
 *
 * 功能:
 * - 解析 SCSS 颜色映射 ($light-colors 和 $dark-colors)
 * - 生成 TypeScript 接口和颜色对象
 * - 提供颜色管理的工具函数
 * - 验证生成文件的正确性
 *
 * 使用方法:
 * npm run sync-colors
 * 或
 * node scripts/sync-theme-colors.js
 */

import fs from 'fs'
import { fileURLToPath } from 'node:url'
import path from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 配置常量
const CONFIG = {
  SCSS_COLORS_FILE: path.join(__dirname, '../src/styles/variables/color-definitions.scss'),
  TS_COLORS_FILE: path.join(__dirname, '../src/utils/theme-colors.ts'),
  MAX_LINE_LENGTH: 80,
  SUPPORTED_COLOR_FORMATS: ['hex', 'rgb', 'rgba', 'hsl', 'hsla', 'var'],
}

/**
 * 日志工具类
 */
class Logger {
  static info(message) {
    console.log(`ℹ️  ${message}`)
  }

  static success(message) {
    console.log(`✅ ${message}`)
  }

  static warn(message) {
    console.warn(`⚠️  ${message}`)
  }

  static error(message) {
    console.error(`❌ ${message}`)
  }

  static debug(message) {
    if (process.env.DEBUG) {
      console.log(`🐛 ${message}`)
    }
  }
}

/**
 * 颜色值验证器
 */
class ColorValidator {
  // 预编译的颜色格式正则表达式
  static COLOR_PATTERNS = [
    /^#[0-9a-fA-F]{3,8}$/, // hex
    /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/, // rgb
    /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)$/, // rgba
    /^hsl\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*\)$/, // hsl
    /^hsla\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*,\s*[\d.]+\s*\)$/, // hsla
    /^var\(--[\w-]+\)$/, // CSS 变量
    /^[\d.]+rem$/, // rem 单位（用于边框等）
    /^\d+px$/, // px 单位
    /^[\d.]+rem\s+solid\s+rgba?\([^)]+\)$/, // 边框样式
    /^\d+\s+[\d.]+rem\s+[\d.]+rem\s+rgba?\([^)]+\)$/, // 阴影样式
  ]

  /**
   * 验证颜色值格式是否有效
   * @param {string} value 颜色值
   * @returns {boolean} 是否有效
   */
  static isValidColorValue(value) {
    if (!value || typeof value !== 'string') return false

    const trimmedValue = value.trim()
    return this.COLOR_PATTERNS.some((pattern) => pattern.test(trimmedValue))
  }

  /**
   * 获取颜色值类型
   * @param {string} value 颜色值
   * @returns {string} 颜色类型
   */
  static getColorType(value) {
    if (value.startsWith('#')) return 'hex'
    if (value.startsWith('rgb(')) return 'rgb'
    if (value.startsWith('rgba(')) return 'rgba'
    if (value.startsWith('hsl(')) return 'hsl'
    if (value.startsWith('hsla(')) return 'hsla'
    if (value.startsWith('var(')) return 'css-variable'
    if (value.includes('rem') || value.includes('px')) return 'dimension'
    return 'unknown'
  }
}

/**
 * SCSS 解析器类
 */
class ScssParser {
  /**
   * 解析 SCSS 颜色映射
   * @param {string} content SCSS 文件内容
   * @param {string} mapName 映射名称 (light-colors 或 dark-colors)
   * @returns {Object} 解析后的颜色对象
   * @throws {Error} 当找不到映射或解析失败时抛出错误
   */
  static parseColorMap(content, mapName) {
    try {
      Logger.debug(`开始解析 ${mapName} 映射`)

      const mapContent = this.extractMapContent(content, mapName)
      const parseResult = this.parseMapLines(mapContent)

      this.validateParseResult(parseResult, mapName)

      Logger.success(
        `${mapName} 解析完成: ${parseResult.validColors} 个有效颜色${parseResult.invalidColors > 0 ? `, ${parseResult.invalidColors} 个无效项` : ''}`,
      )
      return parseResult.colors
    } catch (error) {
      throw new Error(`解析 ${mapName} 映射时出错: ${error.message}`)
    }
  }

  /**
   * 提取 SCSS 映射内容
   * @param {string} content SCSS 文件内容
   * @param {string} mapName 映射名称
   * @returns {string} 映射内容
   * @throws {Error} 当找不到映射时抛出错误
   */
  static extractMapContent(content, mapName) {
    const mapRegex = new RegExp(`\\$${mapName}:\\s*\\((.*?)\\);`, 's')
    const match = content.match(mapRegex)

    if (!match) {
      throw new Error(`找不到 ${mapName} 映射。请检查 SCSS 文件中是否存在 ${mapName} 定义`)
    }

    return match[1]
  }

  /**
   * 解析映射行内容
   * @param {string} mapContent 映射内容
   * @returns {{colors: Object, validColors: number, invalidColors: number}} 解析结果
   */
  static parseMapLines(mapContent) {
    const colors = {}
    let lineNumber = 0
    let validColors = 0
    let invalidColors = 0

    const lines = mapContent.split('\n')
    for (const line of lines) {
      lineNumber++
      const trimmed = line.trim()

      // 跳过空行和注释
      if (!trimmed || trimmed.startsWith('//')) continue

      const parseLineResult = this.parseColorLine(trimmed, lineNumber)

      if (parseLineResult.success) {
        colors[parseLineResult.name] = parseLineResult.value
        validColors++
        Logger.debug(`解析颜色: ${parseLineResult.name} = ${parseLineResult.value}`)
      } else {
        invalidColors++
      }
    }

    return { colors, validColors, invalidColors }
  }

  /**
   * 解析单行颜色定义
   * @param {string} line 行内容
   * @param {number} lineNumber 行号
   * @returns {{success: boolean, name?: string, value?: string}} 解析结果
   */
  static parseColorLine(line, lineNumber) {
    // 匹配格式: 'color-name': value,
    const colorMatch = line.match(/['"]([^'"]+)['"]:\s*(.+?)(?:,\s*$|$)/)

    if (colorMatch) {
      const [, name, value] = colorMatch
      const cleanValue = value.replace(/,$/, '').trim()

      if (!name || !cleanValue) {
        Logger.warn(`第 ${lineNumber} 行颜色定义格式有问题: ${line}`)
        return { success: false }
      }

      // 验证颜色值
      if (!ColorValidator.isValidColorValue(cleanValue)) {
        Logger.warn(`第 ${lineNumber} 行颜色值格式可能无效: ${name} = ${cleanValue}`)
      }

      return { success: true, name, value: cleanValue }
    } else {
      Logger.warn(`无法解析第 ${lineNumber} 行: ${line}`)
      return { success: false }
    }
  }

  /**
   * 验证解析结果
   * @param {Object} parseResult 解析结果
   * @param {string} mapName 映射名称
   * @throws {Error} 当没有有效颜色定义时抛出错误
   */
  static validateParseResult(parseResult, mapName) {
    if (Object.keys(parseResult.colors).length === 0) {
      throw new Error(`${mapName} 映射中没有找到有效的颜色定义`)
    }
  }
}

/**
 * TypeScript 生成器类
 */
class TypeScriptGenerator {
  /**
   * 格式化颜色值为 TypeScript 字符串
   * @param {string} value 颜色值
   * @returns {string} 格式化后的 TypeScript 值
   */
  static formatColorValue(value) {
    // 如果已经是字符串字面量，直接返回
    if (value.startsWith("'") || value.startsWith('"')) {
      return value
    }

    // 所有颜色值都需要加引号以确保 TypeScript 语法正确
    return `'${value}'`
  }

  /**
   * 生成 TypeScript 颜色对象
   * @param {Object} colors 颜色对象
   * @param {string} indent 缩进字符串
   * @returns {string} TypeScript 对象字符串
   */
  static generateColorObject(colors, indent = '  ') {
    const entries = Object.entries(colors).map(([name, value]) => {
      const formattedValue = this.formatColorValue(value)
      const line = `${indent}'${name}': ${formattedValue}`

      // 检查行长度
      if (line.length > CONFIG.MAX_LINE_LENGTH) {
        Logger.warn(`颜色定义行过长 (${line.length} 字符): ${name}`)
      }

      return line
    })

    return entries.join(',\n')
  }

  /**
   * 生成 TypeScript 接口
   * @param {Object} colors 颜色对象
   * @returns {string} TypeScript 接口字符串
   */
  static generateInterface(colors) {
    const properties = Object.keys(colors).map((name) => {
      return `  '${name}': string`
    })

    return properties.join('\n')
  }

  /**
   * 生成完整的 TypeScript 文件内容
   * @param {Object} lightColors 亮色主题颜色
   * @param {Object} darkColors 暗色主题颜色
   * @returns {string} TypeScript 文件内容
   */
  static generateFile(lightColors, darkColors) {
    const interfaceContent = this.generateInterface(lightColors)
    const lightColorsContent = this.generateColorObject(lightColors)
    const darkColorsContent = this.generateColorObject(darkColors)

    return `// 主题颜色配置 - 自动生成，请勿手动修改
// 此文件由 scripts/sync-theme-colors.js 从 SCSS 变量同步生成
// 最后更新时间: ${new Date().toISOString()}

/**
 * 主题颜色接口定义
 * 包含所有可用的颜色变量名称和类型
 */
export interface ThemeColors {
${interfaceContent}
}

/**
 * 亮色主题颜色配置
 * 包含所有亮色模式下的颜色值
 */
export const lightThemeColors: ThemeColors = {
${lightColorsContent}
}

/**
 * 暗色主题颜色配置
 * 包含所有暗色模式下的颜色值
 */
export const darkThemeColors: ThemeColors = {
${darkColorsContent}
}

/**
 * 获取指定主题的颜色配置
 * @param isDark 是否为暗色主题
 * @returns 对应主题的颜色配置对象
 */
export function getThemeColors(isDark: boolean): ThemeColors {
  return isDark ? darkThemeColors : lightThemeColors
}

/**
 * 将颜色对象转换为 CSS 变量对象
 * @param colors 颜色配置对象
 * @returns CSS 变量对象，键名带有 -- 前缀
 */
export function colorsToCSS(colors: ThemeColors): Record<string, string> {
  const cssVars: Record<string, string> = {}
  
  for (const [key, value] of Object.entries(colors)) {
    cssVars[\`--\${key}\`] = value
  }
  
  return cssVars
}

/**
 * 将颜色配置应用到 DOM 根元素
 * @param colors 颜色配置对象
 */
export function applyColorsToDOM(colors: ThemeColors): void {
  const root = document.documentElement
  const cssVars = colorsToCSS(colors)
  
  for (const [property, value] of Object.entries(cssVars)) {
    root.style.setProperty(property, value)
  }
}

/**
 * 获取指定颜色的值
 * @param colorName 颜色名称
 * @param isDark 是否为暗色主题
 * @returns 颜色值，如果不存在则返回 undefined
 */
export function getColorValue(colorName: keyof ThemeColors, isDark: boolean): string | undefined {
  const colors = getThemeColors(isDark)
  return colors[colorName]
}
`
  }
}

/**
 * 文件操作工具类
 */
class FileManager {
  /**
   * 读取并验证 SCSS 文件
   * @returns {string} SCSS 文件内容
   * @throws {Error} 当文件不存在或读取失败时抛出错误
   */
  static readScssFile() {
    try {
      if (!fs.existsSync(CONFIG.SCSS_COLORS_FILE)) {
        throw new Error(`SCSS 颜色文件不存在: ${CONFIG.SCSS_COLORS_FILE}`)
      }

      const content = fs.readFileSync(CONFIG.SCSS_COLORS_FILE, 'utf8')

      if (!content.trim()) {
        throw new Error('SCSS 文件内容为空')
      }

      Logger.success(`成功读取 SCSS 文件: ${CONFIG.SCSS_COLORS_FILE}`)
      return content
    } catch (error) {
      throw new Error(`读取 SCSS 文件失败: ${error.message}`)
    }
  }

  /**
   * 写入 TypeScript 文件
   * @param {string} content 文件内容
   * @throws {Error} 当写入失败时抛出错误
   */
  static writeTypeScriptFile(content) {
    try {
      // 确保目录存在
      const dir = path.dirname(CONFIG.TS_COLORS_FILE)
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
      }

      fs.writeFileSync(CONFIG.TS_COLORS_FILE, content, 'utf8')
      Logger.success(`TypeScript 文件已更新: ${CONFIG.TS_COLORS_FILE}`)
    } catch (error) {
      throw new Error(`写入 TypeScript 文件失败: ${error.message}`)
    }
  }

  /**
   * 验证生成的文件
   * @throws {Error} 当验证失败时抛出错误
   */
  static validateGeneratedFile() {
    try {
      Logger.info('验证生成的文件...')

      if (!fs.existsSync(CONFIG.TS_COLORS_FILE)) {
        throw new Error('生成的 TypeScript 文件不存在')
      }

      const generatedContent = fs.readFileSync(CONFIG.TS_COLORS_FILE, 'utf8')

      const requiredContent = [
        'lightThemeColors',
        'darkThemeColors',
        'ThemeColors',
        'getThemeColors',
        'colorsToCSS',
        'applyColorsToDOM',
      ]

      for (const required of requiredContent) {
        if (!generatedContent.includes(required)) {
          throw new Error(`生成的文件缺少必要内容: ${required}`)
        }
      }

      Logger.success('文件验证通过')
    } catch (error) {
      throw new Error(`文件验证失败: ${error.message}`)
    }
  }
}

/**
 * 颜色一致性验证器
 */
class ConsistencyValidator {
  /**
   * 验证颜色映射的一致性
   * @param {Object} lightColors 亮色主题颜色
   * @param {Object} darkColors 暗色主题颜色
   */
  static validateColorConsistency(lightColors, darkColors) {
    Logger.info('验证颜色映射一致性...')

    const lightKeys = Object.keys(lightColors).sort()
    const darkKeys = Object.keys(darkColors).sort()

    // 检查数量一致性
    if (lightKeys.length !== darkKeys.length) {
      Logger.warn(`颜色数量不一致: 亮色主题 ${lightKeys.length} 个, 暗色主题 ${darkKeys.length} 个`)
    }

    // 检查缺失的颜色
    const missingInDark = lightKeys.filter((key) => !darkKeys.includes(key))
    const missingInLight = darkKeys.filter((key) => !lightKeys.includes(key))

    if (missingInDark.length > 0) {
      Logger.warn(`暗色主题中缺少的颜色: ${missingInDark.join(', ')}`)
    }

    if (missingInLight.length > 0) {
      Logger.warn(`亮色主题中缺少的颜色: ${missingInLight.join(', ')}`)
    }

    // 检查颜色值类型一致性
    const commonKeys = lightKeys.filter((key) => darkKeys.includes(key))
    let typeInconsistencies = 0

    for (const key of commonKeys) {
      const lightType = ColorValidator.getColorType(lightColors[key])
      const darkType = ColorValidator.getColorType(darkColors[key])

      if (lightType !== darkType && lightType !== 'unknown' && darkType !== 'unknown') {
        Logger.warn(`颜色 ${key} 的类型不一致: 亮色(${lightType}) vs 暗色(${darkType})`)
        typeInconsistencies++
      }
    }

    if (typeInconsistencies === 0 && missingInDark.length === 0 && missingInLight.length === 0) {
      Logger.success('颜色映射一致性验证通过')
    } else {
      Logger.warn(
        `发现 ${typeInconsistencies + missingInDark.length + missingInLight.length} 个一致性问题`,
      )
    }
  }
}

/**
 * 主要的同步流程类
 */
class ColorSyncProcessor {
  /**
   * 解析颜色映射
   * @param {string} scssContent SCSS 文件内容
   * @returns {Object} 包含亮色和暗色主题的颜色对象
   */
  static parseColorMaps(scssContent) {
    Logger.info('解析 SCSS 颜色映射...')

    const lightColors = ScssParser.parseColorMap(scssContent, 'light-colors')
    const darkColors = ScssParser.parseColorMap(scssContent, 'dark-colors')

    Logger.success(
      `解析完成: 亮色主题 ${Object.keys(lightColors).length} 个颜色, 暗色主题 ${Object.keys(darkColors).length} 个颜色`,
    )

    return { lightColors, darkColors }
  }

  /**
   * 生成并写入 TypeScript 文件
   * @param {Object} lightColors 亮色主题颜色
   * @param {Object} darkColors 暗色主题颜色
   */
  static generateAndWriteTypeScriptFile(lightColors, darkColors) {
    Logger.info('生成 TypeScript 文件...')

    const tsContent = TypeScriptGenerator.generateFile(lightColors, darkColors)
    FileManager.writeTypeScriptFile(tsContent)
  }

  /**
   * 执行完整的同步流程
   * @param {Object} lightColors 亮色主题颜色
   * @param {Object} darkColors 暗色主题颜色
   */
  static executeSyncProcess(lightColors, darkColors) {
    ConsistencyValidator.validateColorConsistency(lightColors, darkColors)
    this.generateAndWriteTypeScriptFile(lightColors, darkColors)
    FileManager.validateGeneratedFile()
  }
}

/**
 * 主函数 - 协调整个同步流程
 */
function main() {
  const startTime = Date.now()

  try {
    Logger.info('开始同步主题颜色...')
    Logger.debug(`配置信息: SCSS文件=${CONFIG.SCSS_COLORS_FILE}, TS文件=${CONFIG.TS_COLORS_FILE}`)

    const scssContent = FileManager.readScssFile()
    const { lightColors, darkColors } = ColorSyncProcessor.parseColorMaps(scssContent)

    ColorSyncProcessor.executeSyncProcess(lightColors, darkColors)

    const duration = Date.now() - startTime
    Logger.success(`🎨 主题颜色同步完成! 耗时: ${duration}ms`)
  } catch (error) {
    const duration = Date.now() - startTime
    Logger.error(`同步失败 (耗时: ${duration}ms): ${error.message}`)

    if (process.env.DEBUG) {
      console.error('详细错误信息:', error.stack)
      console.error('配置信息:', CONFIG)
    }

    // 提供恢复建议
    Logger.info('💡 故障排除建议:')
    Logger.info('1. 检查 SCSS 文件是否存在且格式正确')
    Logger.info('2. 确保 $light-colors 和 $dark-colors 映射定义完整')
    Logger.info('3. 运行 DEBUG=1 node scripts/sync-theme-colors.js 获取详细日志')

    process.exit(1)
  }
}

// 直接运行主函数
main()

// 导出主要函数供测试使用
export {
  main,
  ScssParser,
  TypeScriptGenerator,
  ColorValidator,
  FileManager,
  ConsistencyValidator,
  ColorSyncProcessor,
}
