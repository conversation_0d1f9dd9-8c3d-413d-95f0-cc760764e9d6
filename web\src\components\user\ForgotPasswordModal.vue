<template>
  <NModal v-model:show="visible" preset="dialog" title="忘记密码">
    <template #header>
      <div class="forgot-password-header">
        <span>重置密码</span>
      </div>
    </template>

    <NForm :model="form" :rules="rules" ref="formRef" label-placement="left" :label-width="100">
      <NFormItem label="邮箱" path="email">
        <NInput v-model:value="form.email" placeholder="请输入注册时的邮箱地址" />
      </NFormItem>
      <NFormItem label="验证码" path="emailCode">
        <div class="email-code-container">
          <NInput v-model:value="form.emailCode" placeholder="请输入邮箱验证码" maxlength="6" />
          <NButton
            :disabled="sendCodeDisabled || !isValidEmail || !isTurnstileVerified"
            @click="handleSendEmailCode"
            text
            type="primary"
            size="small"
            class="send-code-btn"
          >
            {{ sendCodeText }}
          </NButton>
        </div>
      </NFormItem>
      <NFormItem label="新密码" path="newPassword">
        <NInput
          v-model:value="form.newPassword"
          type="password"
          placeholder="请输入新密码"
          show-password-on="click"
        />
      </NFormItem>
      <NFormItem label="确认密码" path="confirmPassword">
        <NInput
          v-model:value="form.confirmPassword"
          type="password"
          placeholder="请再次输入新密码"
          show-password-on="click"
        />
      </NFormItem>

      <!-- Turnstile 验证组件 -->
      <div class="turnstile-section">
        <TurnstileVerification
          ref="turnstileRef"
          @success="handleTurnstileSuccess"
          @error="handleTurnstileError"
        />
      </div>
    </NForm>

    <template #action>
      <div class="forgot-password-actions">
        <NButton @click="handleCancel">取消</NButton>
        <NButton type="primary" @click="handleResetPassword" :loading="loading"> 重置密码 </NButton>
      </div>
    </template>
  </NModal>
</template>

<script setup lang="ts">
import { NModal, NForm, NFormItem, NInput, NButton } from 'naive-ui'
import { ref, computed, watch } from 'vue'

import authApi from '@/api/auth'
import TurnstileVerification from '@/components/verification/TurnstileVerification.vue'
import type { ResponseData } from '@/types/api/response-data.types'
import { EmailCodeType } from '@/types/email/email-code-type.types'
import logger from '@/utils/log/log'
import message from '@/utils/ui/message'

import type { FormItemRule } from 'naive-ui'

// Props
interface Props {
  show: boolean
}

// Emits
interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref()
const turnstileRef = ref()
const loading = ref(false)
const sendCodeDisabled = ref(false)
const sendCodeText = ref('发送验证码')
const countdown = ref(0)

const form = ref({
  email: '',
  emailCode: '',
  newPassword: '',
  confirmPassword: '',
})

// 计算属性
const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value),
})

const isValidEmail = computed(() => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailPattern.test(form.value.email)
})

// Turnstile 验证状态
const turnstileToken = ref('')
const isTurnstileVerified = computed(() => {
  return !!turnstileToken.value
})

// 表单验证规则
const rules = {
  email: [
    { required: true, message: '邮箱不能为空', trigger: 'blur' },
    {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: '请输入有效的邮箱地址',
      trigger: 'blur',
    },
  ],
  emailCode: [
    { required: true, message: '验证码不能为空', trigger: 'blur' },
    { min: 6, max: 6, message: '验证码长度为6位', trigger: 'blur' },
  ],
  newPassword: [
    { required: true, message: '新密码不能为空', trigger: 'blur' },
    { min: 6, message: '密码长度至少为 6 个字符', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (_rule: FormItemRule, value: string) => {
        return value === form.value.newPassword
      },
      message: '两次密码输入不一致',
      trigger: ['blur', 'password-input'],
    },
  ],
}

// Turnstile 验证处理
const handleTurnstileSuccess = (token: string) => {
  logger.debug('Turnstile 验证成功:', token)
  turnstileToken.value = token
}

const handleTurnstileError = () => {
  logger.debug('Turnstile 验证失败')
  turnstileToken.value = ''
}

// 方法
const handleSendEmailCode = async () => {
  if (!form.value.email) {
    message.warning('请先输入邮箱地址')
    return
  }

  if (!isValidEmail.value) {
    message.warning('请输入有效的邮箱地址')
    return
  }

  if (!isTurnstileVerified.value) {
    message.warning('请先通过验证哦~')
    return
  }

  try {
    sendCodeDisabled.value = true
    const response = await authApi.sendEmailCode({
      email: form.value.email,
      type: EmailCodeType.FORGOT,
    })

    if (response.success) {
      message.success('验证码发送成功，请查收邮件')
      startCountdown()
    } else {
      message.error(response.message || '验证码发送失败')
      sendCodeDisabled.value = false
    }
  } catch {
    // 发送失败时重置 Turnstile 验证
    turnstileToken.value = ''
    turnstileRef.value?.reset()
    sendCodeDisabled.value = false
  }
}

const startCountdown = () => {
  countdown.value = 60
  sendCodeText.value = `${countdown.value}s后重发`

  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value > 0) {
      sendCodeText.value = `${countdown.value}s后重发`
    } else {
      sendCodeText.value = '发送验证码'
      sendCodeDisabled.value = false
      clearInterval(timer)
    }
  }, 1000)
}

const handleResetPassword = () => {
  formRef.value.validate((error: boolean) => {
    if (!error) {
      const cftt = turnstileToken.value
      if (!cftt) {
        message.warning('请先通过验证哦~')
        return
      }

      loading.value = true

      authApi
        .resetPassword({
          email: form.value.email,
          emailCode: form.value.emailCode,
          newPassword: form.value.newPassword,
          cftt: cftt,
        })
        .then((res: ResponseData<void>) => {
          if (res.success) {
            message.success('密码重置成功，请使用新密码登录')
            handleCancel()
            emit('success')
          } else {
            message.error(res.message || '密码重置失败')
          }
        })
        .catch(() => {
          // 重置密码失败时重置 Turnstile 验证
          turnstileToken.value = ''
          turnstileRef.value?.reset()
        })
        .finally(() => {
          loading.value = false
        })
    }
  })
}

const handleCancel = () => {
  visible.value = false
  // 清空表单
  form.value = {
    email: '',
    emailCode: '',
    newPassword: '',
    confirmPassword: '',
  }
  // 清空 Turnstile token
  turnstileToken.value = ''
}

// 监听模态框关闭，重置状态
watch(visible, (newVal) => {
  if (!newVal) {
    // 重置验证码发送状态
    sendCodeDisabled.value = false
    sendCodeText.value = '发送验证码'
    countdown.value = 0
  }
})
</script>

<style lang="scss" scoped>
@use '@/styles/user/forgot-password-modal';
</style>
