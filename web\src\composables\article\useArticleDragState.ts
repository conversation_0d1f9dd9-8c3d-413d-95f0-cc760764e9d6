import { ref, computed, onUnmounted } from 'vue'

import type { Article } from '@/types/article/article.types'
import { cleanupDragElement } from '@/utils/drag/drag-clone'
import logger from '@/utils/log/log'

/**
 * 文章拖拽状态管理组合式函数返回值类型
 */
export interface UseArticleDragStateReturn {
  /** 是否正在拖拽 */
  isDragging: import('vue').Ref<boolean>
  /** 被拖拽的文章 */
  draggedArticle: import('vue').Ref<Article | null>
  /** 是否显示垃圾篓 */
  showTrashBin: import('vue').Ref<boolean>
  /** 是否在垃圾篓上方 */
  isOverTrashBin: import('vue').Ref<boolean>
  /** 拖拽悬停的卡片ID */
  dragOverCardId: import('vue').Ref<string | null>
  /** 拖拽悬停位置 */
  dragOverPosition: import('vue').Ref<'before' | 'after' | null>
  /** 是否为单卡片行 */
  isSingleCardRow: import('vue').Ref<boolean>
  /** 拖拽位置 */
  dragPosition: import('vue').Ref<{ x: number; y: number }>
  /** 拖拽样式 */
  dragStyle: import('vue').ComputedRef<Record<string, unknown>>
  /** 是否正在结束拖拽 */
  isEnding: import('vue').Ref<boolean>
  /** 克隆元素引用 */
  clonedElement: import('vue').Ref<HTMLElement | null>
  /** 事件处理器引用 */
  eventHandlers: import('vue').Ref<(() => void) | null>
  /** 强制重置所有状态 */
  forceReset: () => void
  /** 重置拖拽状态 */
  resetDragState: () => void
}

/**
 * 文章拖拽状态管理组合式函数
 * 提供拖拽相关的状态管理功能
 */
export function useArticleDragState(): UseArticleDragStateReturn {
  // 拖拽状态
  const isDragging = ref(false)
  const draggedArticle = ref<Article | null>(null)
  const showTrashBin = ref(false)
  const isOverTrashBin = ref(false)
  const dragOverCardId = ref<string | null>(null)
  const dragOverPosition = ref<'before' | 'after' | null>(null)
  const isSingleCardRow = ref(false)

  // 拖拽元素的位置
  const dragPosition = ref({ x: 0, y: 0 })

  // 克隆的拖拽元素
  const clonedElement = ref<HTMLElement | null>(null)

  // 事件处理器引用
  const eventHandlers = ref<(() => void) | null>(null)

  // 防抖标志
  const isEnding = ref(false)

  // 计算拖拽时的样式
  const dragStyle = computed(() => {
    return {}
  })

  // 重置拖拽状态
  const resetDragState = () => {
    isDragging.value = false
    draggedArticle.value = null
    showTrashBin.value = false
    isOverTrashBin.value = false
    dragOverCardId.value = null
    dragOverPosition.value = null
    isSingleCardRow.value = false
  }

  // 强制重置所有状态
  const forceReset = () => {
    logger.debug('强制重置拖拽状态')

    // 清理事件监听器
    if (eventHandlers.value) {
      eventHandlers.value()
      eventHandlers.value = null
    }

    // 清理克隆元素
    if (clonedElement.value) {
      cleanupDragElement(clonedElement.value)
      clonedElement.value = null
    }

    // 重置所有状态
    resetDragState()

    // 重置防抖标志
    isEnding.value = false
  }

  onUnmounted(() => {
    if (clonedElement.value) {
      cleanupDragElement(clonedElement.value)
    }
    if (eventHandlers.value) {
      eventHandlers.value()
    }
  })

  return {
    isDragging,
    draggedArticle,
    showTrashBin,
    isOverTrashBin,
    dragOverCardId,
    dragOverPosition,
    isSingleCardRow,
    dragPosition,
    dragStyle,
    isEnding,
    clonedElement,
    eventHandlers,
    forceReset,
    resetDragState,
  }
}
