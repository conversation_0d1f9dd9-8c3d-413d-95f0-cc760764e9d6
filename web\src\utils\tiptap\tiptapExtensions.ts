import Blockquote from '@tiptap/extension-blockquote'
import Bold from '@tiptap/extension-bold'
import BubbleMenu from '@tiptap/extension-bubble-menu'
import BulletList from '@tiptap/extension-bullet-list'
import Code from '@tiptap/extension-code'
import Color from '@tiptap/extension-color'
import Document from '@tiptap/extension-document'
import Dropcursor from '@tiptap/extension-dropcursor'
import FloatingMenu from '@tiptap/extension-floating-menu'
import Focus from '@tiptap/extension-focus'
import Gapcursor from '@tiptap/extension-gapcursor'
import Heading from '@tiptap/extension-heading'
import Highlight from '@tiptap/extension-highlight'
import History from '@tiptap/extension-history'
import HorizontalRule from '@tiptap/extension-horizontal-rule'
import Italic from '@tiptap/extension-italic'
import Link from '@tiptap/extension-link'
import ListItem from '@tiptap/extension-list-item'
import OrderedList from '@tiptap/extension-ordered-list'
import Paragraph from '@tiptap/extension-paragraph'
import Strike from '@tiptap/extension-strike'
import TaskList from '@tiptap/extension-task-list'
import Text from '@tiptap/extension-text'
import TextAlign from '@tiptap/extension-text-align'
import TextStyle from '@tiptap/extension-text-style'
import Typography from '@tiptap/extension-typography'
import Underline from '@tiptap/extension-underline'
import { all, createLowlight } from 'lowlight'
import { Markdown } from 'tiptap-markdown'

import Bilibili from '@/components/tiptap/extensions/bilibili'
import CustomCodeBlock from '@/components/tiptap/extensions/code-block'
import Fullscreen from '@/components/tiptap/extensions/fullscreen'
import Image from '@/components/tiptap/extensions/image'
import Mention from '@/components/tiptap/extensions/mention'
import {
  SlashMenuExtension,
  createDefaultSlashMenuItems,
} from '@/components/tiptap/extensions/slash-menu'
import CustomTaskItem from '@/components/tiptap/extensions/task-item'
import type { ExtensionEntry } from '@/types/tiptap/extension-entry.types'

import type { Extension } from '@tiptap/vue-3'

const lowlight = createLowlight(all)

/**
 * TipTap编辑器扩展配置
 * 包含所有编辑器扩展的配置和初始化
 */
export const extensionEntries: ExtensionEntry[] = [
  ['document', Document],
  ['paragraph', Paragraph],
  ['text', Text],
  ['image', Image],
  ['dropcursor', Dropcursor],
  ['bold', Bold],
  ['italic', Italic],
  ['strike', Strike],
  ['underline', Underline],
  ['code', Code],
  ['heading', Heading],
  [
    'bulletList',
    BulletList.configure({
      keepMarks: true,
    }),
  ],
  [
    'orderedList',
    OrderedList.configure({
      keepMarks: true,
    }),
  ],
  ['listItem', ListItem],
  [
    'taskList',
    TaskList.configure({
      itemTypeName: 'taskItem',
    }),
  ],
  [
    'taskItem',
    CustomTaskItem.configure({
      nested: true,
      onReadOnlyChecked: (_node, _checked) => {
        // 在只读模式下默认阻止复选框状态改变
        return false
      },
    }),
  ],
  ['blockquote', Blockquote],
  ['textStyle', TextStyle],
  [
    'color',
    Color.configure({
      types: ['textStyle'],
    }),
  ],
  [
    'backgroundColor',
    Highlight.configure({
      multicolor: true,
    }),
  ],
  [
    'codeBlockLowlight',
    CustomCodeBlock.configure({
      lowlight,
    }),
  ],
  ['horizontalRule', HorizontalRule],
  [
    'link',
    Link.configure({
      defaultProtocol: 'https',
    }),
  ],
  ['history', History],
  ['typography', Typography],
  [
    'markdown',
    Markdown.configure({
      transformPastedText: true,
    }),
  ],
  [
    'focus',
    Focus.configure({
      mode: 'deepest',
    }),
  ],
  ['gapcursor', Gapcursor],
  ['mention', Mention as Extension],
  ['bilibili', Bilibili as Extension],
  ['floatingMenu', FloatingMenu],
  ['bubbleMenu', BubbleMenu],
  [
    'align',
    TextAlign.configure({
      types: ['heading', 'paragraph', 'blockquote'],
      alignments: ['left', 'center', 'right', 'justify'],
      defaultAlignment: 'left',
    }),
  ],
  ['fullscreen', Fullscreen],
  [
    'slashMenu',
    SlashMenuExtension.configure({
      items: createDefaultSlashMenuItems(),
    }),
  ],
]

/**
 * 创建扩展映射表
 * @returns 扩展名称到扩展实例的映射
 */
export function createExtensionMap(): Map<string, Extension> {
  return new Map(extensionEntries) as any
}
