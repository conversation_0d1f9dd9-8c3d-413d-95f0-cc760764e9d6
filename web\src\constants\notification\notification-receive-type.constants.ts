/**
 * 通知接收类型枚举
 */
export enum NotificationReceiveType {
  CLOSE = 0,
  ALL = 1,
  PUBLISH = 2,
  MODIFY = 3,
  FAVORITE = 4,
  SHARE = 5,
}

/**
 * 通知接收类型标签映射
 */
export const NOTIFICATION_RECEIVE_TYPE_LABEL: Record<NotificationReceiveType, string> = {
  [NotificationReceiveType.CLOSE]: '关闭',
  [NotificationReceiveType.ALL]: '全部',
  [NotificationReceiveType.PUBLISH]: '发布',
  [NotificationReceiveType.MODIFY]: '修改',
  [NotificationReceiveType.FAVORITE]: '收藏',
  [NotificationReceiveType.SHARE]: '分享',
} as const
