// 工具类样式
// 提供常用的工具类，如布局、间距、显示等

// 显示相关
.display-block {
  display: block;
}

.display-flex {
  display: flex;
}

.display-none {
  display: none;
}

// 光标样式
.cursor-pointer {
  cursor: pointer;
}

// Flex 布局工具类
.flex-column-start {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.flex-column-end {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.flex-column-gap12 {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.flex-column-gap24 {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.flex-between-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 间距工具类
.padding-0 {
  padding: 0;
}

.padding-2 {
  padding: 2px;
}

.padding-4 {
  padding: 4px;
}

.margin-0 {
  margin: 0;
}

.mr-1 {
  margin-right: 1rem;
}

.mr-2 {
  margin-right: 2rem;
}

// 背景色工具类
.background-white {
  background-color: white;
}

// 用户选择控制工具类
.no-select {
  user-select: none;
}

.text-select {
  user-select: text;
}

.all-select {
  user-select: all;
}

// 图标专用工具类
.icon-no-select {
  user-select: none;
}
