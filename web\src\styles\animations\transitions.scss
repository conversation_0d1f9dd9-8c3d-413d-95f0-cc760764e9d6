/*
 * 通用过渡动画样式
 * 定义项目中常用的过渡动画效果
 */

/* Vue 过渡动画类 */

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 滑动展开/收起动画 */
.slide-down-enter-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top;
}

.slide-down-leave-active {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.6, 1);
  transform-origin: top;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px) scaleY(0.8);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-5px) scaleY(0.9);
}

/* 滑动上升动画 */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 缩放动画 */
.scale-enter-active,
.scale-leave-active {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.scale-enter-from,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* 评论回复框专用动画 */
.comment-reply-enter-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top center;
}

.comment-reply-leave-active {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.6, 1);
  transform-origin: top center;
}

.comment-reply-enter-from {
  opacity: 0;
  transform: translateY(-15px) scaleY(0.7);
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  margin-top: 0;
  margin-bottom: 0;
}

.comment-reply-leave-to {
  opacity: 0;
  transform: translateY(-8px) scaleY(0.85);
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  margin-top: 0;
  margin-bottom: 0;
}

/* 快捷回复框动画 */
.quick-reply-enter-active {
  transition: all 0.35s cubic-bezier(0.25, 0.8, 0.25, 1);
  transform-origin: top center;
}

.quick-reply-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
  transform-origin: top center;
}

.quick-reply-enter-from {
  opacity: 0;
  transform: translateY(-20px) scaleY(0.6);
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.quick-reply-leave-to {
  opacity: 0;
  transform: translateY(-10px) scaleY(0.8);
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
}

/* 弹性动画效果 */
.bounce-enter-active {
  animation: bounce-in 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.bounce-leave-active {
  animation: bounce-out 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(-20px);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05) translateY(-5px);
  }

  70% {
    transform: scale(0.98) translateY(0);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes bounce-out {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }

  100% {
    opacity: 0;
    transform: scale(0.8) translateY(-10px);
  }
}

/* 渐进式展开动画 */
.expand-enter-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.expand-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  opacity: 0;
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  margin-top: 0;
  margin-bottom: 0;
}

/* 组合动画：淡入 + 滑动 + 缩放 */
.smooth-enter-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.smooth-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
}

.smooth-enter-from {
  opacity: 0;
  transform: translateY(-15px) scale(0.95);
}

.smooth-leave-to {
  opacity: 0;
  transform: translateY(-8px) scale(0.98);
}
