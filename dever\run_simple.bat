@echo off
echo Starting Cursor Augment Code Automation...

python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

pip show pyautogui >nul 2>&1
if errorlevel 1 (
    echo Installing dependencies...
    pip install pyautogui PyGetWindow opencv-python Pillow numpy pywin32
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        echo Please run install_manual.bat
        pause
        exit /b 1
    )
)

if not exist "templates\send_button_normal.png" (
    echo Warning: Missing normal button template
    echo Please create template files first
    echo Run: python test_button_detection.py
    pause
    exit /b 0
)

if not exist "templates\send_button_pause.png" (
    echo Warning: Missing pause button template
    echo Please create template files first
    echo Run: python test_button_detection.py
    pause
    exit /b 0
)

echo Starting automation script...
python cursor_augment_automation.py

if errorlevel 1 (
    echo Script execution failed
) else (
    echo Script execution completed
)

pause
