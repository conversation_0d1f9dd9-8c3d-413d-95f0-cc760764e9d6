package com.shenmo.wen.app.gateway.config;

import org.springframework.boot.web.reactive.function.client.WebClientCustomizer;
import org.springframework.cloud.client.loadbalancer.reactive.ReactorLoadBalancerExchangeFilterFunction;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 负载均衡 WebClient 配置类
 * 使用 WebClientCustomizer 来配置负载均衡
 *
 * <AUTHOR>
 */
@Configuration
public class LoadBalancedWebClientConfig {

    /**
     * 通过 WebClientCustomizer 配置负载均衡
     * 这种方式更加灵活，不需要使用 @LoadBalanced 注解
     */
    @Bean
    public WebClientCustomizer loadBalancerWebClientCustomizer(
            ReactorLoadBalancerExchangeFilterFunction loadBalancerFunction) {
        return webClientBuilder -> webClientBuilder.filter(loadBalancerFunction);
    }
}
