package com.shenmo.wen.app.core.article.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shenmo.wen.app.core.article.pojo.entity.WenArticleShare;
import com.shenmo.wen.common.mybatis.handler.TimestampToLongTypeHandler;
import com.shenmo.wen.modules.user.pojo.entity.WenUser;

import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WenArticleShareMapper extends BaseMapper<WenArticleShare> {

    /**
     * 根据文章ID删除分享记录
     *
     * @param articleId 文章ID
     * @return 影响的行数
     */
    @Delete("""
            DELETE FROM wen_article_share WHERE article_id = #{articleId}
            """)
    int deleteByArticleId(@Param("articleId") Long articleId);

    /**
     * 批量插入分享记录
     *
     * @param shares 分享记录列表
     * @return 影响的行数
     */
    @Insert("""
            <script>
                INSERT INTO wen_article_share (id, article_id, user_id)
                VALUES
                <foreach collection="shares" item="share" separator=",">
                    (#{share.id}, #{share.articleId}, #{share.userId})
                </foreach>
            </script>
                """)
    int batchInsert(@Param("shares") List<WenArticleShare> shares);

    /**
     * 根据用户ID查询分享给该用户的文章ID列表
     *
     * @param userId 用户ID
     * @return 文章ID列表
     */
    @Select("""
            SELECT article_id FROM wen_article_share WHERE user_id = #{userId}
            """)
    List<Long> listArticleIdByUserId(@Param("userId") Long userId);

    /**
     * 根据文章ID查询该文章分享的用户列表
     *
     * @param articleId 文章ID
     * @return 用户列表
     */
    @Select("""
            SELECT u.* FROM wen_user u
            INNER JOIN wen_article_share s ON u.id = s.user_id
            WHERE s.article_id = #{articleId}
            """)
    @Result(property = "ctTm", column = "ct_tm", typeHandler = TimestampToLongTypeHandler.class)
    @Result(property = "mdTm", column = "md_tm", typeHandler = TimestampToLongTypeHandler.class)
    List<WenUser> listUsersByArticleId(@Param("articleId") Long articleId);

    /**
     * 判断文章是否分享给指定用户
     *
     * @param articleId 文章ID
     * @param userId    用户ID
     * @return 是否分享给该用户
     */
    @Select("""
            SELECT COUNT(1) > 0 FROM wen_article_share
            WHERE article_id = #{articleId} AND user_id = #{userId}
            """)
    boolean isSharedToUser(@Param("articleId") Long articleId, @Param("userId") Long userId);
}