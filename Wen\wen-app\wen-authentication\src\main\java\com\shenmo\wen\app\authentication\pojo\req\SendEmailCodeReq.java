package com.shenmo.wen.app.authentication.pojo.req;

import com.shenmo.wen.app.authentication.enums.EmailCodeType;
import com.shenmo.wen.app.authentication.validation.ValidEmailCodeType;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 发送邮箱验证码请求
 *
 * <AUTHOR>
 */
@Data
public class SendEmailCodeReq {

    /**
     * 邮箱地址
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式错误")
    private String email;

    /**
     * 验证码类型
     * login - 登录验证码
     * register - 注册验证码
     * forgot - 忘记密码验证码
     */
    @NotBlank(message = "验证码类型不能为空")
    @ValidEmailCodeType
    private String type;

    /**
     * 验证类型是否有效
     *
     * @return 是否有效
     */
    public boolean isValidType() {
        return EmailCodeType.isValid(this.type);
    }

    /**
     * 获取验证码类型枚举
     *
     * @return 验证码类型枚举
     */
    public EmailCodeType getEmailCodeType() {
        return EmailCodeType.fromCode(this.type);
    }
}
