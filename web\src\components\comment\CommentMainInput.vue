<template>
  <NAffix :bottom="0" style="z-index: 1500" class="comment-input-affix">
    <div class="comment-input-row" ref="commentInputWrapperRef">
      <TiptapEditor
        ref="sendTiptapEditorRef"
        :model-value="commentReply"
        @update:model-value="(val) => emit('update:commentReply', val as <PERSON><PERSON><PERSON>ontent)"
        class="comment-tiptap-editor"
        :editor-props="{
          attributes: {
            class: 'ProseMirrorInput',
            'data-main-editor': 'true',
          },
        }"
        :file-bucket="COMMENT"
        :placeholder="'说是你的自由，但是...'"
        :show-character-count="true"
        :extensions="editorExtensions"
        :toolbar="true"
        @keydown.alt.enter.prevent="$emit('sendComment')"
      />
      <NButton
        text
        type="info"
        :loading="sendCommentLoading"
        @click="$emit('sendComment')"
        class="comment-reply-send-btn"
        size="small"
        :disabled="disabled"
      >
        <SendAltFilled :size="28" />
      </NButton>
    </div>
  </NAffix>
</template>

<script lang="ts" setup>
import { NAffix, NButton } from 'naive-ui'
import { ref } from 'vue'

import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'
import { COMMENT } from '@/constants/comment/bucket.constants'
import { COMMENT_EXTENSIONS } from '@/constants/tiptap/tiptap.constants'
import { SendAltFilled } from '@/icons'

import type { JSONContent } from '@tiptap/vue-3'

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps<{
  commentReply: JSONContent | undefined
  sendCommentLoading: boolean
  disabled?: boolean
}>()

const emit = defineEmits<{
  (e: 'sendComment'): void
  (e: 'update:commentReply', value: JSONContent | undefined): void
}>()

const commentInputWrapperRef = ref()
const sendTiptapEditorRef = ref()

const editorExtensions = [...COMMENT_EXTENSIONS, 'characterCount']

defineExpose({
  commentInputWrapperRef,
  sendTiptapEditorRef,
})
</script>

<style lang="scss" scoped>
@use '@/styles/comment/comment-list-item';
</style>
