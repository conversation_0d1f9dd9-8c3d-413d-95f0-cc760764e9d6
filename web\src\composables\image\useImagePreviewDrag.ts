import type { ImageClickCallback } from '@/types/image/image-click-callback.types'
import type { ImagePreviewDragOptions } from '@/types/image/image-preview-drag-options.types'

import { useImagePreviewDragMouse } from './useImagePreviewDragMouse'
import { useImagePreviewDragState } from './useImagePreviewDragState'
import { useImagePreviewDragTouch } from './useImagePreviewDragTouch'
import { useImagePreviewDragTransform } from './useImagePreviewDragTransform'

/**
 * 图片预览拖拽功能组合式函数
 * 提供图片预览模态框中的拖拽和缩放功能
 */

/**
 * 图片预览拖拽组合式函数返回值类型
 */
export interface UseImagePreviewDragReturn {
  /** 拖拽状态 */
  state: any
  /** 滚轮缩放处理 */
  handleWheelZoom: (e: WheelEvent) => void
  /** 初始化拖拽功能 */
  initialize: () => void
  /** 清理拖拽功能 */
  cleanup: () => void
  /** 重置变换状态 */
  resetTransform: () => void
  /** 更新图片变换 */
  updateTransform: () => void
}

/**
 * 图片预览拖拽组合式函数
 * 提供图片预览模态框中的拖拽和缩放功能
 * @param imageElement 图片元素
 * @param options 拖拽选项配置
 * @param onImageClick 图片点击回调
 * @param modalElement 模态框元素
 */
export function useImagePreviewDrag(
  imageElement: HTMLImageElement,
  options: ImagePreviewDragOptions = {},
  onImageClick?: ImageClickCallback,
  modalElement?: HTMLElement,
): UseImagePreviewDragReturn {
  // 初始化状态管理
  const stateReturn = useImagePreviewDragState(options)

  // 初始化变换管理
  const transformReturn = useImagePreviewDragTransform(imageElement, stateReturn)

  // 初始化鼠标事件管理
  const mouseReturn = useImagePreviewDragMouse(
    imageElement,
    stateReturn,
    transformReturn,
    onImageClick,
  )

  // 初始化触摸事件管理
  const touchReturn = useImagePreviewDragTouch(
    imageElement,
    stateReturn,
    transformReturn,
    onImageClick,
  )

  /**
   * 初始化拖拽功能
   */
  const initialize = (): void => {
    // 添加 CSS 性能优化
    imageElement.style.willChange = 'transform'
    imageElement.style.backfaceVisibility = 'hidden'
    imageElement.style.perspective = '1000px'

    // 添加事件监听器
    mouseReturn.addMouseEventListeners()
    touchReturn.addTouchEventListeners(modalElement)
  }

  /**
   * 清理拖拽功能
   */
  const cleanup = (): void => {
    transformReturn.resetTransform()
    mouseReturn.removeMouseEventListeners()
    touchReturn.removeTouchEventListeners(modalElement)
  }

  return {
    state: stateReturn.state,
    handleWheelZoom: transformReturn.handleWheelZoom,
    initialize,
    cleanup,
    resetTransform: transformReturn.resetTransform,
    updateTransform: transformReturn.updateTransform,
  }
}
