<template>
  <NModal
    v-model:show="modalState.isArticleDialogVisible.value"
    preset="dialog"
    negative-text="算了"
    positive-text="确认"
    @negative-click="() => modalState.handleClose(articleForm.resetArticleForm)"
    @positive-click="handleSubmit"
    :showIcon="false"
    @close="() => modalState.handleClose(articleForm.resetArticleForm)"
    @mask-click="() => modalState.handleClose(articleForm.resetArticleForm)"
    :mask-closable="false"
    :auto-focus="false"
    :close-on-esc="false"
    @esc="() => modalState.handleClose(articleForm.resetArticleForm)"
    class="article-modal"
    :positive-button-props="{ loading: modalState.submitLoading.value }"
  >
    <template #header>
      <NGradientText type="primary" :size="20">
        {{ modalState.isEditingArticle.value ? '是得再改改' : '想点什么呢' }}
      </NGradientText>
      <FileUpload :size="24" class="cursor-pointer" @click="articleFile.handleArticleFileClick" />
      <input
        type="file"
        ref="articleFile.articleFileInputRef"
        accept=".md"
        @change="handleFileChange"
        class="display-none"
      />
    </template>
    <NForm
      :model="articleForm.articleForm.value"
      :rules="formRules"
      ref="articleFormRef"
      label-placement="left"
    >
      <!-- 标题 -->
      <NFormItem label="标题" path="title" style="width: min(30rem, 100%)">
        <NInput v-model:value="articleForm.articleForm.value.title" placeholder="请输入文章标题" />
      </NFormItem>
      <!-- 标签 -->
      <NFormItem label="标签" path="tag" style="width: min(30rem, 100%)">
        <NDynamicTags
          v-model:value="articleForm.articleForm.value.tags"
          :input-props="{ maxlength: 20 }"
          :max="3"
          type="primary"
          placeholder="请输入标签"
        />
      </NFormItem>
      <div class="flex-between-center" style="width: min(16rem, 100%)">
        <!-- 等级 -->
        <NFormItem label="等级 | 范围" path="allowCommentLevel" style="width: 6rem">
          <NPopselect
            v-model:value="articleForm.articleForm.value.operationLevel"
            :options="articleForm.generateCommentLevel.value"
            size="small"
            trigger="click"
          >
            <NButton size="small">
              Lv{{ articleForm.articleForm.value.operationLevel || '0' }}
            </NButton>
          </NPopselect>
        </NFormItem>
        <!-- 范围 -->
        <NFormItem path="scope">
          <NRadioGroup
            v-model:value="articleForm.articleForm.value.publishedScope"
            size="small"
            :default-value="ArticlePublishedScope.PERSONAL"
          >
            <NRadioButton
              class="flex-between-center"
              v-for="scope in [
                {
                  value: ArticlePublishedScope.PUBLIC,
                  label: ARTICLE_PUBLISHED_SCOPE_LABEL[ArticlePublishedScope.PUBLIC],
                },
                {
                  value: ArticlePublishedScope.PERSONAL,
                  label: ARTICLE_PUBLISHED_SCOPE_LABEL[ArticlePublishedScope.PERSONAL],
                },
              ]"
              :key="scope.value"
              :value="scope.value"
              :label="scope.label"
            />
          </NRadioGroup>
        </NFormItem>
      </div>
      <!-- 分享用户，仅个人范围时显示 -->
      <NFormItem
        v-if="articleForm.articleForm.value.publishedScope === ArticlePublishedScope.PERSONAL"
        label="分享给"
        path="shareUsers"
        style="width: min(30rem, 100%)"
      >
        <SearchUserSelect
          v-model="articleForm.articleForm.value.shareUsers"
          placeholder="请搜索并选择用户"
          ref="shareUserSelectRef"
        />
      </NFormItem>
      <!-- 内容 -->
      <NFormItem path="content">
        <div class="article-editor-wrapper">
          <div class="article-editor-container">
            <NScrollbar class="article-modal-content">
              <TiptapEditor
                ref="articleTiptapEditorRef"
                v-model="articleForm.articleForm.value.contentObj as JSONContent"
                :editor-props="{
                  attributes: {
                    class: 'ProseMirrorNoneOutline',
                  },
                }"
                :bubble-menu="true"
                :floating-menu="true"
                :file-bucket="ARTICLE"
                :all-extensions="true"
                :toolbar="true"
                :toolbar-class="['editor-toolbar', 'editor-toolbar-bgc']"
                placeholder="尽情发挥！"
                :show-character-count="false"
                :character-limit="ARTICLE_CHARACTER_LIMIT"
                :save-loading="modalState.quickSaveLoading.value"
                @save="handleQuickSave"
              />
            </NScrollbar>
          </div>
          <!-- 字符计数器 - 独立在编辑器下方 -->
          <div v-if="articleTiptapEditorRef?.editor" class="character-count-external">
            {{ articleTiptapEditorRef.editor.storage.characterCount.characters }} /
            {{ ARTICLE_CHARACTER_LIMIT }}
          </div>
        </div>
      </NFormItem>
    </NForm>
  </NModal>
</template>

<script setup lang="ts">
import {
  NButton,
  NModal,
  NForm,
  NFormItem,
  NPopselect,
  NInput,
  NDynamicTags,
  NGradientText,
  NScrollbar,
  NRadioGroup,
  NRadioButton,
} from 'naive-ui'

import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'
import SearchUserSelect from '@/components/user/SearchUserSelect.vue'
import { useArticleFile } from '@/composables/article/useArticleFile'
import { useArticleForm } from '@/composables/article/useArticleForm'
import { useArticleModalState } from '@/composables/article/useArticleModalState'
import {
  ArticlePublishedScope,
  ARTICLE_PUBLISHED_SCOPE_LABEL,
} from '@/constants/article/article-published-scope.constants'
import { ARTICLE } from '@/constants/article/bucket.constants'
import { ARTICLE_CHARACTER_LIMIT } from '@/constants/tiptap/tiptap.constants'
import { FileUpload } from '@/icons'
import type { Article } from '@/types/article/article.types'
import type { ArticleModalEmits } from '@/types/component/article-modal.types'
import type { TiptapEditorRef } from '@/types/component/simple-tiptap-editor-ref.types'

import type { JSONContent } from '@tiptap/vue-3'
import type { FormRules } from 'naive-ui'

// 定义组件 Emits，使用明确的类型定义
const emit = defineEmits<ArticleModalEmits>()

// 使用 composables
const modalState = useArticleModalState()
const articleForm = useArticleForm()
const articleFile = useArticleFile()

// 解构ref，确保ref绑定正确
const { articleFormRef, articleTiptapEditorRef, shareUserSelectRef } = articleForm

// 表单验证规则，使用明确的类型定义
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入文章标题', trigger: 'blur' },
    { min: 1, max: 100, message: '标题长度在 1 到 100 个字符之间', trigger: 'blur' },
  ],
}

// 处理文件变化的包装函数
const handleFileChange = (e: Event) => {
  // 将完整版的TiptapEditorRef转换为简化版的TiptapEditorRef
  const editorRef = {
    value: {
      editor: {
        isEmpty: articleForm.articleTiptapEditorRef.value?.editor.isEmpty || false,
        getJSON: () => articleForm.articleTiptapEditorRef.value?.editor.getJSON() || {},
        commands: {
          clearContent: (_emitUpdate?: boolean) =>
            articleForm.articleTiptapEditorRef.value?.clearContent(),
          setContent: (content: unknown, _emitUpdate?: boolean) =>
            articleForm.articleTiptapEditorRef.value?.setContent(content as JSONContent),
        },
        storage: articleForm.articleTiptapEditorRef.value?.editor.storage || {},
      },
    } as TiptapEditorRef,
  }
  articleFile.handleArticleFileChange(e, articleForm.articleForm, editorRef)
}

// 设置键盘监听
modalState.setupKeyboardListener(() => handleQuickSave())

// 处理提交
const handleSubmit = () => {
  articleForm.submitArticleForm(
    modalState.isEditingArticle,
    modalState.submitLoading,
    modalState.isArticleDialogVisible,
    emit,
  )
  // 返回 false 阻止模态框自动关闭，由保存成功后的逻辑控制关闭
  return false
}

// 处理快速保存
const handleQuickSave = () => {
  articleForm.quickSaveArticleForm(
    modalState.isEditingArticle,
    modalState.quickSaveLoading,
    modalState.isArticleDialogVisible,
    emit,
  )
}

// 暴露方法给父组件
defineExpose({
  openCreateArticleDialog: () => modalState.openCreateArticleDialog(articleForm.resetArticleForm),
  openEditArticleDialog: (articleData: Article) =>
    modalState.openEditArticleDialog(articleData, articleForm.setFormData),
})
</script>

<style lang="scss">
@use '@/styles/article/article-modal';
</style>
