package com.shenmo.wen.app.core.user.service.impl;

import com.shenmo.wen.app.core.user.mapper.WenUserPrivilegeTemplateMapper;
import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilegeTemplate;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeTemplateSaveReq;
import com.shenmo.wen.app.core.user.service.WenUserPrivilegeTemplateService;

import lombok.RequiredArgsConstructor;

import java.util.List;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class WenUserPrivilegeTemplateServiceImpl implements WenUserPrivilegeTemplateService {

    private final WenUserPrivilegeTemplateMapper mapper;

    @Override
    public List<WenUserPrivilegeTemplate> search(String name) {
        if (name == null || name.trim().isEmpty()) {
            return List.of();
        }
        return mapper.searchByNameLike(name.trim());
    }

    @Override
    public Boolean save(WenUserPrivilegeTemplateSaveReq req) {
        final WenUserPrivilegeTemplate template = new WenUserPrivilegeTemplate();
        template.setName(req.getName());
        template.setIcon(req.getIcon());
        template.setDenomination(req.getDenomination());
        template.setLink(req.getLink());
        template.setDescription(req.getDescription());
        template.setQrCodeUrl(req.getQrCodeUrl());
        return mapper.insert(template) > 0;
    }
}
