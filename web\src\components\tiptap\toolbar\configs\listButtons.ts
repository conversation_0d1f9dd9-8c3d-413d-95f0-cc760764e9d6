import { TextBulletListLtr16Filled, TextNumberListLtr16Filled, TaskListLtr24Filled } from '@/icons'

import type { ToolbarButtonConfig } from './types'

/**
 * 列表按钮配置
 */
export const listButtons: ToolbarButtonConfig[] = [
  {
    icon: TextBulletListLtr16Filled,
    extensionName: 'bulletList',
    trigger: (editor) => editor?.chain().focus().toggleBulletList().run(),
    isActive: (editor) => editor?.isActive('bulletList'),
    tooltip: '无序列表',
  },
  {
    icon: TextNumberListLtr16Filled,
    extensionName: 'orderedList',
    trigger: (editor) => editor?.chain().focus().toggleOrderedList().run(),
    isActive: (editor) => editor?.isActive('orderedList'),
    tooltip: '有序列表',
  },
  {
    icon: TaskListLtr24Filled,
    extensionName: 'taskList',
    trigger: (editor) => editor?.chain().focus().toggleTaskList().run(),
    isActive: (editor) => editor?.isActive('taskList'),
    tooltip: '任务列表',
  },
]
