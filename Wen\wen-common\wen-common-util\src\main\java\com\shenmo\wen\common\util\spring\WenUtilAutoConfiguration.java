package com.shenmo.wen.common.util.spring;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 */
@AutoConfiguration
public class WenUtilAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public static SpringAwareUtils springAwareUtils() {
        return new SpringAwareUtils();
    }
}
