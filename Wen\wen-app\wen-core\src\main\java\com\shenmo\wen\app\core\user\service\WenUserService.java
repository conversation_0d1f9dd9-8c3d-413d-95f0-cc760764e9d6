package com.shenmo.wen.app.core.user.service;

import org.springframework.web.multipart.MultipartFile;

import com.shenmo.wen.modules.user.pojo.domain.WenSearchUser;
import com.shenmo.wen.modules.user.pojo.resp.WenUserResp;

import java.io.IOException;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface WenUserService {


    Long online();

    String changeAvatar(MultipartFile avatarFile) throws IOException;

    WenUserResp info();

    /**
     * 更新通知接收类型
     * @param notificationReceiveType 通知接收类型
     * @return 是否更新成功
     */
    boolean updateNotificationReceiveType(Integer notificationReceiveType);

    /**
     * 获取分享用户
     *
     * @param username 用户名
     * @return 分享用户列表
     */
    List<WenSearchUser> search(String username);
}
