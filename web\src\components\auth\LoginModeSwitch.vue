<!--
  登录模式切换组件

  功能说明：
  - 提供手机号和邮箱登录模式切换
  - 使用分段式标签页样式
-->
<template>
  <div class="login-mode-switch">
    <NTabs v-model:value="loginMode" type="segment" size="small" class="login-tabs">
      <NTabPane name="phone" tab="手机号登录" />
      <NTabPane name="email" tab="邮箱登录" />
    </NTabs>
  </div>
</template>

<script lang="ts" setup>
import { NTabs, NTabPane } from 'naive-ui'
import { computed } from 'vue'

import type { LoginMode } from '@/types/auth/login-mode.types'

// 定义组件 Props
interface LoginModeSwitchProps {
  loginMode: LoginMode
}

const props = defineProps<LoginModeSwitchProps>()

// 定义组件 Emits
interface LoginModeSwitchEmits {
  'update:login-mode': [value: LoginMode]
}

const emit = defineEmits<LoginModeSwitchEmits>()

// 双向绑定
const loginMode = computed({
  get: () => props.loginMode,
  set: (value) => emit('update:login-mode', value),
})
</script>

<style lang="scss" scoped>
@use '@/styles/auth/login-mode-switch';
</style>
