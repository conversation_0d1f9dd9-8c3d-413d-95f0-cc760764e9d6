package com.shenmo.wen.common.lock;

import com.shenmo.wen.common.lock.redisson.RedissonLockAdvice;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

/**
 *
 * <AUTHOR>
 */
@AutoConfiguration
public class WenLockAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnBean(RedissonClient.class)
    public RedissonLockAdvice redissonLockAdvice(RedissonClient redissonClient, ObjectProvider<RedissonLockAdvice.LockOption> lockOptionProvider) {
        return new RedissonLockAdvice(redissonClient, lockOptionProvider);
    }
}
