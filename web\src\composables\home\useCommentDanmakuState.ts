import { ref, reactive, watch, type Ref } from 'vue'

import type { Comment } from '@/types/comment/comment.types'

/**
 * 评论弹幕状态管理组合式函数返回值类型
 */
export interface UseCommentDanmakuStateReturn {
  /** 评论列表数据 */
  commentList: Ref<Comment[]>
  /** 弹幕引用 */
  danmakuRef: Ref<any>
  /** 弹幕配置 */
  danmakuConfig: any
  /** 订阅状态跟踪 */
  isSubscribed: Ref<boolean>
}

/**
 * 评论弹幕状态管理组合式函数
 * 提供弹幕组件的状态管理
 */
export function useCommentDanmakuState(
  loop: Ref<boolean>,
  pause: Ref<boolean>,
): UseCommentDanmakuStateReturn {
  // 评论列表数据
  const commentList = ref<Comment[]>([])
  const danmakuRef = ref()

  // 弹幕配置
  const danmakuConfig = reactive({
    isSuspend: true,
    useSlot: true,
    speeds: 160,
    debounce: 200,
    top: 10,
    right: 0,
    channels: 0,
    randomChannel: true,
    fontSize: 14,
    loop: false,
    pause: false,
    autoplay: false,
  })

  // 订阅状态跟踪
  const isSubscribed = ref(false)

  // 监听props变化更新配置
  watch(
    () => loop.value,
    (newVal) => {
      danmakuConfig.loop = newVal
    },
  )

  watch(
    () => pause.value,
    (newVal) => {
      danmakuConfig.pause = newVal
      if (danmakuRef.value) {
        if (newVal) {
          danmakuRef.value.pause()
        } else {
          danmakuRef.value.play()
        }
      }
    },
  )

  return {
    commentList,
    danmakuRef,
    danmakuConfig,
    isSubscribed,
  }
}
