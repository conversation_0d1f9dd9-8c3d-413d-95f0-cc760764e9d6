package com.shenmo.wen.common.objectstorage.response;

import org.springframework.lang.NonNull;

/**
 * 抽象的桶响应
 *
 * <AUTHOR>
 */
public abstract class BucketResponse<O> extends GenericResponse<O> {

    /**
     * 构造方法
     *
     * @param origin 源对象
     * <AUTHOR>
     */
    public BucketResponse(@NonNull O origin) {
        super(origin);
    }

    /**
     * 获取桶名称
     *
     * @return 桶名称
     * <AUTHOR>
     */
    @NonNull
    public abstract String getName();
}
