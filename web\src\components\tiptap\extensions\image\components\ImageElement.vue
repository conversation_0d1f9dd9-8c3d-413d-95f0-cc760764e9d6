<template>
  <img
    ref="imageElement"
    :src="imageSrc"
    :alt="alt || ''"
    :style="imageStyles"
    :data-relative-src="relativeSrc"
    :data-original-src="relativeSrc"
    contenteditable="false"
    loading="eager"
    class="cursor-pointer"
    @dblclick="$emit('doubleClick', $event)"
    @click="$emit('click', $event)"
    @load="$emit('load', $event)"
    @error="$emit('error', $event)"
  />
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

import fileApi from '@/api/file'

interface Props {
  src: string
  alt?: string
  width?: string | number
  height?: string | number
  isResizing: boolean
  resizeWidth: string
  resizeHeight: string
  useThumbnail?: boolean
}

const props = defineProps<Props>()

defineEmits<{
  doubleClick: [event: MouseEvent]
  click: [event: MouseEvent]
  load: [event: Event]
  error: [event: Event]
}>()

const imageElement = ref<HTMLImageElement>()

// 提取相对路径
const extractRelativePath = (url: string): string => {
  if (!url) return ''

  // 如果已经是相对路径，直接返回
  if (!url.startsWith('http')) {
    return url
  }

  try {
    const urlObj = new URL(url)
    return urlObj.pathname
  } catch {
    return url
  }
}

// 获取完整图片URL
const getFullImageUrl = (relativePath: string): string => {
  if (!relativePath) return ''

  // 如果已经是完整URL，直接返回
  if (relativePath.startsWith('http')) {
    return relativePath
  }

  // 使用fileApi获取完整URL
  return fileApi.getResourceURL(relativePath)
}

const relativeSrc = computed(() => extractRelativePath(props.src))
const imageSrc = computed(() => getFullImageUrl(relativeSrc.value))

const imageStyles = computed(() => ({
  display: 'block' as const,
  maxWidth: '100%',
  margin: '0',
  padding: '0',
  verticalAlign: 'baseline' as const,
  transform: 'translateY(0)',
  transition: 'none',
  willChange: 'transform' as const,
  lineHeight: 'normal' as const,
  fontSize: 'inherit' as const,
  width: props.isResizing ? props.resizeWidth : props.width || '',
  height: props.isResizing ? props.resizeHeight : props.height || '',
  // 保持图片清晰度
  imageRendering: 'auto' as const,
  objectFit: 'contain' as const,
}))

defineExpose({
  imageElement,
})
</script>

<style lang="scss" scoped>
img {
  &.cursor-pointer {
    cursor: pointer;
  }
}
</style>
