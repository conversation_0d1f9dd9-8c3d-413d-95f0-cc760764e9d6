/*
 * Image 相关组件样式
 * 图片节点视图和调整控制点组件的样式定义
 */

/*
 * ColorPicker 组件样式
 * 颜色选择器组件的样式定义，隐藏naive-ui的触发器
 */

:deep(.n-color-picker-trigger) {
  border: none !important;
  width: 0 !important;
  height: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: hidden !important;
}

/*
 * ImageNodeView 组件样式
 * 图片节点视图组件的样式定义，包括图片容器和调整控制点
 */

// Vue 版本的图片节点视图样式
// 这些样式专门用于 ImageNodeView.vue 组件

.image-wrapper {
  position: relative;
  display: inline-block;
  max-width: 100%;
  box-sizing: border-box;

  // 为图片添加默认阴影效果，增强可见性
  img {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    transition: box-shadow 0.2s ease;
    display: block;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  &.resizing {
    user-select: none;

    img {
      pointer-events: none;
    }

    // 缩放时禁用控制点过渡效果，提供更好的跟随体验
    .resize-handle {
      transition: none;
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
    }
  }

  // 调整大小控制点样式 - 统一使用桌面设备规格
  .resize-handle {
    position: absolute;
    background: #2d8cf0;
    border: 1px solid white;
    border-radius: 50%;
    width: 8px;
    height: 8px;
    cursor: pointer;
    z-index: 100;
    opacity: 0;
    transition: opacity 0.2s ease;
    user-select: none;

    // 角控制点 - 统一使用桌面设备位置
    &.handle-top-left {
      top: -4px;
      left: -4px;
      cursor: nw-resize;
    }

    &.handle-top-right {
      top: -4px;
      right: -4px;
      cursor: ne-resize;
    }

    &.handle-bottom-left {
      bottom: -4px;
      left: -4px;
      cursor: sw-resize;
    }

    &.handle-bottom-right {
      bottom: -4px;
      right: -4px;
      cursor: se-resize;
    }

    // 边控制点 - 统一使用桌面设备位置
    &.handle-top {
      top: -4px;
      left: calc(50% - 6px);
      cursor: n-resize;
      width: 12px;
      height: 4px;
      border-radius: 2px;
    }

    &.handle-right {
      right: -4px;
      top: calc(50% - 6px);
      cursor: e-resize;
      width: 4px;
      height: 12px;
      border-radius: 2px;
    }

    &.handle-bottom {
      bottom: -4px;
      left: calc(50% - 6px);
      cursor: s-resize;
      width: 12px;
      height: 4px;
      border-radius: 2px;
    }

    &.handle-left {
      left: -4px;
      top: calc(50% - 6px);
      cursor: w-resize;
      width: 4px;
      height: 12px;
      border-radius: 2px;
    }

    // 移动设备优化已移除，统一使用桌面设备样式
  }

  // 尺寸信息显示
  .resize-info {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 75%);
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 400;
    white-space: nowrap;
    z-index: 101;
    pointer-events: none;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 20%);
    backdrop-filter: blur(2px);
    line-height: 1.2;
  }

  // 添加 Vue 组件特有的样式
  // 确保控制点在选中时可见，同时处理图片的选中状态样式
  &.ProseMirror-selectednode {
    .resize-handle {
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
    }

    img {
      box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        0 0 0 2px rgba(45, 140, 240, 50%),
        0 0 8px rgba(45, 140, 240, 30%);

      &:hover {
        box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.15),
          0 0 0 2px rgba(45, 140, 240, 50%),
          0 0 8px rgba(45, 140, 240, 30%);
      }
    }
  }

  // 非编辑模式下隐藏控制点
  &.readonly-image .resize-handle {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }
}

/* 移动设备相关样式已移除，统一使用桌面样式 */
