import { setupModalEvents } from '@/composables/image/useImageModalEvents'
import { useImagePreviewDrag } from '@/composables/image/useImagePreviewDrag'
import type { ImagePreviewOptions } from '@/types/image/image-preview.types'

import { getFullImageUrl } from '../ImageResourceManager'

/**
 * 原图缓存 - 避免重复加载
 */
const originalImageCache = new Map<string, string>()

/**
 * 缓存大小限制
 */
const MAX_CACHE_SIZE = 50

/**
 * 添加到缓存，如果超过限制则清理最老的条目
 */
const addToCache = (key: string, value: string) => {
  if (originalImageCache.size >= MAX_CACHE_SIZE) {
    // 删除最老的条目（Map 的第一个条目）
    const firstKey = originalImageCache.keys().next().value
    if (firstKey) {
      originalImageCache.delete(firstKey)
    }
  }
  originalImageCache.set(key, value)
}

/**
 * 清理原图缓存
 * @param src 要清理的图片路径，如果不提供则清理所有缓存
 */
export const clearOriginalImageCache = (src?: string) => {
  if (src) {
    originalImageCache.delete(src)
  } else {
    originalImageCache.clear()
  }
}

/**
 * 图片预览组合式函数
 * 处理图片预览模态框的显示、缩放、关闭等功能
 */
export function useImagePreview() {
  /**
   * 显示图片预览模态框
   */
  const showImagePreview = (options: ImagePreviewOptions) => {
    const { src, originalSrc, alt, onOriginalLoaded } = options

    // 创建模态框
    const modal = document.createElement('div')
    modal.classList.add('modal-overlay')

    // 创建预览图
    const previewImg = document.createElement('img')
    previewImg.alt = alt
    modal.appendChild(previewImg)
    document.body.appendChild(modal)

    // 触发模态框显示动画
    setTimeout(() => {
      modal.classList.add('modal-overlay-active')
    }, 10)

    // 检查图片是否已经是原图或已经缓存了原图
    const isAlreadyOriginal =
      !originalSrc.includes('/thumbnail') || src.indexOf('/thumbnail') === -1

    // 检查是否已经缓存了原图
    const cachedOriginalUrl = originalImageCache.get(originalSrc)
    const hasCachedOriginal = cachedOriginalUrl !== undefined

    if (originalSrc.includes('/thumbnail') && !isAlreadyOriginal && !hasCachedOriginal) {
      // 处理缩略图预览 - 首次加载原图
      previewImg.src = src
      previewImg.style.opacity = '0.5'

      // 添加加载动画
      const loadingSpinner = document.createElement('div')
      loadingSpinner.classList.add('loading-spinner')
      modal.appendChild(loadingSpinner)

      // 加载原图
      const originalImg = new Image()
      const animationStartTime = Date.now()
      const minDisplayTime = 500

      originalImg.onload = () => {
        const loadTime = Date.now() - animationStartTime

        const setOriginal = () => {
          loadingSpinner.style.display = 'none'
          previewImg.src = originalImg.src
          previewImg.style.opacity = '1'

          // 将原图URL保存到图片元素，以便关闭模态框后更新
          previewImg.dataset.originalFullUrl = originalImg.src

          // 缓存原图URL，避免下次重复加载
          addToCache(originalSrc, originalImg.src)
        }

        if (loadTime < minDisplayTime) {
          setTimeout(setOriginal, minDisplayTime - loadTime)
        } else {
          setOriginal()
        }
      }

      // 获取原图URL
      const originalFullUrl = getFullImageUrl(originalSrc.replace('/thumbnail', ''), false)
      originalImg.src = originalFullUrl
    } else if (hasCachedOriginal) {
      // 使用缓存的原图，无需重新加载
      previewImg.src = cachedOriginalUrl
      previewImg.style.opacity = '1'

      // 设置原图URL以便回调
      previewImg.dataset.originalFullUrl = cachedOriginalUrl
    } else {
      // 已经是原图，直接显示
      previewImg.src = src
      previewImg.style.opacity = '1'
    }

    // 关闭模态框函数
    const closeModal = () => {
      modal.classList.remove('modal-overlay-active')
      modal.addEventListener(
        'transitionend',
        () => {
          if (!modal.classList.contains('modal-overlay-active')) {
            // 如果已经加载了原图，则调用回调函数
            if (previewImg.dataset.originalFullUrl && originalSrc.includes('/thumbnail')) {
              onOriginalLoaded?.(previewImg.dataset.originalFullUrl)
            }

            // 清理事件监听器
            document.body.removeChild(modal)
            cleanupModalEvents()
            dragHandler.cleanup()
          }
        },
        { once: true },
      )
    }

    // 添加拖拽和缩放功能
    const dragHandler = useImagePreviewDrag(
      previewImg,
      {
        minScale: 0.5,
        maxScale: 3,
        scaleStep: 0.1,
      },
      closeModal,
      modal,
    ) // 传入关闭回调和模态框元素

    // 初始化拖拽功能
    dragHandler.initialize()

    // 设置模态框通用事件处理
    const cleanupModalEvents = setupModalEvents(modal, closeModal, dragHandler.handleWheelZoom)
  }

  return {
    showImagePreview,
    clearOriginalImageCache,
  }
}
