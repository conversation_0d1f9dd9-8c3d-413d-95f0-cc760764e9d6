import { ref } from 'vue'

/**
 * 编辑器模态框状态接口
 */
export interface EditorModalState {
  visible: boolean
  title: string
  inputTitle: string
  inputValue: string
  onlyInputValue: boolean
  trigger: () => void
}

/**
 * 模态框显示参数接口
 */
export interface ShowModalParams {
  title: string
  trigger: () => void
  onlyInputValue?: boolean
}

/**
 * 创建编辑器模态框
 * 处理链接、视频等需要用户输入的弹窗
 */
export const createEditorModal = () => {
  // 模态框状态
  const modal = ref<EditorModalState>({
    visible: false,
    title: '',
    inputTitle: '',
    inputValue: '',
    onlyInputValue: false,
    trigger: () => {},
  })

  /**
   * 显示模态框
   * @param params 模态框参数
   */
  const showModal = (params: ShowModalParams) => {
    const { title, trigger, onlyInputValue = false } = params

    modal.value.inputTitle = ''
    modal.value.inputValue = ''
    modal.value.visible = true
    modal.value.title = title
    modal.value.trigger = trigger
    modal.value.onlyInputValue = onlyInputValue
  }

  /**
   * 关闭模态框
   */
  const closeModal = () => {
    modal.value.visible = false
  }

  /**
   * 处理确认按钮点击
   * @param callback 可选的回调函数
   */
  const handleConfirm = (callback?: (modal: EditorModalState) => void) => {
    if (callback) {
      callback(modal.value)
    } else {
      modal.value.trigger()
    }
    closeModal()
  }

  /**
   * 处理取消按钮点击
   */
  const handleCancel = () => {
    closeModal()
  }

  return {
    modal,
    showModal,
    closeModal,
    handleConfirm,
    handleCancel,
  }
}

// 为了保持向后兼容，提供原有的 useModal 函数
export const useModal = () => {
  const { modal, showModal } = createEditorModal()

  return {
    modal,
    handleShowModal: showModal,
  }
}
