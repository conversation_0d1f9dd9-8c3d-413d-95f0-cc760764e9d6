package com.shenmo.wen.app.core.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 *
 * <AUTHOR>
 */
@Data
@RefreshScope
@ConfigurationProperties(ArticleConfigProperties.PREFIX)
public class ArticleConfigProperties {
    public static final String PREFIX = "article";

    private Integer contentMaxLength = Integer.MAX_VALUE;

    private String checkDetailView = "...";
}
