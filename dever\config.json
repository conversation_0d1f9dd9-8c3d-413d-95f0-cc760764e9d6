{"window_detection": {"cursor_window_titles": ["<PERSON><PERSON><PERSON>", "cursor"], "activation_delay": 1.0}, "panel_capture": {"panel_width_ratio": 0.33, "capture_delay": 0.5}, "template_matching": {"normal_button_template": "templates/send_button_normal.png", "pause_button_template": "templates/send_button_pause.png", "match_threshold": 0.8}, "automation": {"tasks_file": "tasks.txt", "current_window_tasks_file": "tasks.txt", "new_window_tasks_file": "tasks_new_window.txt", "new_conversation_hotkey": ["ctrl", "l"], "paste_hotkey": ["ctrl", "v"], "send_key": "enter", "input_area_offset_from_bottom": 100, "loop_mode": true, "check_interval_seconds": 180, "enable_new_window": true, "new_window_threshold": 5, "send_count": 1, "selected_cursor_window": 0, "operation_delays": {"after_new_conversation": 2.0, "after_paste": 1.0, "between_operations": 0.5}}, "logging": {"level": "INFO", "format": "%(asctime)s - %(levelname)s - %(message)s"}, "safety": {"enable_failsafe": true, "pause_between_actions": 0.5}}