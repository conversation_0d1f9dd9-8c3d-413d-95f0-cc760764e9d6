package com.shenmo.wen.app.core.article.exception;

import com.shenmo.wen.common.exception.BaseException;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import org.springframework.http.HttpStatus;

/**
 *
 * <AUTHOR>
 */
public class ArticleException extends BaseException {
    public ArticleException(ExceptionEnum exceptionEnum) {
        super(exceptionEnum);
    }

    public ArticleException(HttpStatus httpStatus, String message) {
        super(httpStatus, message);
    }

    public ArticleException(HttpStatus httpStatus, String description, Throwable throwable) {
        super(httpStatus, description, throwable);
    }

    public ArticleException(HttpStatus httpStatus, String description, String message) {
        super(httpStatus, description, message);
    }
}
