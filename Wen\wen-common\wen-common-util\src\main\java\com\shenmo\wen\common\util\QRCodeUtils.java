package com.shenmo.wen.common.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码生成工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class QRCodeUtils {

    /**
     * 默认二维码尺寸
     */
    private static final int DEFAULT_SIZE = 200;

    /**
     * 默认边距
     */
    private static final int DEFAULT_MARGIN = 2;

    /**
     * 默认前景色（黑色）
     */
    private static final int DEFAULT_FOREGROUND_COLOR = 0xFF000000;

    /**
     * 默认背景色（白色）
     */
    private static final int DEFAULT_BACKGROUND_COLOR = 0xFFFFFFFF;

    /**
     * 生成二维码的Base64字符串
     *
     * @param content 二维码内容
     * @return Base64编码的PNG图片字符串，格式为 "data:image/png;base64,..."
     */
    public static String generateQRCodeBase64(String content) {
        return generateQRCodeBase64(content, DEFAULT_SIZE, DEFAULT_SIZE);
    }

    /**
     * 生成二维码的Base64字符串
     *
     * @param content 二维码内容
     * @param width   宽度
     * @param height  高度
     * @return Base64编码的PNG图片字符串，格式为 "data:image/png;base64,..."
     */
    public static String generateQRCodeBase64(String content, int width, int height) {
        return generateQRCodeBase64(content, width, height, DEFAULT_FOREGROUND_COLOR, DEFAULT_BACKGROUND_COLOR);
    }

    /**
     * 生成二维码的Base64字符串
     *
     * @param content         二维码内容
     * @param width           宽度
     * @param height          高度
     * @param foregroundColor 前景色（二维码颜色）
     * @param backgroundColor 背景色
     * @return Base64编码的PNG图片字符串，格式为 "data:image/png;base64,..."
     */
    public static String generateQRCodeBase64(String content, int width, int height, 
                                              int foregroundColor, int backgroundColor) {
        try {
            // 创建二维码写入器
            QRCodeWriter qrCodeWriter = new QRCodeWriter();

            // 设置编码参数
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
            hints.put(EncodeHintType.MARGIN, DEFAULT_MARGIN);

            // 生成二维码矩阵
            BitMatrix bitMatrix = qrCodeWriter.encode(content, BarcodeFormat.QR_CODE, width, height, hints);

            // 创建BufferedImage
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);

            // 填充像素
            for (int x = 0; x < width; x++) {
                for (int y = 0; y < height; y++) {
                    image.setRGB(x, y, bitMatrix.get(x, y) ? foregroundColor : backgroundColor);
                }
            }

            // 转换为Base64
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "PNG", outputStream);
            byte[] imageBytes = outputStream.toByteArray();
            String base64String = Base64.getEncoder().encodeToString(imageBytes);

            return "data:image/png;base64," + base64String;

        } catch (WriterException | IOException e) {
            log.error("生成二维码失败: content={}, width={}, height={}", content, width, height, e);
            return null;
        }
    }

    /**
     * 生成带自定义颜色的二维码Base64字符串
     *
     * @param content         二维码内容
     * @param width           宽度
     * @param height          高度
     * @param foregroundColor 前景色（十六进制字符串，如 "#4f46e5"）
     * @param backgroundColor 背景色（十六进制字符串，如 "#ffffff"）
     * @return Base64编码的PNG图片字符串，格式为 "data:image/png;base64,..."
     */
    public static String generateQRCodeBase64(String content, int width, int height, 
                                              String foregroundColor, String backgroundColor) {
        try {
            int fgColor = parseColor(foregroundColor, DEFAULT_FOREGROUND_COLOR);
            int bgColor = parseColor(backgroundColor, DEFAULT_BACKGROUND_COLOR);
            return generateQRCodeBase64(content, width, height, fgColor, bgColor);
        } catch (Exception e) {
            log.error("解析颜色失败，使用默认颜色: foregroundColor={}, backgroundColor={}", 
                     foregroundColor, backgroundColor, e);
            return generateQRCodeBase64(content, width, height);
        }
    }

    /**
     * 解析颜色字符串为整数
     *
     * @param colorStr     颜色字符串（如 "#4f46e5" 或 "4f46e5"）
     * @param defaultColor 默认颜色
     * @return 颜色整数值
     */
    private static int parseColor(String colorStr, int defaultColor) {
        if (colorStr == null || colorStr.trim().isEmpty()) {
            return defaultColor;
        }

        try {
            // 移除 # 前缀
            String hex = colorStr.startsWith("#") ? colorStr.substring(1) : colorStr;
            
            // 确保是6位十六进制
            if (hex.length() != 6) {
                return defaultColor;
            }

            // 解析为RGB并添加Alpha通道
            int rgb = Integer.parseInt(hex, 16);
            return 0xFF000000 | rgb; // 添加完全不透明的Alpha通道
        } catch (NumberFormatException e) {
            log.warn("无效的颜色格式: {}", colorStr);
            return defaultColor;
        }
    }

    /**
     * 生成错误提示二维码
     *
     * @param errorMessage 错误信息
     * @return Base64编码的错误提示图片
     */
    public static String generateErrorQRCode(String errorMessage) {
        try {
            int width = DEFAULT_SIZE;
            int height = DEFAULT_SIZE;
            
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = image.createGraphics();
            
            // 设置抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            
            // 设置背景
            g2d.setColor(new Color(0xFFF3F4F6)); // 浅灰色背景
            g2d.fillRect(0, 0, width, height);
            
            // 设置边框
            g2d.setColor(new Color(0xFFEF4444)); // 红色边框
            g2d.setStroke(new BasicStroke(3));
            g2d.drawRoundRect(10, 10, width - 20, height - 20, 16, 16);
            
            // 绘制错误图标
            g2d.setColor(new Color(0xFFEF4444)); // 红色
            g2d.setFont(new Font("Arial", Font.BOLD, 24));
            FontMetrics fm = g2d.getFontMetrics();
            String icon = "❌";
            int iconX = (width - fm.stringWidth(icon)) / 2;
            g2d.drawString(icon, iconX, 60);
            
            // 绘制错误信息
            g2d.setColor(new Color(0xFF374151)); // 深灰色文字
            g2d.setFont(new Font("Arial", Font.PLAIN, 12));
            fm = g2d.getFontMetrics();
            
            // 分行显示错误信息
            String[] lines = errorMessage.split("\\n");
            int lineHeight = fm.getHeight();
            int startY = 90;
            
            for (int i = 0; i < lines.length && i < 3; i++) { // 最多显示3行
                String line = lines[i];
                if (line.length() > 20) {
                    line = line.substring(0, 17) + "...";
                }
                int textX = (width - fm.stringWidth(line)) / 2;
                g2d.drawString(line, textX, startY + i * lineHeight);
            }
            
            g2d.dispose();
            
            // 转换为Base64
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "PNG", outputStream);
            byte[] imageBytes = outputStream.toByteArray();
            String base64String = Base64.getEncoder().encodeToString(imageBytes);
            
            return "data:image/png;base64," + base64String;
            
        } catch (IOException e) {
            log.error("生成错误提示二维码失败: {}", errorMessage, e);
            // 返回一个简单的错误占位符
            return "data:image/svg+xml;base64," + 
                   Base64.getEncoder().encodeToString(
                       "<svg width='200' height='200' xmlns='http://www.w3.org/2000/svg'><rect width='200' height='200' fill='#f3f4f6'/><text x='100' y='100' text-anchor='middle' fill='#ef4444'>❌ 错误</text></svg>".getBytes()
                   );
        }
    }
}
