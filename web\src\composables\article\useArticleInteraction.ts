import { type Ref } from 'vue'

import favoriteApi from '@/api/favorite'
import interactionApi from '@/api/interaction'
import type { FavoriteResponse } from '@/types/api/favorite-response.types'
import type { InteractionResponse } from '@/types/api/interaction-response.types'
import { type Article } from '@/types/article/article.types'
import message from '@/utils/ui/message'

/**
 * 文章互动组合式函数返回值类型
 */
interface UseArticleInteractionReturn {
  /** 处理互动按钮（点赞/踩） */
  interactionBtn: (articleId: string, actionType: number) => void
  /** 处理收藏按钮 */
  favoriteBtn: (articleId: string) => void
}

/**
 * 文章互动管理组合式函数
 * 提供文章的点赞、踩、收藏等互动功能
 * @param article 文章数据的响应式引用
 */
export function useArticleInteraction(article: Ref<Article>): UseArticleInteractionReturn {
  // 处理互动按钮（点赞/踩）
  const interactionBtn = (articleId: string, actionType: number): void => {
    const reqParam = {
      targetType: 1,
      targetId: articleId,
      actionType: actionType,
    }

    interactionApi.save({ ...reqParam, type: reqParam.targetType }).then((res) => {
      const data = res?.data
      if (data) {
        const interactionData: InteractionResponse = data
        article.value.likeCount = interactionData.likeCount || 0
        article.value.dislikeCount = interactionData.dislikeCount || 0
        const like = actionType === 1

        // 取消互动
        if (interactionData.cancel) {
          if (like) {
            message.info('赞取消')
            article.value.isLike = false
          } else {
            message.info('踩取消')
            article.value.isDislike = false
          }
        } else {
          if (like) {
            message.success('赞 :)')
            article.value.isLike = true
          } else {
            message.warning('踩 :(')
            article.value.isDislike = true
          }
        }
      }
    })
  }

  // 处理收藏按钮
  const favoriteBtn = (articleId: string): void => {
    const reqParam = {
      targetType: 1,
      targetId: articleId,
    }

    favoriteApi.save(reqParam).then((res) => {
      const data = res?.data
      if (data) {
        const favoriteData: FavoriteResponse = data
        article.value.favoriteCount = favoriteData.count || 0
        if (favoriteData.cancel) {
          message.info('取消收藏')
          article.value.isFavorite = false
        } else {
          message.success('已收藏')
          article.value.isFavorite = true
        }
      }
    })
  }

  return {
    interactionBtn,
    favoriteBtn,
  }
}
