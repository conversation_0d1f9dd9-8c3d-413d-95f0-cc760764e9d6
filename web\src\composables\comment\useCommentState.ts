import { watch } from 'vue'
import { useRoute } from 'vue-router'

import { useCommentStore } from '@/stores'

import { useCommentBreadcrumb, type UseCommentBreadcrumbReturn } from './useCommentBreadcrumb'
import { useCommentLoader, type UseCommentLoaderReturn } from './useCommentLoader'
import { useCommentScroll, type UseCommentScrollReturn } from './useCommentScroll'

/**
 * 评论状态组合式函数返回值类型
 */
interface UseCommentStateReturn
  extends UseCommentBreadcrumbReturn,
    UseCommentLoaderReturn,
    UseCommentScrollReturn {
  /** 评论存储 */
  commentStore: ReturnType<typeof useCommentStore>
}

/**
 * 评论状态管理组合式函数
 * 提供评论列表的加载、状态管理、面包屑导航等功能
 * @param getArticleId 获取文章ID的函数
 * @param getDynamicLoadSize 获取动态加载数量的函数（可选）
 * @param onLoadComplete 加载完成回调函数（可选）
 */
export function useCommentState(
  getArticleId: () => string,
  getDynamicLoadSize?: () => number,
  onLoadComplete?: () => void,
): UseCommentStateReturn {
  const commentStore = useCommentStore()
  const route = useRoute()

  // 初始化各个功能模块
  const breadcrumbActions = useCommentBreadcrumb()

  const loaderActions = useCommentLoader(
    getArticleId,
    getDynamicLoadSize,
    onLoadComplete,
    breadcrumbActions,
  )

  const scrollActions = useCommentScroll(
    loaderActions.commentLoading,
    breadcrumbActions,
    loaderActions.resetCommentList,
    loaderActions.addCommentList,
    loaderActions.setFirstFixedComment,
  )

  // 更新加载器的重置滚动触发器函数
  const originalResetCommentList = loaderActions.resetCommentList
  loaderActions.resetCommentList = () => {
    scrollActions.commentRefs.value.clear()
    originalResetCommentList()
    scrollActions.resetScrollTrigger()
  }

  // 监听路由变化
  watch(route, () => {
    if (scrollActions.getCommentId()) {
      scrollActions.locationComment()
    } else {
      loaderActions.loadCurrentCommentList()
    }
  })

  // 监听评论ID变化
  watch(
    () => commentStore.getId,
    (newCommentId) => {
      if (newCommentId) {
        scrollActions.locationComment(newCommentId)
      }
    },
  )

  return {
    // 面包屑相关
    ...breadcrumbActions,
    // 加载器相关
    ...loaderActions,
    // 滚动相关
    ...scrollActions,
    // 评论存储
    commentStore,
  }
}
