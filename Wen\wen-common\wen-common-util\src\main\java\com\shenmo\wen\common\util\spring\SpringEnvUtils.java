package com.shenmo.wen.common.util.spring;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.PlaceholderConfigurerSupport;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.bind.BindResult;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.context.properties.bind.PropertySourcesPlaceholdersResolver;
import org.springframework.boot.context.properties.source.ConfigurationPropertySources;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.*;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.lang.reflect.Field;

/**
 * spring 解析环境中定义的属性配置以及占位符工具
 * <p>
 * {@link PlaceholderConfigurerSupport}
 * {@link EnvironmentAware}
 *
 * <AUTHOR>
 * @version 0.0.1
 */
@Slf4j
public abstract class SpringEnvUtils {

    /**
     * 根据key获取环境中的配置
     *
     * @param key  配置key
     * @param type 获取的值的类型
     * @param <T>  类型泛型
     * @return 配置值
     * <AUTHOR>
     */
    @Nullable
    public static <T> T getConfig(String key, Class<T> type) {

        return SpringAwareUtils.env().getProperty(key, type);
    }

    /**
     * 根据key获取环境中的配置
     *
     * @param key          配置key
     * @param type         获取的值的类型
     * @param defaultValue 如果没有找到值，则返回默认值
     * @param <T>          类型泛型
     * @return 配置值
     * <AUTHOR>
     */
    @NonNull
    public static <T> T getConfig(String key, Class<T> type, T defaultValue) {

        return SpringAwareUtils.env().getProperty(key, type, defaultValue);
    }

    /**
     * 根据key获取环境中的配置
     *
     * @param key 配置key
     * @return 配置值
     * <AUTHOR>
     */
    @Nullable
    public static String getConfig(String key) {

        return SpringAwareUtils.env().getProperty(key);
    }

    /**
     * 根据key获取环境中的配置
     *
     * @param key          配置key
     * @param defaultValue 如果没有找到值，则返回默认值
     * @return 配置值
     * <AUTHOR>
     */
    @NonNull
    public static String getConfig(String key, String defaultValue) {

        return SpringAwareUtils.env().getProperty(key, defaultValue);
    }

    /**
     * 根据属性配置类前缀获取属性配置类
     *
     * @param propertiesClass 属性配置类class对象
     * @param <T>             类型泛型
     * @return 配置值
     * <AUTHOR>
     */
    @NonNull
    public static <T> T bindConfig(Class<T> propertiesClass) {

        String prefix = "";
        try {
            final Field prefixField = propertiesClass.getField("PREFIX");
            prefix = String.valueOf(prefixField.get(null));
        } catch (Exception e) {
            log.error("Can not find field 'PREFIX'");
        }
        return bindConfig(prefix, propertiesClass);
    }

    /**
     * 根据属性配置类前缀获取属性配置类
     *
     * @param prefix          属性配置前缀{@link ConfigurationProperties#prefix()}
     * @param propertiesClass 属性配置类class对象
     * @param <T>             类型泛型
     * @return 配置值
     * <AUTHOR>
     */
    @NonNull
    public static <T> T bindConfig(String prefix, Class<T> propertiesClass) {

        return bindResult(prefix, propertiesClass).get();
    }

    /**
     * 根据属性配置类前缀获取属性绑定对象
     *
     * @param prefix          属性配置前缀{@link ConfigurationProperties#prefix()}
     * @param propertiesClass 属性配置类class对象
     * @param <T>             类型泛型
     * @return 配置值
     * <AUTHOR>
     */
    @NonNull
    public static <T> BindResult<T> bindResult(String prefix, Class<T> propertiesClass) {

        final ConfigurableEnvironment env = SpringAwareUtils.env();
        final PropertySourcesPlaceholdersResolver propertySourcesPlaceholdersResolver = new PropertySourcesPlaceholdersResolver(
                env);
        return new Binder(ConfigurationPropertySources.from(env.getPropertySources()),
                propertySourcesPlaceholdersResolver)
                .bind(prefix, propertiesClass);
    }
}
