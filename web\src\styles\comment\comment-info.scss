/*
 * CommentInfo 组件样式
 * 评论信息容器的样式定义，包括布局和响应式设计
 */

.comment-info-container {
  box-sizing: border-box;
  flex: 0 0 35vw;
  flex: 0 0 35dvw;
  width: 35vw;
  width: 35dvw;
  height: 100vh;
  height: 100dvh;
  max-height: 100vh;
  max-height: 100dvh;
  background-color: var(--comment-info-bg, var(--creamy-white-1));
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;

  /* 当有快速评论框时的特殊处理 */
  &.has-quick-reply {
    position: relative;
    overflow: hidden;

    .comment-list-container {
      overflow-y: auto;
      max-height: calc(100vh - 200px); /* 为快速评论框预留空间 */
      max-height: calc(100dvh - 200px);
    }
  }

  @media (width <= 768px) {
    width: 100vw;
    width: 100dvw;
    height: auto; /* 改回自适应高度 */
    min-height: 70vh; /* 增加最小高度 */
    position: relative;
    overflow: visible; /* 允许内容显示 */

    &.has-quick-reply {
      .comment-list-container {
        overflow-y: auto;
        padding-bottom: 20px; /* 减少底部间距 */
      }
    }
  }
}

/*
 * CommentControls 组件样式
 * 评论控制组件的样式定义，包括回复信息和交互按钮
 */

.comment-controls-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;

  .comment-reply-info {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .comment-interaction-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  // 统一交互按钮样式
  .n-icon {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;

    &:hover {
      transform: scale(1.15) translateY(-1px);
      filter: brightness(1.1);
    }

    &:active {
      transform: scale(1.05) translateY(0);
      transition: all 0.1s ease;
    }
  }

  // 按钮样式
  .comment-reply-list-btn {
    font-size: 0.9rem;
    padding: 0 0.5rem;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: translateX(3px);
    }

    &:active {
      transform: translateX(1px);
    }
  }
}

/*
 * CommentHeader 组件样式
 * 评论头部组件的样式定义，包括标题容器、面包屑导航和响应式设计
 */

.comment-title-container {
  box-sizing: border-box;
  padding: 1.25rem 1.25rem 0.5rem;
  border-bottom: var(--border-1);
  display: flex;
  flex-direction: column;
  height: 176px;

  .comment-header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 7.5rem;
    flex-wrap: wrap;
  }

  .comment-header-bottom {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  :deep(.n-breadcrumb) {
    padding: 0.5rem 0;
    transition: all 0.2s ease;

    .n-breadcrumb-item {
      position: relative;

      &:last-child {
        font-weight: bold;
      }
    }
  }

  .breadcrumb-text {
    display: flex;
    align-items: center;
    font-size: 1rem;
    transition: all 0.25s ease;
    padding: 0.3rem 0;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 0;
      height: 2px;
      background-color: var(--blue);
      transition: width 0.3s ease;
    }

    &:hover {
      &::before {
        width: 100%;
      }
    }

    .n-avatar {
      margin: 0 0.25rem;
      border: 1px solid rgba(200, 200, 200, 30%);
    }
  }
}

// 添加响应式设计，调整窄屏下的布局
@media (width <= 768px) {
  .comment-title-container {
    :deep(.n-breadcrumb) {
      padding: 0.3rem 0;
    }

    .breadcrumb-text {
      font-size: 0.9rem;
      padding: 0.2rem 0;
    }
  }
}
