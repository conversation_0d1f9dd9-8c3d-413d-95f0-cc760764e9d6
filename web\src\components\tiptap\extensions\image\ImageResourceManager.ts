import fileApi from '@/api/file'
import { THUMBNAIL } from '@/constants/image/filepath.constants'
import type { EditorWithStorage } from '@/types/tiptap/editor-with-storage.types'
import logger from '@/utils/log/log'

interface ImageAttributes {
  src: string
  alt?: string
  title?: string
  width?: string
  height?: string
}

// 基础资源路径，在应用启动时设置
let baseResourcePath = '/'

/**
 * 设置基础资源路径
 * @param path 基础资源路径
 */
export const setBaseResourcePath = (path: string) => {
  baseResourcePath = path
}

/**
 * 规范化路径，移除多余的斜杠和特殊字符
 * @param path 原始路径
 * @returns 规范化后的路径
 */
const normalizePath = (path: string): string => {
  // 移除多余的斜杠，保留开头的斜杠
  return path.replace(/\/+/g, '/').replace(/^\/+/, '/')
}

/**
 * 从URL或路径中提取相对路径
 * @param src 图片源路径
 * @returns 相对路径
 */
export const extractRelativePath = (src: string): string => {
  if (!src) return ''

  // 如果已经是相对路径，直接返回规范化后的路径
  if (!src.startsWith('http')) {
    return normalizePath(src)
  }

  // 处理localhost的情况
  if (src.includes('localhost')) {
    const resourcePrefixMatch = src.match(/\/osr(\/.*$)/)
    if (resourcePrefixMatch && resourcePrefixMatch[1]) {
      return normalizePath(resourcePrefixMatch[1])
    }
  }

  // 检查URL格式是否有效
  if (!/^https?:\/\/[^\s]+$/.test(src)) {
    logger.warn('Invalid URL format:', src)
    // 尝试从字符串中提取路径
    const pathMatch = src.match(/\/[^?#]*/)
    return pathMatch ? normalizePath(pathMatch[0]) : normalizePath(src)
  }

  // 解析URL
  const url = new URL(src)
  const pathname = url.pathname

  // 如果URL路径包含基础资源路径，提取相对部分
  if (pathname.startsWith(baseResourcePath)) {
    return normalizePath(pathname.substring(baseResourcePath.length))
  }

  // 不含基础资源路径的情况，返回完整pathname
  return normalizePath(pathname)
}

/**
 * 获取图片完整URL
 * @param src 相对路径
 * @param useThumbnail 是否使用缩略图
 * @returns 完整URL
 */
export const getFullImageUrl = (src: string, useThumbnail: boolean = false): string => {
  // 确保使用相对路径获取资源
  const relativePath = extractRelativePath(src)
  // 应用缩略图逻辑
  const finalPath = useThumbnail ? relativePath : relativePath.replace(THUMBNAIL, '')
  // 返回完整URL
  return fileApi.getResourceURL(finalPath)
}

/**
 * 处理图片上传逻辑
 * @param file 文件对象
 * @param editor 编辑器实例
 * @param fileBucket 文件存储桶
 * @param useThumbnail 是否使用缩略图
 * @returns Promise，返回上传后的相对路径
 */
export const handleImageUpload = (
  file: File,
  editor: EditorWithStorage,
  fileBucket: string,
  useThumbnail: boolean = false,
) => {
  logger.debug('handleImageUpload started for file:', file.name, file.type)

  return fileApi.uploadImage(file, fileBucket).then((res) => {
    // 获取相对路径，确保不保存完整域名
    let imageUrl = res.data

    logger.debug('Original image URL from server:', imageUrl)

    if (!useThumbnail && imageUrl.includes(THUMBNAIL)) {
      imageUrl = imageUrl.replace(THUMBNAIL, '')
      logger.debug('Removed thumbnail from URL:', imageUrl)
    }

    // 确保保存的是相对路径，使用编辑器的图片扩展提供的转换方法
    const relativePath = editor.storage.image?.transformSrc
      ? editor.storage.image.transformSrc(imageUrl)
      : extractRelativePath(imageUrl)

    logger.debug('Converted to relative path:', relativePath)

    // 获取图片尺寸
    const img = document.createElement('img')
    img.onerror = (event: string | Event) => {
      logger.error('Image loading error:', event)
    }

    img.onload = () => {
      const maxWidth = 600
      const maxHeight = 800
      let width = img.width
      let height = img.height
      let ratio = 1

      // 记录原始尺寸
      logger.debug('Original image dimensions:', { width, height, aspectRatio: width / height })

      // 修改逻辑，同时考虑宽度和高度的限制，保持宽高比
      if (width > maxWidth || height > maxHeight) {
        const ratioWidth = maxWidth / width
        const ratioHeight = maxHeight / height
        // 取较小的比例，确保宽和高都不超过限制
        ratio = Math.min(ratioWidth, ratioHeight)
        width = Math.round(width * ratio)
        height = Math.round(height * ratio)
      }

      // 记录调整后的尺寸
      logger.debug('Adjusted image dimensions:', { width, height, ratio })

      // 设置图片，使用相对路径，并确保 width 和 height 都被显式设置
      logger.debug('Setting image with relative path:', relativePath)
      const imageAttrs: ImageAttributes = {
        src: relativePath,
        width: `${width}px`,
        height: `${height}px`,
      }
      editor.commands.setImage(imageAttrs)
    }

    // 加载图片需要使用完整路径
    const fullImageUrl = editor.storage.image?.getFullUrl
      ? editor.storage.image.getFullUrl(imageUrl)
      : getFullImageUrl(imageUrl, useThumbnail)

    logger.debug('Loading image with full URL:', fullImageUrl)
    img.src = fullImageUrl

    // 返回相对路径
    return relativePath
  })
}
