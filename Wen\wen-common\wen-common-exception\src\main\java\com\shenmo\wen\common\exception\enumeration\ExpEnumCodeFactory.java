package com.shenmo.wen.common.exception.enumeration;


import com.shenmo.wen.common.exception.BaseException;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;

import java.util.Objects;

/**
 * 异常枚举code值快速创建
 *
 * <AUTHOR>
 */
public class ExpEnumCodeFactory {

    /**
     * 获取异常模块
     *
     * @param cls     异常枚举class对象
     * @param code    {@link org.springframework.http.HttpStatus}状态码
     * @param ordinal 枚举序号
     * @return 合成后的异常编码
     * <AUTHOR>
     */
    public static Integer getExceptionModule(@NonNull Class<? extends ExceptionEnum> cls, int code, int ordinal) {

        ExceptionType exceptionType = cls.getAnnotation(ExceptionType.class);
        if (Objects.isNull(exceptionType)) {

            throw new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, "异常枚举类缺少@ExpEnumType");
        }
        String codeValue = code + exceptionType.module() + (ordinal >= 0 ? ordinal : "");
        return Integer.valueOf(codeValue);
    }

    public static Class<? extends BaseException> getExceptionType(@NonNull Class<? extends ExceptionEnum> cls) {

        ExceptionType exceptionType = cls.getAnnotation(ExceptionType.class);
        if (Objects.isNull(exceptionType)) {

            throw new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, "异常枚举类缺少@ExpEnumType");
        }
        return exceptionType.type();
    }
}
