// 颜色变量定义 - 统一管理所有颜色
// 使用 SCSS 变量作为单一数据源，然后生成 CSS 自定义属性

@use 'sass:map';

// ===== 基础颜色定义 =====
// 亮色主题颜色
$light-colors: (
  // 基础色
  'white': #fff,
  'white-1': #f0f0f0,
  'white-2': #ddd,
  'creamy-white-1': #eeece4,
  'creamy-white-2': #e4e1d8,
  'creamy-white-3': #dcd8ca,
  'black': #2e2b29,
  'black-contrast': #110f0e,
  'dark-gray': #191919,
  'dark-gray-1': #202020,
  'deep-gray': #111,

  // 灰度色
  'gray-1': rgba(61, 37, 20, 0.05),
  'gray-2': rgba(61, 37, 20, 0.08),
  'gray-3': rgba(61, 37, 20, 0.12),
  'gray-4': rgba(53, 38, 28, 0.3),
  'gray-5': rgba(28, 25, 23, 0.6),
  // 功能色
  'green': #22c55e,
  'blue': #4ba3fd,
  'blue-light': #e6f3ff,
  'purple': #6a00f5,
  'purple-contrast': #5800cc,
  'purple-light': rgba(88, 5, 255, 0.05),
  'yellow-contrast': #facc15,
  'yellow': rgba(250, 204, 21, 0.4),
  'yellow-light': #fffae5,
  'red': #ff5c33,
  'red-light': #ffebe5,

  // 引用块颜色
  'blockquote-border-light': #d8d5d3,
  'blockquote-border-dark': #935c38,

  // 边框和阴影
  'border-1': 0.1rem solid rgba(61, 37, 20, 0.12),
  'shadow': 0 0.25rem 0.6rem rgba(0, 0, 0, 0.1),
  // 代码高亮色
  'code-text': #24292e,
  'code-comment': #6a737d,
  'code-keyword': #d73a49,
  'code-string': #032f62,
  'code-number': #005cc5,
  'code-function': #6f42c1,
  'code-variable': #e36209,
  'code-tag': #22863a,
  'code-attribute': #6f42c1,
  'code-builtin': #005cc5,
  'code-meta': #6a737d,
  'code-deletion-color': #b31d28,
  'code-deletion-bg': #ffeef0,
  'code-addition-color': #22863a,
  'code-addition-bg': #f0fff4
);

// 暗色主题颜色
$dark-colors: (
  // 基础色
  'white': #121212,
  'white-1': #242424,
  'white-2': #363636,
  'creamy-white-1': #1a1a1a,
  'creamy-white-2': #262626,
  'creamy-white-3': #333,
  'black': #e0e0e0,
  'black-contrast': #fff,
  'dark-gray': #191919,
  'dark-gray-1': #202020,
  'deep-gray': #111,

  // 灰度色
  'gray-1': rgba(200, 200, 200, 0.05),
  'gray-2': rgba(200, 200, 200, 0.08),
  'gray-3': rgba(200, 200, 200, 0.12),
  'gray-4': rgba(200, 200, 200, 0.3),
  'gray-5': rgba(200, 200, 200, 0.6),
  // 功能色
  'green': #22c55e,
  'blue': #4ba3fd,
  'blue-light': #2a3745,
  'purple': #9d6dff,
  'purple-contrast': #8a5cf5,
  'purple-light': rgba(154, 92, 255, 0.15),
  'yellow-contrast': #facc15,
  'yellow': rgba(250, 204, 21, 0.4),
  'yellow-light': #3f3a14,
  'red': #ff5c33,
  'red-light': #3d1a12,

  // 引用块颜色
  'blockquote-border-light': #d8d5d3,
  'blockquote-border-dark': #935c38,

  // 边框和阴影
  'border-1': 0.1rem solid rgba(200, 200, 200, 0.12),
  'shadow': 0 0.25rem 0.6rem rgba(255, 255, 255, 0.1),
  // 代码高亮色
  'code-text': #e6edf3,
  'code-comment': #8b949e,
  'code-keyword': #ff7b72,
  'code-string': #a5d6ff,
  'code-number': #79c0ff,
  'code-function': #d2a8ff,
  'code-variable': #ffa657,
  'code-tag': #7ee787,
  'code-attribute': #d2a8ff,
  'code-builtin': #79c0ff,
  'code-meta': #8b949e,
  'code-deletion-color': #ffa198,
  'code-deletion-bg': #490202,
  'code-addition-color': #56d364,
  'code-addition-bg': #0f5132
);

// ===== 生成 CSS 自定义属性 =====
// 生成亮色主题的 CSS 变量
:root {
  @each $name, $value in $light-colors {
    --#{$name}: #{$value};
  }
}

// 生成暗色主题的 CSS 变量
[data-theme='dark'] {
  @each $name, $value in $dark-colors {
    --#{$name}: #{$value};
  }
}

// ===== 导出 SCSS 变量供其他文件使用 =====
// 注意：SCSS 不支持动态变量名，所以我们只导出映射本身
$light-theme-colors: $light-colors !default;
$dark-theme-colors: $dark-colors !default;

// ===== 工具函数 =====
// 获取颜色值的函数
@function get-color($name, $theme: 'light') {
  @if $theme == 'light' {
    @return map.get($light-colors, $name);
  } @else if $theme == 'dark' {
    @return map.get($dark-colors, $name);
  }

  @error "Unknown theme: #{$theme}";
}

// 生成主题颜色变量的混入
@mixin theme-colors($theme: 'light') {
  $colors: if($theme == 'light', $light-colors, $dark-colors);

  @each $name, $value in $colors {
    --#{$name}: #{$value};
  }
}
