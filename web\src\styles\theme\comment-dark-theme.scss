/* 评论组件深色模式样式 */

/* 评论内容应该与评论容器颜色一致 */
.dark-theme .user-comment-container .comment-content-row .ProseMirror,
.dark-theme .user-comment-container .comment-content-row .tiptap-editor-wrapper,
.dark-theme .user-comment-container .comment-content-row .editor-content,
.dark-theme .user-comment-container .comment-content-row p {
  background-color: var(--comment-container-bg);
}

/* 评论中的引用块只设置字体颜色，不设置背景色 */
.dark-theme .user-comment-container .comment-content-row blockquote {
  border-left: 3px solid var(--blockquote-border-dark);
  color: var(--gray-5);
}

/* 固定评论内容应该与固定评论容器颜色一致 */
.dark-theme .user-comment-container-fixed .comment-content-row .ProseMirror,
.dark-theme .user-comment-container-fixed .comment-content-row .tiptap-editor-wrapper,
.dark-theme .user-comment-container-fixed .comment-content-row .editor-content,
.dark-theme .user-comment-container-fixed .comment-content-row p,
.dark-theme .comment-flash .comment-content-row .ProseMirror,
.dark-theme .comment-flash .comment-content-row .tiptap-editor-wrapper,
.dark-theme .comment-flash .comment-content-row .editor-content,
.dark-theme .comment-flash .comment-content-row p {
  background-color: var(--comment-fixed-bg);
  opacity: 1; /* 确保不透明 */
}

/* 固定评论中的引用块只设置字体颜色，不设置背景色 */
.dark-theme .user-comment-container-fixed .comment-content-row blockquote,
.dark-theme .comment-flash .comment-content-row blockquote {
  border-left: 3px solid var(--blockquote-border-dark);
  color: var(--gray-5);
}

/* 评论输入行应与评论列表容器颜色一致 */
.dark-theme .comment-input-row,
.dark-theme .comment-reply-row {
  border-color: var(--gray-3);
}

/* 确保普通评论的回复框继承普通评论容器的背景色 */
.dark-theme .user-comment-container .comment-reply-row {
  background-color: var(--comment-reply-bg);
}

.dark-theme .user-comment-container .comment-reply-send-btn {
  background-color: var(--comment-reply-btn-bg);
}

/* 确保固定评论中的回复框在深色模式下也保持蓝色背景 */
.dark-theme .user-comment-container-fixed .comment-reply-row,
.dark-theme .comment-flash .comment-reply-row {
  background-color: var(--comment-fixed-reply-bg);
  border-color: var(--gray-3);
  opacity: 1; /* 确保不透明 */
}

/* 确保固定评论中的回复框中的编辑器也保持蓝色背景 */
.dark-theme .user-comment-container-fixed .comment-reply-row .tiptap-editor-wrapper,
.dark-theme .user-comment-container-fixed .comment-reply-row .editor-content,
.dark-theme .comment-flash .comment-reply-row .tiptap-editor-wrapper,
.dark-theme .comment-flash .comment-reply-row .editor-content {
  background-color: var(--comment-fixed-reply-bg);
  opacity: 1; /* 确保不透明 */
}

/* 确保固定评论中的按钮区域也保持蓝色背景 */
.dark-theme .user-comment-container-fixed .comment-reply-send-btn,
.dark-theme .comment-flash .comment-reply-send-btn {
  background-color: var(--comment-fixed-reply-btn-bg);
  opacity: 1; /* 确保不透明 */
}

/* 确保普通评论中的编辑器内容区域保持普通评论容器的背景色 */
.dark-theme .user-comment-container .comment-reply-row .tiptap-editor-wrapper,
.dark-theme .user-comment-container .comment-reply-row .editor-content {
  background-color: var(--comment-reply-bg);
}

/* 确保普通评论中的输入框有合适的背景色 */
.dark-theme .user-comment-container .comment-reply-row .ProseMirrorInput {
  background-color: var(--comment-reply-prosemirror-bg);
  border-color: var(--gray-4);
  color: var(--black);
}

/* 为暗色模式下的评论输入框添加更明显的边框和对比度 */
.dark-theme .comment-input-row {
  border-top: 1px solid var(--gray-4);
  background-color: var(--comment-input-bg);
}

/* 主评论输入框的编辑器背景色也需要与容器一致 */
.dark-theme .comment-input-row .tiptap-editor-wrapper,
.dark-theme .comment-input-row .editor-content {
  background-color: var(--comment-input-bg);
}

.dark-theme .comment-input-row .ProseMirrorInput {
  background-color: var(--comment-input-prosemirror-bg);
  border-color: var(--gray-3);
  color: var(--black);
}

/* 固定评论的输入框 */
.dark-theme .user-comment-container-fixed .comment-reply-row .ProseMirrorInput,
.dark-theme .comment-flash .comment-reply-row .ProseMirrorInput {
  background-color: var(--comment-fixed-prosemirror-bg);
  border-color: var(--gray-4);
  color: var(--black);
}

.dark-theme .comment-title-container {
  color: var(--black);
}

.dark-theme .breadcrumb-text {
  color: var(--blue);
}

.dark-theme .comment-flash {
  background-color: var(--blue-light);
}
