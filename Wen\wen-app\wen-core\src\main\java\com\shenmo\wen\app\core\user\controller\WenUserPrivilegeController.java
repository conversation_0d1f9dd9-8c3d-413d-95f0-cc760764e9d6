package com.shenmo.wen.app.core.user.controller;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.shenmo.wen.app.core.user.service.WenUserPrivilegeService;
import com.shenmo.wen.common.pojo.response.ResponseData;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeActivateReq;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeCodeReq;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeSearchReq;
import com.shenmo.wen.app.core.user.pojo.resp.WenUserPrivilegeResp;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/user-privileges")
@RequiredArgsConstructor
public class WenUserPrivilegeController {

    private final WenUserPrivilegeService service;

    @PostMapping("code")
    public ResponseData<String> code(@Validated @RequestBody WenUserPrivilegeCodeReq req) {
        return ResponseData.success(service.code(req));
    }

    @PostMapping("/activation")
    public ResponseData<Boolean> activate(@Validated @RequestBody WenUserPrivilegeActivateReq req) {
        return ResponseData.success(service.activate(req));
    }

    @GetMapping
    public ResponseData<List<WenUserPrivilegeResp>> search(WenUserPrivilegeSearchReq req) {
        return ResponseData.success(service.search(req));
    }
}
