# 二维码验证功能测试指南

## 🧪 测试准备

### 1. 创建测试二维码
您可以使用以下任一方式创建测试二维码：

#### 在线二维码生成器：
- 访问：https://www.qr-code-generator.com/
- 输入测试内容：`https://example.com/test-login?user=testuser&token=abc123`
- 生成并下载二维码图片

#### 使用手机应用：
- 微信扫一扫 → 右上角 → 我的二维码
- 支付宝 → 扫一扫 → 右上角 → 我的码
- 截图保存

### 2. 测试内容建议
```
URL类型：https://login.example.com/qr?token=xyz789
文本类型：LOGIN_TOKEN_ABC123DEF456
JSON类型：{"action":"login","user":"test","timestamp":1692345678}
```

## 🔍 完整测试流程

### 步骤1：启动服务
```bash
# 前端
cd web
npm run dev
# 访问 http://localhost:5174

# 后端（如果需要）
# 启动Spring Boot应用
```

### 步骤2：创建测试特权
1. 在特权管理页面创建一个二维码验证类型的特权
2. 确保特权有有效的模板链接

### 步骤3：测试验证流程
1. **点击特权卡片**，打开验证弹框
2. **查看步骤1**：
   - ✅ 显示模板平台链接
   - ✅ 显示"请先访问对应的模板平台"提示
   - ❌ "开始验证"按钮应该隐藏

3. **点击模板链接**：
   - ✅ 链接在新标签页打开
   - ✅ 二维码上传区域出现
   - ✅ 显示"上传二维码截图"区域

4. **上传二维码图片**：
   - ✅ 显示"正在解析二维码..."loading状态
   - ✅ 解析成功后显示"二维码解析成功"
   - ✅ 显示二维码内容预览（前50个字符）
   - ✅ "开始验证"按钮出现

5. **点击开始验证**：
   - ✅ 进入步骤2
   - ✅ 后端接收到qrcc参数
   - ✅ 邮件发送成功

6. **验证页面测试**：
   - ✅ 打开邮件中的验证链接
   - ✅ 页面显示"正在生成二维码..."
   - ✅ 生成真实的二维码（基于上传的内容）
   - ✅ 显示二维码内容提示
   - ✅ 二维码可以被手机扫描

## 🐛 常见问题排查

### 前端问题
1. **二维码解析失败**
   - 检查图片是否清晰
   - 确认图片包含有效二维码
   - 查看浏览器控制台错误信息

2. **开始验证按钮不出现**
   - 确认已点击模板链接
   - 确认二维码解析成功
   - 检查`shouldShowStartButton`计算属性

3. **API调用失败**
   - 检查网络连接
   - 查看浏览器Network标签
   - 确认后端服务运行正常

### 后端问题
1. **qrcc验证失败**
   - 检查`WenUserPrivilegeVerificationStartReq`是否正确接收qrcc
   - 确认验证逻辑：`verificationType == 1`时qrcc必填
   - 查看后端日志

2. **邮件模板问题**
   - 确认`qrCodeUrl`变量正确设置
   - 检查模板文件路径
   - 验证邮件发送配置

### 验证页面问题
1. **二维码不显示**
   - 检查qrcode.js库是否加载成功
   - 确认`qrCodeContent`变量有值
   - 查看浏览器控制台错误

2. **二维码生成失败**
   - 检查二维码内容是否过长
   - 确认内容格式正确
   - 验证qrcode.js版本兼容性

## 📊 测试检查清单

### 功能测试 ✅
- [ ] 模板链接显示和点击
- [ ] 二维码图片上传
- [ ] 二维码内容解析
- [ ] 解析状态显示
- [ ] 开始验证按钮控制
- [ ] API参数传递
- [ ] 后端验证逻辑
- [ ] 邮件模板生成
- [ ] 验证页面二维码生成

### 异常测试 ⚠️
- [ ] 上传非二维码图片
- [ ] 上传损坏图片
- [ ] 网络异常处理
- [ ] 后端服务异常
- [ ] 邮件发送失败

### 用户体验测试 🎨
- [ ] Loading状态显示
- [ ] 错误信息提示
- [ ] 成功状态反馈
- [ ] 响应式布局
- [ ] 深色主题适配

## 🎯 预期结果

完整流程成功后，您应该看到：
1. ✅ 用户可以顺利上传二维码图片
2. ✅ 系统自动解析出二维码内容
3. ✅ 内容正确传递给后端
4. ✅ 验证页面生成真实可扫描的二维码
5. ✅ 整个验证流程完整闭环

## 🚀 性能优化建议

1. **图片处理优化**：
   - 限制上传图片大小（建议2MB以内）
   - 压缩图片后再解析
   - 添加图片格式验证

2. **解析性能优化**：
   - 使用Web Worker进行后台解析
   - 添加解析超时机制
   - 缓存解析结果

3. **用户体验优化**：
   - 添加解析进度条
   - 支持拖拽上传
   - 提供示例二维码

测试愉快！🎉
