package com.shenmo.wen.app.core.interaction.controller;

import com.shenmo.wen.app.core.interaction.pojo.req.WenInteractionReq;
import com.shenmo.wen.app.core.interaction.pojo.resp.WenInteractionResp;
import com.shenmo.wen.app.core.interaction.service.WenInteractionService;
import com.shenmo.wen.common.pojo.response.ResponseData;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/interactions")
@RequiredArgsConstructor
public class WenInteractionController {
    private final WenInteractionService service;

    @PostMapping
    public ResponseData<WenInteractionResp> toggleInteraction(@RequestBody WenInteractionReq req) {
        return ResponseData.success(service.toggleInteraction(req));
    }
}
