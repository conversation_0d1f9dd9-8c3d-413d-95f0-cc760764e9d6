/*
 * TipTapEditor 组件样式
 * TipTap编辑器核心组件的样式定义，包括颜色选择器、全屏按钮和字符计数
 */

:deep(.n-color-picker-trigger) {
  border: none !important;
  width: 0 !important;
  height: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: hidden !important;
}

.fullscreen-close-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  z-index: 1000;
  cursor: pointer;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.fullscreen-close-button:hover {
  transform: scale(1.1);
}

.character-count {
  text-align: right;
  font-size: 0.8rem;
  color: #888;
  margin-top: 0.25rem;
  padding-right: 0.5rem;
}

.character-count-fullscreen {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0.75rem 1rem;
  margin-top: 0.5rem;
  background-color: var(--white);
  color: var(--gray-5);
  font-size: 0.75rem;
  font-family: var(--tiptap-font-family-mono);
  border-top: 1px solid var(--gray-3);
  user-select: none;
  min-height: 2rem;
  box-sizing: border-box;
  flex-shrink: 0;

  .dark-theme & {
    background-color: var(--dark-gray);
    color: var(--gray-5);
    border-top-color: var(--gray-3);
  }
}

.editor-content-fullscreen {
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  :deep(.n-scrollbar-rail) {
    background: transparent;
  }

  :deep(.n-scrollbar-rail__scrollbar) {
    background: var(--scrollbar-color, rgba(0, 0, 0, 0.2));
    border-radius: 4px;
  }
}

/*
 * BilibiliNodeView 组件样式
 * Bilibili视频节点视图组件的样式定义，包括iframe和错误状态
 */

.bilibili-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.bilibili-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: var(--color-border-light);
  color: var(--color-text-secondary);
  border-radius: 4px;
}

.bilibili-error p {
  margin: 0;
  font-size: 14px;
}

/* 全局样式已经排除了 bilibili-container，不再需要覆盖选中状态 */
.bilibili-container {
  outline: none;
  border: none;
}

/*
 * MentionNodeView 组件样式
 * @提及节点视图组件的样式定义，包括提及样式、头像和悬停效果
 */

.mention {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background-color: var(--color-primary-light);
  color: var(--color-primary);
  border-radius: 12px;
  font-size: 14px;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.mention:hover {
  background-color: var(--color-primary-hover);
}

.mention-name {
  font-weight: 500;
}

.mention-avatar {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

/*
 * EditorModalHandler 组件样式
 * 编辑器模态框处理器组件的样式定义，包括布局工具类
 */

.flex-column-gap12 {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/*
 * TiptapBtn 组件样式
 * Tiptap工具栏按钮组件的样式定义，包括按钮容器和激活状态
 */

.tiptap-btn-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  height: 1.75rem;
  width: 1.75rem;
}

.is-active {
  background-color: var(--tiptap-button-active-bg);
  color: var(--tiptap-button-active-color);
}

// 禁用 TiptapBtn 中 NButton 的 hover 效果
:deep(.tiptap-btn-wrapper .n-button) {
  &:hover {
    background-color: inherit;
    border-color: inherit;
    color: inherit;
  }

  // 激活状态下也禁用 hover 效果
  &.is-active:hover {
    background-color: var(--tiptap-button-active-bg);
    color: var(--tiptap-button-active-color);
  }
}
