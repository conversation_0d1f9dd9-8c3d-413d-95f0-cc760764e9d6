import { Extension } from '@tiptap/core'
import { Editor } from '@tiptap/core'
import { Mark } from '@tiptap/pm/model'
import { Plugin, PluginKey } from '@tiptap/pm/state'
import { EditorState } from '@tiptap/pm/state'
import { EditorView } from '@tiptap/pm/view'

import type { RawCommands, SingleCommands } from '@tiptap/core'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    formatPainter: {
      toggleFormatPainter: () => ReturnType
    }
  }
}

export interface FormatPainterOptions {
  enabled: boolean
}

export const FormatPainterExtension = Extension.create<FormatPainterOptions>({
  name: 'formatPainter',

  addOptions() {
    return {
      enabled: true,
    }
  },

  addStorage() {
    return {
      formatPainter: {
        isActive: false,
        sourceMarks: null,
        sourceNode: null,
      },
    }
  },

  addCommands() {
    return {
      toggleFormatPainter:
        () =>
        ({ editor, state }: { editor: Editor; state: EditorState }) => {
          const { formatPainter } = editor.storage
          const { selection } = state
          const { from, to } = selection

          if (!formatPainter.isActive) {
            // 保存源格式
            const node = state.doc.nodeAt(from)
            if (node) {
              formatPainter.isActive = true
              formatPainter.sourceNode = node
              formatPainter.sourceMarks = node.marks
              return true
            }
            return false
          } else {
            // 应用格式
            if (formatPainter.sourceMarks) {
              const tr = state.tr

              // 清除目标范围内的所有标记
              state.doc.nodesBetween(from, to, (node, pos) => {
                if (node.isText) {
                  tr.removeMark(pos, pos + node.nodeSize, null)
                }
              })

              // 应用源格式的标记
              formatPainter.sourceMarks.forEach((mark: Mark) => {
                tr.addMark(from, to, mark)
              })

              editor.view.dispatch(tr)
            }

            formatPainter.isActive = false
            formatPainter.sourceNode = null
            formatPainter.sourceMarks = null
            return true
          }
        },
    } as unknown as RawCommands
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('formatPainter'),
        props: {
          handleClick: (view: EditorView, _pos) => {
            const editor = (view as unknown as { editor: Editor }).editor
            if (!editor) return false

            const { formatPainter } = editor.storage
            if (formatPainter?.isActive) {
              ;(
                editor.commands as SingleCommands & { toggleFormatPainter: () => boolean }
              ).toggleFormatPainter()
              return true
            }
            return false
          },
        },
      }),
    ]
  },
})
