/**
 * 邮箱验证码工具函数
 */

import { EmailCodeTypeDescriptions } from '@/types/email/email-code-descriptions.types'
import { EmailCodeType } from '@/types/email/email-code-type.types'

/**
 * 根据代码获取枚举
 * @param code 代码
 * @returns 枚举值
 */
export function getEmailCodeTypeFromCode(code: string): EmailCodeType {
  const type = Object.values(EmailCodeType).find((t) => t === code)
  if (!type) {
    throw new Error(`未知的验证码类型: ${code}`)
  }
  return type
}

/**
 * 验证代码是否有效
 * @param code 代码
 * @returns 是否有效
 */
export function isValidEmailCodeType(code: string): boolean {
  try {
    getEmailCodeTypeFromCode(code)
    return true
  } catch {
    return false
  }
}

/**
 * 获取邮箱验证码类型的描述
 * @param type 邮箱验证码类型
 * @returns 描述
 */
export function getEmailCodeTypeDescription(type: EmailCodeType): string {
  return EmailCodeTypeDescriptions[type]
}

/**
 * 获取所有邮箱验证码类型
 * @returns 所有类型的数组
 */
export function getAllEmailCodeTypes(): EmailCodeType[] {
  return Object.values(EmailCodeType)
}
