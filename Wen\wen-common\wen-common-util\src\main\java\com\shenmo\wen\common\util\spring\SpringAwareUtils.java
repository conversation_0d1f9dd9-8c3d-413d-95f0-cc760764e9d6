package com.shenmo.wen.common.util.spring;

import com.shenmo.wen.common.exception.BaseException;
import com.shenmo.wen.common.util.AssertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.Aware;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.*;
import org.springframework.core.Ordered;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;

import java.util.Objects;

/**
 * spring aware 工具
 * <p>
 * {@link Aware}参考spring bean的生命周期: {@link BeanFactory}上声明的注释信息
 *
 * <AUTHOR>
 */
@Slf4j
public class SpringAwareUtils implements Ordered,
        BeanFactoryPostProcessor,
        BeanFactoryAware,
        ApplicationContextAware,
        EnvironmentAware,
        ApplicationEventPublisherAware {


    private static ApplicationContext applicationContext;
    private static ConfigurableListableBeanFactory beanFactory;
    private static SpringAwareUtils self;
    private static ConfigurableEnvironment environment;
    private static ApplicationEventPublisher eventPublisher;

    /**
     * {@link Ordered}
     */
    private int order = Ordered.HIGHEST_PRECEDENCE + 9;

    /**
     * {@link BeanFactory}
     *
     * @return {@link BeanFactory}
     * <AUTHOR>
     */
    @NonNull
    public static ConfigurableListableBeanFactory beanFactory() {

        selfInspection();
        return beanFactory;
    }

    /**
     * {@link ApplicationContext}
     *
     * @return {@link ApplicationContext}
     * <AUTHOR>
     */
    @NonNull
    public static ApplicationContext applicationContext() {

        selfInspection();
        return applicationContext;
    }

    /**
     * {@link ApplicationContext}
     *
     * @return {@link ApplicationContext}
     * <AUTHOR>
     */
    @NonNull
    public static ConfigurableEnvironment env() {

        selfInspection();
        return environment;
    }


    /**
     * {@link ApplicationEventPublisher}
     *
     * @return {@link ApplicationEventPublisher}
     * <AUTHOR>
     */
    @NonNull
    public static ApplicationEventPublisher eventPublisher() {

        selfInspection();
        return eventPublisher;
    }

    /**
     * 自检
     *
     * <AUTHOR>
     */
    private static void selfInspection() {
        AssertUtils.isTrue(Objects.nonNull(self), new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, "The current spring environment is unavailable"));
    }

    @Override
    public int getOrder() {

        return order;
    }

    public void setOrder(int order) {

        this.order = order;
    }


    @Override
    public void postProcessBeanFactory(@NonNull ConfigurableListableBeanFactory beanFactory) throws BeansException {
        setBeanFactory(beanFactory);
    }

    @Override
    public void setBeanFactory(@NonNull BeanFactory beanFactory) throws BeansException {

        SpringAwareUtils.self = this;
        SpringAwareUtils.beanFactory = (ConfigurableListableBeanFactory) beanFactory;
    }

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {

        SpringAwareUtils.self = this;
        SpringAwareUtils.applicationContext = applicationContext;
    }

    @Override
    public void setEnvironment(@NonNull Environment environment) {

        SpringAwareUtils.self = this;
        SpringAwareUtils.environment = (ConfigurableEnvironment) environment;
    }

    @Override
    public void setApplicationEventPublisher(@NonNull ApplicationEventPublisher applicationEventPublisher) {

        SpringAwareUtils.self = this;
        SpringAwareUtils.eventPublisher = applicationEventPublisher;
    }
}
