import { ref, computed, type Ref, type ComputedRef } from 'vue'

import {
  createLoginUser,
  type LoginUserCreationParams,
} from '@/types/user/login-user-creation.types'
import localStorage from '@/utils/storage/local-storage'

/**
 * 用户认证状态类型
 */
interface AuthUser {
  /** 用户ID */
  id: string
  /** 用户名 */
  username: string
  /** 用户等级 */
  level: number
  /** 用户头像 */
  avatar?: string
  /** 用户邮箱 */
  email?: string
}

/**
 * 认证组合式函数返回值类型
 */
interface UseAuthReturn {
  /** 当前用户信息 */
  currentUser: Ref<AuthUser | null>
  /** 是否已登录 */
  isAuthenticated: ComputedRef<boolean>
  /** 是否为管理员 */
  isAdmin: ComputedRef<boolean>
  /** 获取用户信息 */
  getCurrentUser: () => AuthUser | null
  /** 设置用户信息 */
  setCurrentUser: (user: AuthUser | null) => void
  /** 清除用户信息 */
  clearCurrentUser: () => void
  /** 检查用户权限 */
  hasPermission: (requiredLevel: number) => boolean
}

/**
 * 用户认证管理组合式函数
 * 提供用户认证状态管理、权限检查等功能
 */
export function useAuth(): UseAuthReturn {
  // 当前用户信息
  const currentUser = ref<AuthUser | null>(null)

  // 是否已登录
  const isAuthenticated = computed(() => currentUser.value !== null)

  // 是否为管理员（假设等级10以上为管理员）
  const isAdmin = computed(() => {
    return currentUser.value ? currentUser.value.level >= 10 : false
  })

  /**
   * 获取当前用户信息
   * 优先从内存获取，如果没有则从本地存储获取
   * @returns 用户信息或null
   */
  const getCurrentUser = (): AuthUser | null => {
    if (currentUser.value) {
      return currentUser.value
    }

    // 从本地存储获取用户信息
    const storedUser = localStorage.getLoginUser()
    if (storedUser) {
      // 将 LoginUser 转换为 AuthUser
      currentUser.value = {
        id: storedUser.id,
        username: storedUser.username,
        level: storedUser.level,
        avatar: storedUser.avatar,
        email: storedUser.email,
      }
      return currentUser.value
    }

    return null
  }

  /**
   * 设置当前用户信息
   * 同时更新内存和本地存储
   * @param user 用户信息
   */
  const setCurrentUser = (user: AuthUser | null): void => {
    currentUser.value = user
    if (user) {
      // 将AuthUser转换为LoginUser格式
      const loginUserCreationParams: LoginUserCreationParams = {
        id: user.id,
        username: user.username,
        level: user.level,
        avatar: user.avatar,
        email: user.email,
      }
      const loginUserData = createLoginUser(loginUserCreationParams)
      localStorage.setLoginUser(loginUserData)
    } else {
      localStorage.removeLoginUser()
    }
  }

  /**
   * 清除当前用户信息
   * 清除内存和本地存储中的用户数据
   */
  const clearCurrentUser = (): void => {
    currentUser.value = null
    localStorage.removeLoginUser()
  }

  /**
   * 检查用户是否具有指定权限
   * @param requiredLevel 所需的最低权限等级
   * @returns 是否具有权限
   */
  const hasPermission = (requiredLevel: number): boolean => {
    const user = getCurrentUser()
    return user ? user.level >= requiredLevel : false
  }

  // 初始化时从本地存储加载用户信息
  getCurrentUser()

  return {
    currentUser,
    isAuthenticated,
    isAdmin,
    getCurrentUser,
    setCurrentUser,
    clearCurrentUser,
    hasPermission,
  }
}
