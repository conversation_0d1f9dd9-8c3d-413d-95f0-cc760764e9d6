import { ref, type Ref } from 'vue'

import type { Article } from '@/types/article/article.types'
import type { TiptapEditorRef } from '@/types/component/tiptap-editor-ref.types'

import type { Span } from 'naive-ui/es/legacy-grid/src/interface'

/**
 * 文章列表状态管理组合式函数返回值类型
 */
export interface UseArticleListStateReturn {
  /** 文章列表数据 */
  articleList: Ref<Article[]>
  /** 加载状态 */
  loading: Ref<boolean>
  /** 是否没有更多数据 */
  noMore: Ref<boolean>
  /** 卡片列跨度 */
  cardColSpan: Ref<Span>
  /** 文章编辑器映射 */
  articleTiptapEditorMap: Ref<Map<string, TiptapEditorRef>>
  /** 容器引用 */
  containerRef: Ref<HTMLElement | null>
  /** 滚动容器引用 */
  scrollContainerRef: Ref<HTMLElement | null>
  /** 当前已加载文章数量 */
  currentLoadedArticlesCount: Ref<number>
  /** 追踪每行卡片数 */
  cardsPerRow: Ref<number>
}

/**
 * 文章列表状态管理组合式函数
 * 提供文章列表的基础状态管理
 */
export function useArticleListState(): UseArticleListStateReturn {
  // 文章列表数据
  const articleList = ref<Article[]>([])
  const loading = ref(false)
  const noMore = ref(false)
  const cardColSpan = ref<Span>(6)
  const articleTiptapEditorMap = ref(new Map<string, TiptapEditorRef>())

  // 追踪当前已加载的文章总数
  const currentLoadedArticlesCount = ref(0)
  // 追踪每行卡片数
  const cardsPerRow = ref(1)

  // DOM引用
  const containerRef = ref<HTMLElement | null>(null)
  const scrollContainerRef = ref<HTMLElement | null>(null)

  return {
    articleList,
    loading,
    noMore,
    cardColSpan,
    articleTiptapEditorMap,
    containerRef,
    scrollContainerRef,
    currentLoadedArticlesCount,
    cardsPerRow,
  }
}
