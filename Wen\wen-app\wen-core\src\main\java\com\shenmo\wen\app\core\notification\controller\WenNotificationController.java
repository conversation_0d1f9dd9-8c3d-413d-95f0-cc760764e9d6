package com.shenmo.wen.app.core.notification.controller;

import com.shenmo.wen.app.core.notification.pojo.req.WenNotificationLoadReq;
import com.shenmo.wen.app.core.notification.pojo.resp.WenNotificationResp;
import com.shenmo.wen.app.core.notification.service.WenNotificationService;
import com.shenmo.wen.common.pojo.response.PageResult;
import com.shenmo.wen.common.pojo.response.ResponseData;

import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import cn.dev33.satoken.stp.StpUtil;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/notifications")
@RequiredArgsConstructor
public class WenNotificationController {
    private final WenNotificationService service;

    @GetMapping
    public ResponseData<PageResult<WenNotificationResp>> load(@Validated WenNotificationLoadReq req) {
        return ResponseData.success(service.load(req));
    }

    @PatchMapping("/{id}/read-status")
    public ResponseData<Void> read(@PathVariable("id") Long id) {
        service.read(id);
        return ResponseData.success();
    }

    @PatchMapping("/read-status")
    public ResponseData<Void> readAll() {
        service.readAll();
        return ResponseData.success();
    }

    @GetMapping("/total-unread")
    public ResponseData<Long> totalUnread() {
        return ResponseData.success(service.totalUnread());
    }
}
