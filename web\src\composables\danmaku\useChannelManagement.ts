import { reactive } from 'vue'

import type { DanChannel } from '@/types/danmaku/dan-channel.types'
import type { DanmakuChannelConfig } from '@/types/danmaku/danmaku-config.types'
import { getDanmuRightPosition } from '@/utils/danmaku/layout/positionCalculator'

/**
 * 弹幕轨道管理组合式函数返回值类型
 */
interface UseChannelManagementReturn {
  /** 弹幕轨道数据 */
  danChannel: DanChannel
  /** 获取可插入的弹幕轨道索引 */
  getChannelIndex: (el: HTMLDivElement, dmContainer: HTMLDivElement) => number
}

/**
 * 弹幕轨道管理组合式函数
 * 提供弹幕轨道的分配和管理功能
 * @param danmakuConfig 弹幕轨道配置
 */
export function useChannelManagement(
  danmakuConfig: DanmakuChannelConfig,
): UseChannelManagementReturn {
  // 弹幕轨道管理
  const danChannel = reactive<DanChannel>({})

  /**
   * 获取可插入的弹幕轨道索引
   */
  function getChannelIndex(el: HTMLDivElement, dmContainer: HTMLDivElement): number {
    let _channels = [...Array(danmakuConfig.channels).keys()]

    if (danmakuConfig.randomChannel) {
      _channels = _channels.sort(() => 0.5 - Math.random())
    }

    for (const i of _channels) {
      const items = danChannel[i]

      if (items && items.length) {
        for (let j = 0; j < items.length; j++) {
          const danRight = getDanmuRightPosition(items[j] as HTMLDivElement, dmContainer) - 10
          // 安全距离判断
          if (danRight <= (el.offsetWidth - items[j].offsetWidth) * 0.75 || danRight <= 0) {
            break
          }
          if (j === items.length - 1) {
            danChannel[i].push(el)
            el.addEventListener('animationend', () => danChannel[i].splice(0, 1))
            return i % danmakuConfig.channels
          }
        }
      } else {
        danChannel[i] = [el] as HTMLElement[]
        el.addEventListener('animationend', () => danChannel[i].splice(0, 1))
        return i % danmakuConfig.channels
      }
    }
    return -1
  }

  return {
    danChannel,
    getChannelIndex,
  }
}
