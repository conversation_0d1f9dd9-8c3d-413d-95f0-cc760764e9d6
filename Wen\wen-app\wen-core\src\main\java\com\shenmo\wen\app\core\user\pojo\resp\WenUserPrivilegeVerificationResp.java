package com.shenmo.wen.app.core.user.pojo.resp;

import lombok.Data;

/**
 * 用户特权验证响应
 * 
 * <AUTHOR>
 */
@Data
public class WenUserPrivilegeVerificationResp {

    /**
     * 验证流程ID
     */
    private Long id;

    /**
     * 申请用户ID
     */
    private Long userId;

    /**
     * 申请的特权ID
     */
    private Long privilegeId;

    /**
     * 特权名称
     */
    private String privilegeName;

    /**
     * 验证类型：0-短信验证，1-二维码验证
     */
    private Integer verificationType;

    /**
     * 当前步骤：1,2,3
     */
    private Integer currentStep;

    /**
     * 状态：0-进行中，1-成功，2-失败，3-超时
     */
    private Integer status;

    /**
     * 验证页面URL
     */
    private String pageUrl;

    /**
     * 流程过期时间
     */
    private Long expireTime;

    /**
     * 创建时间
     */
    private Long ctTm;
}
