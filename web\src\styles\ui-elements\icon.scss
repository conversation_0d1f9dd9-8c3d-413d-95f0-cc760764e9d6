// 图标通用样式
// 处理图标的选择、拖拽和交互行为

/* 基础图标样式 */
.icon,
svg,
[class*='icon-'],
[class*='Icon'] {
  // 禁用文本选择
  user-select: none;

  // 防止图标被高亮选中
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;

  // 确保图标不会影响文本流
  display: inline-block;
  vertical-align: middle;
}

/* 可点击图标样式 */
.icon.clickable,
.clickable svg,
.clickable [class*='icon-'],
.scope-icon-wrapper,
.cursor-pointer svg,
.cursor-pointer,
[role='button'] svg,
button svg {
  cursor: pointer;

  // 添加平滑的过渡效果
  transition:
    opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
    filter 0.2s ease;

  // 确保变换原点在中心
  transform-origin: center;

  // 悬浮时放大效果
  &:hover {
    opacity: 0.9;
    transform: scale(1.05);
  }

  // 点击时缩小效果
  &:active {
    transform: scale(1);
    opacity: 0.9;
  }
}

/* 防止图标容器的文本选择问题 */
.icon-wrapper,
.scope-icon-wrapper,
.article-header,
.flex-between-center {
  // 对于包含图标的容器，确保图标不会被选中
  svg,
  .icon,
  [class*='icon-'] {
    user-select: none;

    // 防止图标周围出现选择框
    outline: none;

    // 防止焦点样式
    &:focus {
      outline: none;
    }
  }
}

/* 特殊情况：Naive UI 组件中的图标 */
.n-card-header,
.n-button,
.n-tag,
.n-avatar {
  svg,
  .icon,
  [class*='icon-'] {
    user-select: none;
  }
}

/* 防止双击时的文本选择扩散 */
.article-card,
.card-item {
  // 对于卡片容器，允许标题等文本被选择
  user-select: text;

  // 但是图标和按钮区域不允许选择
  .scope-icon-wrapper,
  .n-avatar,
  svg,
  .icon,
  [class*='icon-'],
  .cursor-pointer {
    user-select: none;
  }
}

/* 特定场景的图标动画效果 */

/* 导航图标 - 更明显的悬浮效果 */
.nav-icon,
.header-icon,
.menu-icon {
  &:hover {
    transform: scale(1.08);
    opacity: 0.9;
  }
}

/* 操作按钮图标 - 带阴影效果 */
.action-icon,
.edit-icon,
.delete-icon,
.save-icon {
  &:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 10%));
  }
}

/* 社交互动图标 - 弹性动画 */
.like-icon,
.share-icon,
.comment-icon {
  &:hover {
    transform: scale(1.08);
    animation: icon-bounce 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
}

@keyframes icon-bounce {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.12);
  }

  100% {
    transform: scale(1.08);
  }
}

/* 小尺寸图标 - 适度放大 */
.small-icon,
.icon-sm {
  &:hover {
    transform: scale(1.15);
  }
}

/* 大尺寸图标 - 轻微放大 */
.large-icon,
.icon-lg {
  &:hover {
    transform: scale(1.05);
  }
}

/* 圆形图标容器 - 带背景色变化 */
.icon-circle,
.rounded-icon {
  border-radius: 50%;
  padding: 8px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background-color: rgba(24, 144, 255, 10%);
    transform: scale(1.05);
  }
}

/* 方形图标容器 - 带背景色变化 */
.icon-square {
  border-radius: 4px;
  padding: 6px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background-color: rgba(24, 144, 255, 8%);
    transform: scale(1.04);
  }
}

/* 工具栏图标 - 统一的悬浮效果 */
.toolbar-icon,
.tool-icon {
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 5%);
    transform: scale(1.05);
  }

  // 暗色主题适配
  [data-theme='dark'] & {
    &:hover {
      background-color: rgba(255, 255, 255, 10%);
    }
  }
}

/* 状态图标 - 带颜色变化 */
.status-icon {
  &.success:hover {
    color: #52c41a;
    transform: scale(1.06);
  }

  &.warning:hover {
    color: #faad14;
    transform: scale(1.06);
  }

  &.error:hover {
    color: #ff4d4f;
    transform: scale(1.06);
  }

  &.info:hover {
    color: #2d8cf0;
    transform: scale(1.06);
  }
}

/* 禁用状态的图标 - 无动画 */
.icon.disabled,
.disabled svg,
[disabled] svg {
  cursor: not-allowed;
  opacity: 0.4;

  &:hover {
    transform: none !important;
    opacity: 0.4 !important;
    filter: none !important;
  }
}

/* 修复可能的选择高亮问题 */
::selection {
  background-color: rgba(45, 140, 240, 20%);
}

/* 特定组件中的图标动画 */

/* 文章卡片中的图标 */
.article-card {
  .icon,
  svg {
    &:hover {
      transform: scale(1.12);
    }
  }
}

/* 评论区域中的图标 */
.comment-item,
.comment-form {
  .icon,
  svg {
    &:hover {
      transform: scale(1.15);
    }
  }
}

/* 弹幕相关图标 */
.danmaku-input,
.danmaku-settings {
  .icon,
  svg {
    &:hover {
      transform: scale(1.1);
      filter: brightness(1.2);
    }
  }
}

/* 用户头像和相关图标 */
.user-avatar,
.user-profile {
  .icon,
  svg {
    &:hover {
      transform: scale(1.08);
    }
  }
}

/* 通知相关图标 */
.notification-item,
.notification-modal {
  .icon,
  svg {
    &:hover {
      transform: scale(1.12);
    }
  }
}

/* 主题切换图标 - 特殊旋转效果 */
.theme-toggle {
  .icon,
  svg {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: scale(1.1) rotate(15deg);
    }
  }
}

/* TipTap 编辑器工具栏图标 */
.tiptap-toolbar,
.editor-toolbar {
  .icon,
  svg,
  button {
    &:hover {
      transform: scale(1.08);
      background-color: rgba(24, 144, 255, 8%);
    }
  }
}

/* 模态框和对话框中的图标 */
.modal-header,
.dialog-header {
  .close-icon,
  .icon {
    &:hover {
      transform: scale(1.2);
      color: #ff4d4f;
    }
  }
}

/* 加载状态图标 - 旋转动画 */
.loading-icon {
  animation: spin 1s linear infinite;

  &:hover {
    transform: none !important;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 响应式图标动画 - 移动端优化 */
@media (width <= 768px) {
  .icon,
  svg,
  [class*='icon-'] {
    &:hover {
      // 移动端减少动画幅度
      transform: scale(1.05) !important;
    }
  }

  // 触摸设备上的点击反馈
  @media (hover: none) and (pointer: coarse) {
    .icon,
    svg,
    [class*='icon-'] {
      &:active {
        transform: scale(1);
        opacity: 0.8;
      }
    }
  }
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
  .icon,
  svg,
  [class*='icon-'] {
    &:hover {
      outline: 2px solid currentcolor;
      outline-offset: 2px;
    }
  }
}

/* 减少动画偏好适配 */
@media (prefers-reduced-motion: reduce) {
  .icon,
  svg,
  [class*='icon-'] {
    transition: none !important;
    animation: none !important;

    &:hover {
      transform: none !important;
      opacity: 0.8;
    }
  }
}

/* 确保图标在所有浏览器中都不会被选中 */
@supports (-webkit-user-select: none) {
  svg,
  .icon,
  [class*='icon-'],
  .scope-icon-wrapper {
    user-select: none;
  }
}

@supports (-moz-user-select: none) {
  svg,
  .icon,
  [class*='icon-'],
  .scope-icon-wrapper {
    user-select: none;
  }
}
