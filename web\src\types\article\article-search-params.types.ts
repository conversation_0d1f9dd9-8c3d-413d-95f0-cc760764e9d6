/**
 * 文章搜索请求参数类型定义
 * 用于搜索、筛选文章的请求参数
 */
export interface ArticleSearchParams {
  /** 搜索关键词 */
  searchKey?: string
  /** 是否只查询自己的文章 */
  owner?: boolean
  /** 是否包含互动信息 */
  interaction?: boolean
  /** 是否只查询收藏的文章 */
  favorite?: boolean
  /** 按标签筛选 */
  tag?: string
  /** 文章ID */
  id?: string | number
  /** 加载数量限制 */
  loadSize?: number
  /** 其他扩展参数 */
  [key: string]: string | number | boolean | null | undefined
}
