import { type Ref } from 'vue'

import fileApi from '@/api/file'
import { TOPIC_COMMENTS } from '@/constants/comment/destination.constants'
import { COMMENT_RECEIVED } from '@/constants/comment/frequency-key.constants'
import router from '@/router'
import type { Comment } from '@/types/comment/comment.types'
import type { Notification } from '@/types/notification/notification.types'
import Json from '@/utils/data/json'
import logger from '@/utils/log/log'
import webSocket from '@/utils/network/web-socket'
import frequencyLimit from '@/utils/performance/frequency-limit'
import tiptap from '@/utils/tiptap/tiptap'

// 限制弹幕列表最大长度
const MAX_DANMAKU_COUNT = 200

/**
 * 评论弹幕操作组合式函数返回值类型
 */
export interface UseCommentDanmakuOperationsReturn {
  /** 清理弹幕列表 */
  clearDanmaku: () => void
  /** 添加评论到列表 */
  addCommentList: (list: Comment[]) => void
  /** 处理弹幕点击 */
  handleDanmuClick: (danmu: Comment) => void
  /** 订阅评论 */
  subscribeComment: () => void
  /** 取消订阅评论 */
  unsubscribeComment: () => void
  /** 处理图片预览开始事件 */
  handleImagePreviewOpen: () => void
  /** 处理图片预览结束事件 */
  handleImagePreviewClose: () => void
  /** 调整大小 */
  resize: () => void
  /** 播放 */
  play: () => void
  /** 暂停 */
  pause: () => void
}

/**
 * 评论弹幕操作组合式函数
 * 提供弹幕组件的操作逻辑
 */
export function useCommentDanmakuOperations(
  commentList: Ref<Comment[]>,
  danmakuRef: Ref<any>,
  danmakuConfig: any,
  isSubscribed: Ref<boolean>,
): UseCommentDanmakuOperationsReturn {
  // 获取资源URL
  const getResourceURL = (uri: string): string => {
    return fileApi.getResourceURL(uri)
  }

  // 清理弹幕列表
  const clearDanmaku = () => {
    commentList.value = []
    if (danmakuRef.value) {
      danmakuRef.value.reset()
    }
  }

  // 处理弹幕点击
  const handleDanmuClick = (danmu: Comment) => {
    // 使用 window.open 在新标签页中打开并自动切换过去
    const route = router.resolve({
      name: 'Article',
      params: { articleId: danmu.articleId, commentId: danmu.id },
    })
    const newWindow = window.open(route.href, '_blank')
    if (newWindow) {
      newWindow.focus()
    }
  }

  // 添加评论到列表
  const addCommentList = (list: Comment[]) => {
    logger.debug('addCommentList 被调用:', {
      inputList: list,
      inputLength: list?.length || 0,
      currentListLength: commentList.value.length,
    })

    // 检查输入参数
    if (!list || !Array.isArray(list)) {
      logger.warn('addCommentList 收到无效的评论列表:', list)
      return
    }

    // 用Set来跟踪已经显示的评论ID，避免重复
    const existingCommentIds = new Set(commentList.value.map((comment) => comment.id))

    // 过滤掉已经存在的评论
    const newComments = list.filter((comment) => !existingCommentIds.has(comment.id))

    logger.debug('过滤后的新评论:', {
      newCommentsLength: newComments.length,
      existingIds: Array.from(existingCommentIds),
    })

    // 如果没有新评论，直接返回
    if (newComments.length === 0) {
      logger.debug('没有新评论需要添加')
      return
    }

    try {
      const processedComments = newComments.map((comment) => ({
        ...comment,
        publisherAvatar: getResourceURL(comment.publisherAvatar || ''),
        // 确保内容字段存在，同时安全地处理JSON转换
        content: comment.content || '',
        contentObj: comment.content
          ? tiptap.toJsonObject(comment.content)
          : {
              type: 'doc',
              content: [{ type: 'paragraph', content: [] }],
            },
      }))

      // 分批添加弹幕，避免一次性添加太多造成卡顿
      const batchSize = 10
      for (let i = 0; i < processedComments.length; i += batchSize) {
        const batch = processedComments.slice(i, i + batchSize)
        setTimeout(() => {
          commentList.value.push(...batch)
        }, i * 50) // 每批次间隔50ms
      }

      // 限制列表长度
      if (commentList.value.length > MAX_DANMAKU_COUNT) {
        commentList.value = commentList.value.slice(-MAX_DANMAKU_COUNT)
      }

      // 确保danmakuRef存在
      if (danmakuRef.value) {
        danmakuRef.value.play()
      }
    } catch (error) {
      logger.error('处理评论列表时出错:', error as Error)
    }
  }

  // 处理评论消息接收
  const handleCommentMessageReceived = (newMessage: string) => {
    const messageObj = Json.parse(newMessage) as Notification
    if (!messageObj) return

    frequencyLimit.debounce(COMMENT_RECEIVED + messageObj.commentId, () => {
      // 检查评论是否已经存在
      const isExisting = commentList.value.some((comment) => comment.id === messageObj.commentId)
      if (isExisting) return

      // 检查必要字段是否存在
      if (!messageObj.commentId || !messageObj.articleId || !messageObj.publisher) {
        logger.warn('消息缺少必要字段:', messageObj)
        return
      }

      // 预处理消息内容
      try {
        // 确保消息内容是有效的JSON或字符串
        const processedMessage = {
          id: messageObj.commentId,
          articleId: messageObj.articleId,
          publisher: messageObj.publisher,
          publisherAvatar: getResourceURL(messageObj.publisherAvatar || ''),
          content: messageObj.content || '',
          // 只有当content存在且不为空时才调用toJsonObject
          contentObj: messageObj.content
            ? tiptap.toJsonObject(messageObj.content)
            : {
                type: 'doc',
                content: [{ type: 'paragraph', content: [] }],
              },
        } as Comment

        // 确保danmakuRef已初始化
        if (danmakuRef.value) {
          danmakuRef.value.insert(processedMessage)
        }
      } catch (error) {
        logger.error('处理弹幕消息时出错:', error as Error)
      }
    })
  }

  // 订阅评论
  const subscribeComment = () => {
    if (!isSubscribed.value) {
      webSocket.subscribe(TOPIC_COMMENTS, handleCommentMessageReceived)
      isSubscribed.value = true
    }
  }

  // 取消订阅评论
  const unsubscribeComment = () => {
    if (isSubscribed.value) {
      webSocket.unsubscribe(TOPIC_COMMENTS)
      isSubscribed.value = false
    }
  }

  // 处理图片预览开始事件，暂停弹幕
  const handleImagePreviewOpen = () => {
    if (danmakuRef.value) {
      danmakuRef.value.pause()
    }
  }

  // 处理图片预览结束事件，恢复弹幕
  const handleImagePreviewClose = () => {
    if (danmakuRef.value && !danmakuConfig.pause) {
      danmakuRef.value.play()
    }
  }

  // 调整大小
  const resize = () => {
    if (danmakuRef.value) {
      danmakuRef.value.resize()
    }
  }

  // 弹幕播放控制
  const play = () => {
    if (danmakuRef.value) {
      danmakuRef.value.play()
    }
  }

  const pause = () => {
    if (danmakuRef.value) {
      danmakuRef.value.pause()
    }
  }

  return {
    clearDanmaku,
    addCommentList,
    handleDanmuClick,
    subscribeComment,
    unsubscribeComment,
    handleImagePreviewOpen,
    handleImagePreviewClose,
    resize,
    play,
    pause,
  }
}
