/**
 * 拖拽事件处理工具函数
 * 提供统一的拖拽事件管理，支持鼠标和触摸事件
 */

import logger from '@/utils/log/log'

/**
 * 拖拽事件处理器接口
 */
interface DragEventHandlers {
  /** 拖拽移动事件处理器 */
  onMove: (event: MouseEvent | TouchEvent) => void
  /** 拖拽结束事件处理器 */
  onEnd: () => void
}

/**
 * 设置拖拽事件监听器
 * @param handlers 事件处理器对象
 * @returns 清理函数，用于移除事件监听器
 * @example
 * ```typescript
 * const cleanup = setupDragEvents({
 *   onMove: (event) => console.log('拖拽中', event),
 *   onEnd: () => console.log('拖拽结束')
 * })
 *
 * // 在组件卸载时调用清理函数
 * cleanup()
 * ```
 */
export function setupDragEvents(handlers: DragEventHandlers): () => void {
  const { onMove, onEnd } = handlers

  // 创建事件处理器
  const moveHandler = (event: MouseEvent | TouchEvent) => {
    onMove(event)
  }

  const endHandler = () => {
    onEnd()
    cleanup()
  }

  // 添加事件监听器
  document.addEventListener('mousemove', moveHandler, { passive: false })
  document.addEventListener('mouseup', endHandler)
  document.addEventListener('touchmove', moveHandler, { passive: false })
  document.addEventListener('touchend', endHandler)
  document.addEventListener('touchcancel', endHandler)

  logger.debug('事件监听器已添加')

  // 返回清理函数
  const cleanup = () => {
    document.removeEventListener('mousemove', moveHandler)
    document.removeEventListener('mouseup', endHandler)
    document.removeEventListener('touchmove', moveHandler)
    document.removeEventListener('touchend', endHandler)
    document.removeEventListener('touchcancel', endHandler)
    logger.debug('事件监听器已清理')
  }

  return cleanup
}
