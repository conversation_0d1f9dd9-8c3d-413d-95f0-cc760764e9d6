package com.shenmo.wen.common.util;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.shenmo.wen.common.constant.StringConstant;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.AesKeyStrength;
import net.lingala.zip4j.model.enums.EncryptionMethod;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

/**
 * Zip压缩工具类
 *
 * <AUTHOR>
 */
public abstract class ZipUtils {
    public static final Charset GBK = Charset.forName("GBK");

    public static final String ZIP_EXTENSION = ".zip";
    private static final String TEMP = "temp";

    /**
     * zip文件解压，默认解压到压缩文件当前目录的同名文件夹
     * 如果默认解压文件夹名和压缩文件名相同（压缩文件无后缀），则解压到随机文件目录中
     *
     * @param zipFile zip文件
     * @return 解压目录
     */
    public static File unzip(File zipFile) throws IOException {
        String baseName = FilenameUtils.getBaseName(zipFile.getName());
        if (Objects.equals(baseName, zipFile.getName())) {
            baseName = zipFile.getName() + StringConstant.UNDERSCORE + IdWorker.getIdStr();
        }
        File unzipDirectory = FileUtils.getFile(Optional.ofNullable(zipFile.getParent()).orElse(StringConstant.EMPTY), baseName);

        return unzip(zipFile, unzipDirectory);
    }

    /**
     * zip文件解压
     *
     * @param zipFile        zip文件
     * @param unzipDirectory 解压目录
     * @return 解压目录
     */
    public static File unzip(File zipFile, File unzipDirectory) throws IOException {
        return unzip(zipFile, unzipDirectory, GBK);
    }

    /**
     * zip文件解压
     *
     * @param inputStream    zip文件输入流
     * @param unzipDirectory 解压目录
     * @return 解压目录
     */
    public static File unzip(InputStream inputStream, File unzipDirectory) throws IOException {
        return unzip(inputStream, unzipDirectory, GBK);
    }

    /**
     * zip文件解压
     *
     * @param inputStream    zip文件输入流
     * @param unzipDirectory 解压目录
     * @param charset        字符集
     * @return 解压目录
     */
    public static File unzip(InputStream inputStream, File unzipDirectory, Charset charset) throws IOException {
        AssertUtils.isNotNull(inputStream, ThrowUtils.getThrow().internalServerError("inputStream must not be null"));

        File tempZipFile = new File(TEMP + StringConstant.SLASH + IdWorker.getIdStr() + ZIP_EXTENSION);
        FileUtils.copyInputStreamToFile(inputStream, tempZipFile);

        try {
            return unzip(tempZipFile, unzipDirectory, charset);
        } finally {
            FileUtils.deleteQuietly(tempZipFile);
        }
    }

    /**
     * zip文件解压
     *
     * @param zipFile        zip文件
     * @param unzipDirectory 解压目录
     * @param charset        字符集
     * @return 解压目录
     */
    public static File unzip(File zipFile, File unzipDirectory, Charset charset) throws IOException {
        AssertUtils.isNotNull(zipFile, ThrowUtils.getThrow().internalServerError("zipFile must not be null"));
        AssertUtils.isNotNull(unzipDirectory, ThrowUtils.getThrow().internalServerError("unzipDirectory must not be null"));
        AssertUtils.isNotNull(charset, ThrowUtils.getThrow().internalServerError("charset must not be null"));

        // 判断压缩文件是否存在
        if (!zipFile.exists() || !zipFile.isFile()) {
            throw ThrowUtils.getThrow().internalServerError("压缩文件必须存在并且是一个文件");
        }

        // 判断解压目录是否存在
        if (unzipDirectory.exists()) {
            if (unzipDirectory.isFile()) {
                throw ThrowUtils.getThrow().internalServerError("解压目录不能是一个文件");
            }
        } else {
            FileUtils.forceMkdir(unzipDirectory);
        }

        // 解压文件
        try (ZipFile resultFile = new ZipFile(zipFile, charset)) {
            final Enumeration<? extends ZipEntry> entries = resultFile.entries();

            while (entries.hasMoreElements()) {
                final ZipEntry zipEntry = entries.nextElement();

                // 如果是文件夹
                if (zipEntry.isDirectory()) {
                    final File directory = new File(unzipDirectory, zipEntry.getName());
                    FileUtils.forceMkdir(directory);
                } else {
                    final File file = new File(unzipDirectory, zipEntry.getName());
                    if (!file.getParentFile().exists()) {
                        FileUtils.forceMkdirParent(file);
                    }

                    FileUtils.copyInputStreamToFile(resultFile.getInputStream(zipEntry), file);
                }
            }

            return unzipDirectory;
        }
    }

    /**
     * zip文件压缩，默认压缩到待压缩文件当前目录的同名zip压缩包
     *
     * @param unzipFile 待压缩文件
     * @return 压缩文件
     * @throws IOException IOException
     */
    public static File zip(File unzipFile) throws IOException {
        String baseName = FilenameUtils.getBaseName(unzipFile.getName()) + ZIP_EXTENSION;
        File zipFile = FileUtils.getFile(unzipFile.getParent(), baseName);

        return zip(unzipFile, zipFile);
    }

    /**
     * zip文件压缩
     *
     * @param unzipFile 待压缩文件
     * @param zipFile   压缩文件
     * @return 压缩文件
     * @throws IOException IOException
     */
    public static File zip(File unzipFile, File zipFile) throws IOException {
        return zip(unzipFile, zipFile, StandardCharsets.UTF_8);
    }

    /**
     * zip文件压缩
     * 如果unzipFile为一个文件，则直接其添加到压缩包中
     * 如果unzipFile为一个目录，则将该目录下所有文件和目录添加到压缩包中
     *
     * @param unzipFile 待压缩文件
     * @param zipFile   压缩文件
     * @return 压缩文件
     * @throws IOException IOException
     */
    public static File zip(File unzipFile, File zipFile, Charset charset) throws IOException {
        AssertUtils.isNotNull(unzipFile, ThrowUtils.getThrow().internalServerError("unzipFile must not be null"));
        AssertUtils.isNotNull(charset, ThrowUtils.getThrow().internalServerError("charset must not be null"));

        // 判断待压缩文件是否存在
        if (!unzipFile.exists()) {
            throw ThrowUtils.getThrow().internalServerError("待压缩文件不存在");
        }

        // 判断压缩文件父路径是否存在
        if (!zipFile.getParentFile().exists()) {
            FileUtils.forceMkdir(zipFile.getParentFile());
        }

        // 判断压缩文件是否存在
        if (zipFile.exists()) {
            FileUtils.deleteQuietly(zipFile);
        }

        // 获取压缩文件列表
        List<File> fileList = new ArrayList<>();
        if (unzipFile.isFile()) {
            fileList.add(unzipFile);
        } else {
            fileList.addAll(FileUtils.listFiles(unzipFile, null, true));
        }

        // 压缩
        try (FileOutputStream fileOutputStream = new FileOutputStream(zipFile);
             ZipOutputStream zipOutputStream = new ZipOutputStream(fileOutputStream, charset)) {
            for (File file : fileList) {
                final String name = StringUtils.removeStart(file.getPath(), unzipFile.getPath());
                zipOutputStream.putNextEntry(new ZipEntry(name));
                IOUtils.copy(FileUtils.openInputStream(file), zipOutputStream);
            }
            return zipFile;
        }
    }

    /**
     * 打包指定的目录
     *
     * @param catalogPath 待打包的目录
     * @param zipFilePath 存储压缩包的路径，包含文件名
     * @throws Exception 如 catalogPath 非目录，则抛出此异常
     */
    public static void zipUsePass(String catalogPath, String zipFilePath, String password) throws Exception {
        ZipParameters zipParameters = new ZipParameters();
        zipParameters.setEncryptFiles(true);
        zipParameters.setEncryptionMethod(EncryptionMethod.AES);
        zipParameters.setAesKeyStrength(AesKeyStrength.KEY_STRENGTH_256);
        net.lingala.zip4j.ZipFile zipFile = new net.lingala.zip4j.ZipFile(zipFilePath, password.toCharArray());
        List<File> filesToAdd = FileUtils.listFiles(FileUtils.getFile(catalogPath), null, true).stream().toList();
        zipFile.addFiles(filesToAdd, zipParameters);
        zipFile.close();
    }

    /**
     * 解压带密码的压缩包
     *
     * @param zipFilePath  待解压的压缩包绝对路径
     * @param unzipCatalog 解压后的目录
     * @param password     压缩包密码
     * @throws Exception 解压异常
     */
    public static void unzip(String zipFilePath, String unzipCatalog, String password) throws Exception {

        final net.lingala.zip4j.ZipFile zipFile = new net.lingala.zip4j.ZipFile(zipFilePath, password.toCharArray());
        zipFile.extractAll(unzipCatalog);
        zipFile.close();
    }
}
