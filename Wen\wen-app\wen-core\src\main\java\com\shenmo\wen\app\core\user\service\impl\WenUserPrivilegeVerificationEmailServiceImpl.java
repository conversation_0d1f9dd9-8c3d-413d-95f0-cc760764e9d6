package com.shenmo.wen.app.core.user.service.impl;

import com.shenmo.wen.app.core.user.constant.PrivilegeVerificationConstant;
import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilegeTemplate;
import com.shenmo.wen.app.core.user.service.WenUserPrivilegeVerificationEmailService;
import com.shenmo.wen.common.util.HtmlTemplateUtils;
import com.shenmo.wen.modules.user.config.properties.EmailProperties;
import com.shenmo.wen.modules.user.pojo.entity.WenUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

/**
 * 特权验证邮件服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WenUserPrivilegeVerificationEmailServiceImpl implements WenUserPrivilegeVerificationEmailService {

    private final JavaMailSender mailSender;
    private final EmailProperties emailProperties;

    @Override
    public boolean sendSmsVerificationEmail(WenUser userA, WenUser userB,
            WenUserPrivilegeTemplate template, String pageUrl) {
        try {
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

            helper.setFrom(emailProperties.getFrom(), emailProperties.getPersonal());
            helper.setTo(userB.getEmail());

            // 设置邮件主题
            String subject = String.format(PrivilegeVerificationConstant.EMAIL_SUBJECT_TEMPLATE,
                    emailProperties.getSubjectPrefix(), template.getName());
            helper.setSubject(subject);

            // 构建邮件内容
            String htmlContent = buildSmsVerificationEmailContent(userA, userB, template, pageUrl);
            helper.setText(htmlContent, true);

            mailSender.send(mimeMessage);
            log.info("短信验证邮件发送成功，收件人: {}, 特权: {}", userB.getEmail(), template.getName());
            return true;

        } catch (MessagingException e) {
            log.error("短信验证邮件发送失败，收件人: {}, 特权: {}", userB.getEmail(), template.getName(), e);
            return false;
        } catch (UnsupportedEncodingException e) {
            log.error("短信验证邮件发送失败，收件人: {}, 特权: {}", userB.getEmail(), template.getName(), e);
            return false;
        }
    }

    @Override
    public boolean sendQrCodeVerificationEmail(WenUser userA, WenUser userB,
            WenUserPrivilegeTemplate template, String pageUrl) {
        try {
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

            helper.setFrom(emailProperties.getFrom(), emailProperties.getPersonal());
            helper.setTo(userB.getEmail());

            // 设置邮件主题
            String subject = String.format(PrivilegeVerificationConstant.EMAIL_SUBJECT_TEMPLATE,
                    emailProperties.getSubjectPrefix(), template.getName());
            helper.setSubject(subject);

            // 构建邮件内容
            String htmlContent = buildQrCodeVerificationEmailContent(userA, userB, template, pageUrl);
            helper.setText(htmlContent, true);

            mailSender.send(mimeMessage);
            log.info("二维码验证邮件发送成功，收件人: {}, 特权: {}", userB.getEmail(), template.getName());
            return true;

        } catch (MessagingException e) {
            log.error("二维码验证邮件发送失败，收件人: {}, 特权: {}", userB.getEmail(), template.getName(), e);
            return false;
        } catch (UnsupportedEncodingException e) {
            log.error("二维码验证邮件发送失败，收件人: {}, 特权: {}", userB.getEmail(), template.getName(), e);
            return false;
        }
    }

    /**
     * 构建短信验证邮件内容
     */
    private String buildSmsVerificationEmailContent(WenUser userA, WenUser userB,
            WenUserPrivilegeTemplate template, String pageUrl) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("subjectPrefix", emailProperties.getSubjectPrefix());
        variables.put("userAUsername", userA.getUsername());
        variables.put("templateName", template.getName());
        variables.put("pageUrl", pageUrl);

        return HtmlTemplateUtils.processTemplate(HtmlTemplateUtils.Templates.PRIVILEGE_VERIFICATION_SMS, variables);
    }

    /**
     * 构建二维码验证邮件内容
     */
    private String buildQrCodeVerificationEmailContent(WenUser userA, WenUser userB,
            WenUserPrivilegeTemplate template, String pageUrl) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("subjectPrefix", emailProperties.getSubjectPrefix());
        variables.put("emailContent", String.format(PrivilegeVerificationConstant.QR_CODE_EMAIL_CONTENT_TEMPLATE,
                userA.getUsername(), template.getName()));
        variables.put("templateName", template.getName());
        variables.put("pageUrl", pageUrl);

        return HtmlTemplateUtils.processTemplate(HtmlTemplateUtils.Templates.PRIVILEGE_VERIFICATION_QRCODE, variables);
    }
}
