import { ref } from 'vue'

import { addTouchEventListeners } from '../../../events/PassiveEventHandlers'

import type { Editor } from '@tiptap/vue-3'

/**
 * 图片调整大小配置接口
 */
export interface ImageResizeOptions {
  editor: Editor
  getPos: () => number
  imageElement: HTMLImageElement
}

/**
 * 图片调整大小组合式函数
 * 处理图片的拖拽调整大小功能
 */
export function useImageResize(options: ImageResizeOptions) {
  const { editor, getPos, imageElement } = options

  // 调整大小状态
  const isResizing = ref(false)
  const currentHandle = ref<string | null>(null)

  // 图片尺寸状态
  const startX = ref(0)
  const startY = ref(0)
  const startWidth = ref(0)
  const startHeight = ref(0)
  const currentWidth = ref(0)
  const currentHeight = ref(0)
  const aspectRatio = ref(1)

  // 存储触摸事件清理函数
  let currentTouchCleanup: (() => void) | null = null

  // 调整大小的控制点
  const handles = {
    'top-left': null,
    'top-right': null,
    'bottom-left': null,
    'bottom-right': null,
    top: null,
    right: null,
    bottom: null,
    left: null,
  } as Record<string, null>

  // 判断是否为中心控制点
  const isCenterHandle = (position: string) => {
    return ['top', 'right', 'bottom', 'left'].includes(position)
  }

  /**
   * 开始调整大小
   */
  const handleResizeStart = (e: MouseEvent, position: string) => {
    // 阻止事件传播，防止编辑器处理这个事件
    e.preventDefault()
    e.stopPropagation()

    // 检查编辑模式和位置函数
    if (!editor.isEditable) return
    if (typeof getPos !== 'function') return

    // 确保图片节点被选中
    const pos = getPos()
    editor.commands.setNodeSelection(pos)

    // 记录开始调整的状态
    isResizing.value = true
    currentHandle.value = position

    // 获取图片当前尺寸
    if (imageElement) {
      const rect = imageElement.getBoundingClientRect()
      startWidth.value = rect.width
      startHeight.value = rect.height
      currentWidth.value = rect.width
      currentHeight.value = rect.height
      aspectRatio.value = startWidth.value / startHeight.value
    }

    // 记录鼠标起始位置
    startX.value = e.clientX
    startY.value = e.clientY

    // 添加全局事件监听
    document.addEventListener('mousemove', handleResize)
    document.addEventListener('mouseup', handleResizeEnd)
  }

  /**
   * 处理触摸开始
   */
  const handleTouchStart = (e: TouchEvent, position: string) => {
    if (!e.touches || e.touches.length === 0) return

    const touch = e.touches[0]

    // 创建类似鼠标事件的对象
    const touchEvent = {
      clientX: touch.clientX,
      clientY: touch.clientY,
      preventDefault: () => {},
      stopPropagation: () => {},
    } as unknown as MouseEvent

    handleResizeStart(touchEvent, position)

    // 在下一帧阻止滚动，而不是直接阻止触摸事件
    requestAnimationFrame(() => {
      document.body.style.overflow = 'hidden'
    })

    // 使用被动事件监听器工具添加触摸事件
    currentTouchCleanup = addTouchEventListeners(document, {
      touchmove: handleTouchResize as EventListener,
      touchend: handleTouchEnd as EventListener,
      touchcancel: handleTouchEnd as EventListener,
    })
  }

  /**
   * 处理调整大小 - 立即更新提供最佳跟随体验
   */
  const handleResize = (e: MouseEvent) => {
    if (!isResizing.value || !currentHandle.value) return

    // 阻止事件传播
    e.preventDefault()

    // 立即计算鼠标移动距离
    const deltaX = e.clientX - startX.value
    const deltaY = e.clientY - startY.value

    // 获取控制点位置
    const position = currentHandle.value

    // 计算新的宽度和高度
    let newWidth = startWidth.value
    let newHeight = startHeight.value

    // 根据控制点位置和修饰键调整尺寸
    const keepRatio = e.shiftKey // Shift键保持宽高比
    const freeTransform = e.altKey // Alt键自由变换

    // 根据控制点位置调整尺寸
    switch (position) {
      case 'top-left':
        newWidth = startWidth.value - deltaX
        newHeight = startHeight.value - deltaY
        break
      case 'top-right':
        newWidth = startWidth.value + deltaX
        newHeight = startHeight.value - deltaY
        break
      case 'bottom-left':
        newWidth = startWidth.value - deltaX
        newHeight = startHeight.value + deltaY
        break
      case 'bottom-right':
        newWidth = startWidth.value + deltaX
        newHeight = startHeight.value + deltaY
        break
      case 'top':
        newHeight = startHeight.value - deltaY
        if (keepRatio) newWidth = newHeight * aspectRatio.value
        break
      case 'right':
        newWidth = startWidth.value + deltaX
        if (keepRatio) newHeight = newWidth / aspectRatio.value
        break
      case 'bottom':
        newHeight = startHeight.value + deltaY
        if (keepRatio) newWidth = newHeight * aspectRatio.value
        break
      case 'left':
        newWidth = startWidth.value - deltaX
        if (keepRatio) newHeight = newWidth / aspectRatio.value
        break
    }

    // 如果按住Shift键且不是中间控制点，保持宽高比
    if (keepRatio && !isCenterHandle(position) && !freeTransform) {
      // 根据控制点位置决定是以宽度还是高度为基准
      if (
        position === 'top-left' ||
        position === 'top-right' ||
        position === 'bottom-left' ||
        position === 'bottom-right'
      ) {
        // 对于角控制点，使用移动距离较大的方向作为基准
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
          newHeight = newWidth / aspectRatio.value
        } else {
          newWidth = newHeight * aspectRatio.value
        }
      }
    }

    // 确保尺寸不小于最小值
    const minSize = 20
    newWidth = Math.max(newWidth, minSize)
    newHeight = Math.max(newHeight, minSize)

    // 立即更新当前尺寸
    currentWidth.value = newWidth
    currentHeight.value = newHeight

    // 立即应用新尺寸
    if (imageElement) {
      imageElement.style.width = `${newWidth}px`
      imageElement.style.height = `${newHeight}px`
    }
  }

  /**
   * 处理触摸调整大小
   */
  const handleTouchResize = (e: TouchEvent) => {
    if (!isResizing.value || !currentHandle.value) return
    if (!e.touches || e.touches.length === 0) return

    const touch = e.touches[0]

    // 检测触摸点数量
    const touchCount = e.touches.length

    // 创建类似鼠标事件的对象
    const touchEvent = {
      clientX: touch.clientX,
      clientY: touch.clientY,
      preventDefault: () => {},
      // 两指触摸模拟按住Alt键（自由调整）
      altKey: touchCount === 2,
      // 三指触摸模拟按住Shift键（保持比例）
      shiftKey: touchCount === 3,
    } as unknown as MouseEvent

    handleResize(touchEvent)
  }

  /**
   * 处理触摸结束
   */
  const handleTouchEnd = () => {
    handleResizeEnd()
  }

  /**
   * 结束调整大小
   */
  const handleResizeEnd = () => {
    if (!isResizing.value) return

    // 移除全局事件监听
    document.removeEventListener('mousemove', handleResize)
    document.removeEventListener('mouseup', handleResizeEnd)

    // 清理触摸事件监听器
    if (currentTouchCleanup) {
      currentTouchCleanup()
      currentTouchCleanup = null
    }

    // 恢复页面滚动
    document.body.style.overflow = ''

    // 重置调整状态
    isResizing.value = false
    currentHandle.value = null

    // 更新节点属性
    if (typeof getPos === 'function' && imageElement) {
      const width = Math.round(currentWidth.value)
      const height = Math.round(currentHeight.value)

      editor.commands.updateAttributes('image', {
        width: `${width}px`,
        height: `${height}px`,
      })
    }
  }

  return {
    // 状态
    isResizing,
    currentWidth,
    currentHeight,
    handles,

    // 方法
    handleResizeStart,
    handleTouchStart,
    isCenterHandle,
  }
}
