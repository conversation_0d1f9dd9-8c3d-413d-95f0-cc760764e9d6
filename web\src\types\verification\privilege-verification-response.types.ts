/**
 * 用户特权验证响应类型定义
 */
export interface PrivilegeVerificationResponse {
  /** 验证流程ID */
  id: number

  /** 申请用户ID */
  userId: number

  /** 申请的特权ID */
  privilegeId: number

  /** 特权名称 */
  privilegeName: string

  /** 验证类型：0-短信验证，1-二维码验证 */
  verificationType: 0 | 1

  /** 当前步骤：1,2,3 */
  currentStep: 1 | 2 | 3

  /** 状态：0-进行中，1-成功，2-失败，3-超时 */
  status: 0 | 1 | 2 | 3

  /** 验证页面URL */
  pageUrl: string

  /** 流程过期时间 */
  expireTime: number

  /** 创建时间 */
  ctTm: number
}
