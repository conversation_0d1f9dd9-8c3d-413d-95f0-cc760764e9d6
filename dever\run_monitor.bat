@echo off
echo Starting Cursor Augment Code Monitor Mode...
echo This will check every minute for button status
echo Press Ctrl+C to stop monitoring
echo.

python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found. Please install Python 3.7+
    pause
    exit /b 1
)

if not exist "templates\send_button_normal.png" (
    echo Warning: Missing normal button template
    echo Please create template files first
    echo Run: test_buttons.bat
    pause
    exit /b 0
)

if not exist "templates\send_button_pause.png" (
    echo Warning: Missing pause button template
    echo Please create template files first
    echo Run: test_buttons.bat
    pause
    exit /b 0
)

echo Starting monitor mode...
echo.
python cursor_augment_automation.py

pause
