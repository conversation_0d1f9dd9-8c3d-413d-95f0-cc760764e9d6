/**
 * 评论列表项类型定义
 * 用于评论列表展示的评论基本信息，与后端WenCommentResp保持一致
 */
export interface CommentListItem {
  /** 评论ID */
  id: string
  /** 评论内容 */
  content: string
  /** 目标文章ID */
  articleId: string
  /** 父评论ID */
  parentCommentId?: string
  /** 评论发布人 */
  publisher: string
  /** 发布人头像URL */
  publisherAvatar: string
  /** 发布人职业 */
  publisherJob: string
  /** 是否评论拥有者 */
  isOwner: boolean
  /** 评论发布时的IP地址 */
  ipLocation: string
  /** 发布时间 */
  publishedAt: string
  /** 点赞数 */
  likeCount: number
  /** 是否已点赞 */
  isLike: boolean
  /** 点踩数 */
  dislikeCount: number
  /** 是否已点踩 */
  isDislike: boolean
  /** 回复数量 */
  replyCount: number
  /** 收藏数 */
  favoriteCount: number
  /** 是否已收藏 */
  isFavorite: boolean
  /** 是否置顶 */
  fixed: boolean
}
