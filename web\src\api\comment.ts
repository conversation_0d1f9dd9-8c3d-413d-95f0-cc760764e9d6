import { type ResponseData } from '@/types/api/response-data.types'
import type { CommentListItem } from '@/types/comment/comment-list-item.types'
import type { CommentLocationResponse } from '@/types/comment/comment-location-response.types'
import type { CommentSaveParams } from '@/types/comment/comment-save-params.types'
import type { CommentSearchParams } from '@/types/comment/comment-search-params.types'
import type { CommentSearchResponse } from '@/types/comment/comment-search-response.types'
import api from '@/utils/api/api'

import type { AxiosRequestConfig } from 'axios'

/** 评论保存响应数据类型 */
interface CommentSaveResponse {
  /** 评论ID */
  id: string
  /** 保存时间 */
  savedAt: string
}

/**
 * 评论相关API接口
 * 提供评论的查询、发布、定位等功能
 */
const commentApi = {
  /** API基础路径 */
  URL: '/core/comments',

  /**
   * 搜索评论
   * 根据指定条件搜索评论列表
   * @param params 搜索参数，包含文章ID、分页信息等
   * @param signal 请求取消信号，用于中断请求
   * @returns 返回匹配的评论列表和分页信息
   */
  search: async (
    params: CommentSearchParams,
    signal?: AbortSignal,
  ): Promise<ResponseData<CommentSearchResponse>> => {
    const config: AxiosRequestConfig = { signal }
    const response = await api.get<ResponseData<CommentSearchResponse>>(
      commentApi.URL + '/search',
      params,
      config,
    )
    return response.data
  },

  /**
   * 定位评论位置
   * 获取指定评论在评论列表中的位置信息
   * @param id 评论ID
   * @returns 返回评论的页码和位置索引
   */
  location: async (id: string): Promise<ResponseData<CommentLocationResponse>> => {
    const response = await api.get<ResponseData<CommentLocationResponse>>(
      commentApi.URL + '/' + id + '/location',
    )
    return response.data
  },

  /**
   * 根据ID加载评论
   * 获取指定ID的评论详细信息
   * @param id 评论ID
   * @returns 返回评论详细信息
   */
  loadById: async (id: string): Promise<ResponseData<CommentListItem>> => {
    const response = await api.get<ResponseData<CommentListItem>>(commentApi.URL + '/' + id)
    return response.data
  },

  /**
   * 加载评论（通用）
   * 根据URL参数加载评论数据
   * @param params URL参数字符串
   * @returns 返回评论数据
   */
  load: async (params: string): Promise<ResponseData<CommentSearchResponse>> => {
    const response = await api.get<ResponseData<CommentSearchResponse>>(commentApi.URL + params)
    return response.data
  },

  /**
   * 保存评论
   * 发布新的评论或回复
   * @param params 评论参数，包含内容、文章ID等
   * @returns 返回保存成功的评论ID和时间
   */
  save: async (params: CommentSaveParams): Promise<ResponseData<CommentSaveResponse>> => {
    const response = await api.post<ResponseData<CommentSaveResponse>>(commentApi.URL, params)
    return response.data
  },
}

export default commentApi
