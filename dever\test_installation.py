#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装测试脚本
检查所有依赖包是否正确安装
"""

import sys

def test_imports():
    """测试所有依赖包的导入"""
    print("=" * 50)
    print("测试依赖包安装情况")
    print("=" * 50)
    
    packages = [
        ("pyautogui", "GUI自动化"),
        ("pygetwindow", "窗口管理"),
        ("cv2", "OpenCV图像处理"),
        ("PIL", "Pillow图像库"),
        ("numpy", "数值计算"),
        ("win32api", "Windows API")
    ]
    
    failed_packages = []
    
    for package, description in packages:
        try:
            __import__(package)
            print(f"✅ {package:15} - {description}")
        except ImportError as e:
            print(f"❌ {package:15} - {description} (导入失败: {e})")
            failed_packages.append(package)
        except Exception as e:
            print(f"⚠️  {package:15} - {description} (其他错误: {e})")
            failed_packages.append(package)
    
    print("\n" + "=" * 50)
    
    if failed_packages:
        print(f"❌ 有 {len(failed_packages)} 个包安装失败:")
        for pkg in failed_packages:
            print(f"   - {pkg}")
        print("\n建议解决方案:")
        print("1. 运行 install_manual.bat 手动安装")
        print("2. 使用国内镜像: pip install 包名 -i https://pypi.tuna.tsinghua.edu.cn/simple/")
        print("3. 升级pip: python -m pip install --upgrade pip")
        return False
    else:
        print("🎉 所有依赖包安装成功！")
        print("可以开始使用自动化工具了。")
        return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n" + "=" * 50)
    print("测试基本功能")
    print("=" * 50)
    
    try:
        import pyautogui
        print("✅ pyautogui 基本功能正常")
        
        import pygetwindow as gw
        windows = gw.getAllWindows()
        print(f"✅ 检测到 {len(windows)} 个窗口")
        
        import cv2
        print(f"✅ OpenCV 版本: {cv2.__version__}")
        
        import numpy as np
        print(f"✅ NumPy 版本: {np.__version__}")
        
        from PIL import Image
        print("✅ Pillow 图像库正常")
        
        print("\n🎉 基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("Cursor Augment Code 自动化工具 - 安装测试")
    print(f"Python 版本: {sys.version}")
    print()
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        # 测试基本功能
        func_success = test_basic_functionality()
        
        if func_success:
            print("\n" + "=" * 50)
            print("🚀 安装测试完成，可以开始使用！")
            print("=" * 50)
            print("下一步:")
            print("1. 双击运行 run_gui.bat 启动图形界面")
            print("2. 或运行 python gui_automation.py")
            print("3. 首次使用请先创建按钮模板")
        else:
            print("\n❌ 功能测试失败，请检查安装")
    else:
        print("\n❌ 依赖包安装不完整，请重新安装")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
