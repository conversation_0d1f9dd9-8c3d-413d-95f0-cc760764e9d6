package com.shenmo.wen.common.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum InteractionActionEnum {

    DISLIKE(0,"点踩"),
    LIKE(1,"点赞")
    ;

    private final int code;
    private final String text;

    public static InteractionActionEnum of(int code) {
        final InteractionActionEnum[] values = values();
        for (InteractionActionEnum e : values) {
            if (e.code == code) {
                return e;
            }
        }
        throw new EnumConstantNotPresentException(InteractionActionEnum.class, String.valueOf(code));
    }
}
