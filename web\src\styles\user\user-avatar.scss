/*
 * UserAvatar 组件样式
 * 用户头像组件的样式定义，包括头像容器、用户信息和操作按钮
 */

/* 头像容器样式，使用 flex 布局实现内部元素的居中对齐 */
.avatar-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 用户信息文本样式 */
.user-info {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  min-width: 8rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.info-row strong {
  margin-right: 0.3rem;
  min-width: 3.5rem;
  flex-shrink: 0;
}

/* 操作按钮行 */
.actions-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(0, 0, 0, 10%);
}

/*
 * UserInfoGroup 组件样式
 * 用户信息组合组件的样式定义，包括用户信息组和在线通知
 */

.user-info-group {
  display: flex;
  align-items: center;
  margin-top: 0.75rem;
}

.online-notification-container {
  width: 3rem;
  margin-right: 12%;
}

.online-info {
  display: flex;
  justify-content: flex-end;
}
