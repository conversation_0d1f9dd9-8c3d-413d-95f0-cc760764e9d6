package com.shenmo.wen.app.core.interaction.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.shenmo.wen.app.core.interaction.pojo.entity.WenInteraction;
import com.shenmo.wen.common.enumeration.InteractionTargetEnum;
import com.shenmo.wen.common.util.mybatis.LambdaUtils;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface WenInteractionMapper extends BaseMapper<WenInteraction> {
    default List<Long> listArticleIdByUserId(Long userId) {
        return listTargetIdByUserAndTarget(userId, InteractionTargetEnum.ARTICLE.getCode());
    }

    default List<Long> listCommentIdByUserId(Long userId) {
        return listTargetIdByUserAndTarget(userId, InteractionTargetEnum.COMMENT.getCode());
    }

    default List<Long> listTargetIdByUserAndTarget(Long userId, Integer targetType) {
        return selectList(new QueryWrapper<WenInteraction>()
                .select("DISTINCT " + LambdaUtils.getColumnCache(WenInteraction::getTargetId))
                .lambda()
                .eq(WenInteraction::getUserId, userId)
                .eq(WenInteraction::getTargetType, targetType))
                .stream().map(WenInteraction::getTargetId).toList();
    }

    default WenInteraction targetById(Long userId, Integer targetType, Long targetId) {
        return selectOne(Wrappers.<WenInteraction>lambdaQuery().
                eq(WenInteraction::getUserId, userId)
                .eq(WenInteraction::getTargetType, targetType)
                .eq(WenInteraction::getTargetId, targetId));
    }

    /**
     * 删除目标的所有互动记录
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     */
    default void deleteByTargetId(Integer targetType, Long targetId) {
        delete(Wrappers.<WenInteraction>lambdaQuery()
                .eq(WenInteraction::getTargetType, targetType)
                .eq(WenInteraction::getTargetId, targetId));
    }
}
