package com.shenmo.wen.common.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum InteractionTargetEnum {

    COMMENT(0,"评论"),
    ARTICLE(1,"文章")
    ;

    private final int code;
    private final String text;

    public static InteractionTargetEnum of(int code) {
        final InteractionTargetEnum[] values = values();
        for (InteractionTargetEnum e : values) {
            if (e.code == code) {
                return e;
            }
        }
        throw new EnumConstantNotPresentException(InteractionTargetEnum.class, String.valueOf(code));
    }
}
