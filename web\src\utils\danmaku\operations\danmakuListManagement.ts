/**
 * 弹幕列表管理工具函数
 * 提供弹幕列表的增删改查操作
 */

import type { Danmu } from '@/types/danmaku/danmu.types'

import type { Ref, ComputedRef } from 'vue'

/**
 * 弹幕列表管理选项接口
 */
interface ListManagementOptions {
  /** 弹幕列表 */
  danmuList: ComputedRef<Danmu[]>
  /** 当前播放索引 */
  index: Ref<number>
}

/**
 * 弹幕列表管理操作
 * @param options 管理选项
 * @returns 包含列表操作方法的对象
 * @example
 * ```typescript
 * const { add, push } = useDanmakuListManagement({
 *   danmuList: computed(() => danmus.value),
 *   index: ref(0)
 * })
 *
 * // 在当前位置插入弹幕
 * const insertIndex = add('新弹幕内容')
 *
 * // 在末尾添加弹幕
 * const appendIndex = push('末尾弹幕')
 * ```
 */
export function useDanmakuListManagement(options: ListManagementOptions) {
  const { danmuList, index } = options

  /**
   * 添加弹幕（插入到当前播放的弹幕位置）
   * @param danmu 要添加的弹幕数据
   * @returns 插入后的索引位置
   */
  function add(danmu: Danmu): number {
    if (index.value === danmuList.value.length) {
      // 如果当前弹幕已经播放完了，那么仍然走 push
      danmuList.value.push(danmu)

      return danmuList.value.length - 1
    } else {
      const _index = index.value % danmuList.value.length
      danmuList.value.splice(_index, 0, danmu)

      return _index + 1
    }
  }

  /**
   * 添加弹幕（插入到弹幕末尾）
   * @param danmu 要添加的弹幕数据
   * @returns 插入后的索引位置
   */
  function push(danmu: Danmu): number {
    danmuList.value.push(danmu)
    return danmuList.value.length - 1
  }

  return {
    add,
    push,
  }
}
