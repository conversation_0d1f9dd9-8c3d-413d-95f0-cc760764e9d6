<!--
  特权模板创建表单组件
  
  功能说明：
  - 提供模板创建表单
  - 支持图标上传、基本信息填写等
  - 提交后关闭弹框
-->
<template>
  <div class="privilege-template-form">
    <NForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="120px"
    >
      <!-- 模板名称 -->
      <NFormItem label="模板名称" path="name">
        <NInput
          v-model:value="formData.name"
          placeholder="请输入模板名称"
          maxlength="50"
          show-count
        />
      </NFormItem>

      <!-- 模板图标 -->
      <NFormItem label="模板图标" path="icon">
        <NUpload :custom-request="handleIconUpload" :show-file-list="false" accept="image/*">
          <NButton :loading="isUploadingIcon">
            {{ formData.icon ? '重新上传' : '上传图标' }}
          </NButton>
        </NUpload>
        <div v-if="formData.icon" class="icon-preview">
          <img :src="formData.icon" alt="图标预览" />
        </div>
      </NFormItem>

      <!-- 模板链接 -->
      <NFormItem label="模板链接" path="link">
        <NInput v-model:value="formData.link" placeholder="请输入模板链接" />
      </NFormItem>

      <!-- 模板面额 -->
      <NFormItem label="模板面额" path="denomination">
        <NInputNumber
          v-model:value="formData.denomination"
          placeholder="请输入模板面额"
          :min="1"
          :precision="0"
        />
      </NFormItem>

      <!-- 模板描述 -->
      <NFormItem label="模板描述" path="description">
        <NInput
          v-model:value="formData.description"
          type="textarea"
          placeholder="请输入模板描述"
          :rows="4"
          maxlength="200"
          show-count
        />
      </NFormItem>

      <!-- 二维码URL -->
      <NFormItem label="二维码URL" path="qrCodeUrl">
        <NInput v-model:value="formData.qrCodeUrl" placeholder="请输入二维码URL（可选）" />
      </NFormItem>

      <!-- 提交按钮 -->
      <NFormItem>
        <NSpace>
          <NButton
            type="primary"
            @click="handleSubmit"
            :loading="isSubmitting"
            :disabled="!isFormValid"
          >
            创建
          </NButton>
          <NButton @click="handleReset">重置</NButton>
        </NSpace>
      </NFormItem>
    </NForm>
  </div>
</template>

<script lang="ts" setup>
import {
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NUpload,
  NButton,
  NSpace,
  useMessage,
  type UploadCustomRequestOptions,
} from 'naive-ui'
import { ref, computed, reactive } from 'vue'

import fileApi from '@/api/file'
import { privilegeTemplateApi } from '@/api/privilege'
import { PRIVILEGE_TEMPLATE } from '@/constants/privilege/bucket.constants'

const emit = defineEmits<{
  success: []
}>()

const message = useMessage()

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({
  name: '',
  icon: '',
  link: '',
  denomination: null as number | null,
  description: '',
  qrCodeUrl: '',
})

// 表单验证规则
const formRules = {
  name: {
    required: true,
    message: '请输入模板名称',
    trigger: 'blur',
  },
  icon: {
    required: true,
    message: '请上传模板图标',
    trigger: 'change',
  },
  link: {
    required: true,
    message: '请输入模板链接',
    trigger: 'blur',
  },
  denomination: {
    required: true,
    message: '请输入模板面额',
    trigger: 'change',
    validator: (_rule: unknown, value: number) => {
      if (!value || value <= 0) {
        return new Error('模板面额必须大于0')
      }
      return true
    },
  },
}

// 状态
const isSubmitting = ref(false)
const isUploadingIcon = ref(false)

// 表单验证状态
const isFormValid = computed(() => {
  return (
    formData.name.trim() !== '' &&
    formData.icon !== '' &&
    formData.link.trim() !== '' &&
    formData.denomination &&
    formData.denomination > 0
  )
})

/**
 * 处理图标上传
 */
const handleIconUpload = async (options: UploadCustomRequestOptions) => {
  const { file } = options

  if (!file.file) {
    message.error('请选择文件')
    return
  }

  // 验证文件类型
  if (!file.file.type.startsWith('image/')) {
    message.error('请选择图片文件')
    return
  }

  // 验证文件大小（限制为2MB）
  if (file.file.size > 2 * 1024 * 1024) {
    message.error('图片大小不能超过2MB')
    return
  }

  isUploadingIcon.value = true
  try {
    const response = await fileApi.upload(file.file, PRIVILEGE_TEMPLATE)
    if (response.success && response.data) {
      formData.icon = fileApi.getResourceURL(response.data)
      message.success('图标上传成功')
    } else {
      message.error('图标上传失败')
    }
  } catch (error) {
    console.error('图标上传失败:', error)
    message.error('图标上传失败，请稍后重试')
  } finally {
    isUploadingIcon.value = false
  }
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
  } catch {
    return
  }

  isSubmitting.value = true
  try {
    const response = await privilegeTemplateApi.save({
      name: formData.name,
      icon: formData.icon,
      link: formData.link,
      denomination: formData.denomination!,
      description: formData.description,
      qrCodeUrl: formData.qrCodeUrl,
    })

    if (response.success && response.data) {
      message.success('模板创建成功！')
      emit('success')
      handleReset()
    } else {
      message.error('创建模板失败')
    }
  } catch (error) {
    console.error('创建模板失败:', error)
    message.error('创建模板失败，请稍后重试')
  } finally {
    isSubmitting.value = false
  }
}

/**
 * 重置表单
 */
const handleReset = () => {
  formData.name = ''
  formData.icon = ''
  formData.link = ''
  formData.denomination = null
  formData.description = ''
  formData.qrCodeUrl = ''
}

/**
 * 重置表单（暴露给父组件）
 */
const reset = () => {
  handleReset()
}

// 暴露方法给父组件
defineExpose({
  reset,
})
</script>

<style lang="scss" scoped>
.privilege-template-form {
  .icon-preview {
    margin-top: 8px;

    img {
      width: 64px;
      height: 64px;
      object-fit: cover;
      border-radius: 8px;
      border: 1px solid var(--border-color);
    }
  }
}
</style>
