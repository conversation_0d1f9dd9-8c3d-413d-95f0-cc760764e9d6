import favoriteApi from '@/api/favorite'
import interactionApi from '@/api/interaction'
import type { FavoriteResponse } from '@/types/api/favorite-response.types'
import type { InteractionResponse } from '@/types/api/interaction-response.types'
import type { Comment } from '@/types/comment/comment.types'
import message from '@/utils/ui/message'

/**
 * 评论交互操作组合式函数返回值类型
 */
export interface UseCommentInteractionActionsReturn {
  /** 互动操作（点赞/踩） */
  interactionBtn: (comment: Comment, actionType: number) => void
  /** 收藏操作 */
  favoriteBtn: (comment: Comment) => void
}

/**
 * 评论交互操作组合式函数
 * 提供点赞、踩、收藏等操作功能
 */
export function useCommentInteractionActions(): UseCommentInteractionActionsReturn {
  // 互动（点赞/踩）
  const interactionBtn = (comment: Comment, actionType: number) => {
    const reqParam = {
      targetType: 0,
      targetId: comment!.id,
      actionType: actionType,
    }
    interactionApi.save({ ...reqParam, type: reqParam.targetType }).then((res) => {
      const data = res?.data as InteractionResponse
      if (data) {
        comment!.likeCount = data.likeCount
        comment!.dislikeCount = data.dislikeCount
        const like = actionType === 1
        // 取消互动
        if (data.cancel) {
          if (like) {
            message.info('赞取消')
            comment!.isLike = false
          } else {
            message.info('踩取消')
            comment!.isDislike = false
          }
        } else {
          if (like) {
            message.success('赞 :)')
            comment!.isLike = true
          } else {
            message.warning('踩 :(')
            comment!.isDislike = true
          }
        }
      }
    })
  }

  // 收藏
  const favoriteBtn = (comment: Comment) => {
    const reqParam = {
      targetType: 0,
      targetId: comment!.id,
    }
    favoriteApi.save(reqParam).then((res) => {
      const data = res?.data as FavoriteResponse
      if (data) {
        comment!.favoriteCount = data.count
        if (data.cancel) {
          message.info('取消收藏')
          comment!.isFavorite = false
        } else {
          message.success('已收藏')
          comment!.isFavorite = true
        }
      }
    })
  }

  return {
    interactionBtn,
    favoriteBtn,
  }
}
