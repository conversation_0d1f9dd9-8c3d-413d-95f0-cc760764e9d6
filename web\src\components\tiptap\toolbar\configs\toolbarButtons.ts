// 导入分组的按钮配置
import { alignButtons } from './alignButtons'
import { headingButtons } from './headingButtons'
import { listButtons } from './listButtons'
import { otherButtons } from './otherButtons'
import { textFormatButtons } from './textFormatButtons'

// 导出类型
export type { ToolbarButtonConfig } from './types'

// 重新导出分组的按钮配置
export { textFormatButtons } from './textFormatButtons'
export { headingButtons } from './headingButtons'
export { listButtons } from './listButtons'
export { alignButtons } from './alignButtons'
export { otherButtons } from './otherButtons'

/**
 * 所有按钮配置的集合
 */
export const allButtonConfigs = {
  textFormat: textFormatButtons,
  heading: headingButtons,
  list: listButtons,
  align: alignButtons,
  other: otherButtons,
}
