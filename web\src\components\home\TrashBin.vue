<template>
  <transition name="trash-bin-fade">
    <div v-if="visible" class="trash-bin" :class="{ 'trash-bin-active': isActive }">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        width="48"
        height="48"
        :style="{ color: isActive ? '#ff4444' : '#666666' }"
      >
        <path
          fill="currentColor"
          d="M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12Z"
        />
      </svg>
      <span class="trash-bin-text">{{ isActive ? '释放删除' : '拖拽到此处删除' }}</span>
    </div>
  </transition>
</template>

<script lang="ts" setup>
defineProps<{
  visible: boolean
  isActive: boolean
}>()
</script>

<style lang="scss" scoped>
@use '@/styles/ui-elements/long-press';
</style>
