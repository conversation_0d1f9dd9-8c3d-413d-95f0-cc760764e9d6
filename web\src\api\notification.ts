import type { NotificationListResponse } from '@/types/api/notification-list-response.types'
import { type ResponseData } from '@/types/api/response-data.types'
import { type NotificationRequestParams } from '@/types/notification/notification-api.types'
import api from '@/utils/api/api'

/**
 * 通知相关API接口
 * 提供通知的查询、标记已读等功能
 */
const notificationApi = {
  /** API基础路径 */
  URL: '/core/notifications',

  /**
   * 加载通知列表
   * 获取用户的通知列表，支持分页和筛选
   * @param params 查询参数，包含分页信息和筛选条件
   * @returns 返回通知列表和统计信息
   */
  load: async (
    params?: NotificationRequestParams,
  ): Promise<ResponseData<NotificationListResponse>> => {
    const response = await api.get<ResponseData<NotificationListResponse>>(
      notificationApi.URL,
      params,
    )
    return response.data
  },

  /**
   * 标记通知为已读
   * 将指定的通知标记为已读状态
   * @param id 通知ID
   * @returns 返回操作结果
   */
  read: async (id: string): Promise<ResponseData<void>> => {
    const response = await api.patch<ResponseData<void>>(
      notificationApi.URL + '/' + id + '/read-status',
    )
    return response.data
  },

  /**
   * 标记所有通知为已读
   * 将用户的所有未读通知标记为已读状态
   * @returns 返回操作结果
   */
  readAll: async (): Promise<ResponseData<void>> => {
    const response = await api.patch<ResponseData<void>>(notificationApi.URL + '/read-status')
    return response.data
  },

  /**
   * 获取未读通知数量
   * 获取当前用户的未读通知总数
   * @returns 返回未读通知数量
   */
  unreadCount: async (): Promise<ResponseData<number>> => {
    const response = await api.get<ResponseData<number>>(notificationApi.URL + '/total-unread')
    return response.data
  },
}

export default notificationApi
