/*
 * ArticleModal 组件样式
 * 文章模态框的样式定义，包括模态框容器和编辑器布局
 */

.article-modal {
  &.n-modal {
    width: 100vw;
    height: 100vh;
    max-width: 100vw;
    max-height: 100vh;
  }

  &.n-dialog {
    margin: 0;
    width: 100vw;
    max-width: 100vw;
    max-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  /* 确保对话框内容区域不超出屏幕 */
  .n-dialog__content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  /* 确保按钮区域在屏幕内 */
  .n-dialog__action {
    flex-shrink: 0;
    padding: 16px 24px;
  }

  /* 暗色主题下的弹框背景色 */
  .dark-theme &.n-modal,
  .dark-theme &.n-dialog {
    background-color: var(--dark-gray-1);
  }

  /* 编辑器整体包装器 - 垂直布局 */
  .article-editor-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
  }

  /* 编辑器容器 - 只包含编辑器 */
  .article-editor-container {
    flex: 1;
    width: 100%;
    min-height: 0; /* 允许 flex 子项收缩 */
  }

  &-content {
    max-width: 100%;
    padding: 0 1.25rem;
    border: var(--border-1);
    border-radius: 0.25rem 0.25rem 0 0; /* 只有上方圆角 */
    min-height: calc(100vh - 450px);
    min-height: calc(100dvh - 450px);
    max-height: calc(100vh - 450px);
    max-height: calc(100dvh - 450px);
    overflow: hidden;
    box-sizing: border-box;

    .dark-theme & {
      background-color: var(--dark-gray);
    }

    /* 暗色主题下的编辑器工具栏背景色 */
    .dark-theme & .tiptap-editor-wrapper .editor-toolbar,
    .dark-theme & .tiptap-editor-wrapper .editor-toolbar-bgc {
      background-color: var(--dark-gray);
    }

    /* 确保编辑器不超出容器 */
    .tiptap-editor-wrapper {
      max-width: 100%;
      height: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;

      .editor-toolbar {
        position: sticky;
        top: 0;
        z-index: 10;
      }

      .editor-content {
        flex: 1;
        overflow-y: auto;
        max-width: 100%;
        box-sizing: border-box;
      }
    }
  }

  /* 外部字符计数器样式 - 独立于编辑器 */
  .character-count-external {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0.5rem 1.25rem;
    background-color: var(--white);
    color: var(--gray-5);
    font-size: 0.75rem;
    font-family: var(--tiptap-font-family-mono);
    border: var(--border-1);
    border-top: none; /* 与编辑器连接 */
    border-radius: 0 0 0.25rem 0.25rem; /* 只有下方圆角 */
    user-select: none;
    min-height: 2rem;
    box-sizing: border-box;
    flex-shrink: 0; /* 防止被压缩 */

    .dark-theme & {
      background-color: var(--dark-gray);
      color: var(--gray-5);
    }
  }
}

/*
 * ArticleCard 组件样式
 * 文章卡片组件的样式定义，包括卡片布局、拖拽效果和响应式设计
 */

.card-item {
  border-radius: 0.5rem;
  margin-top: 1.25rem;
  box-sizing: border-box;
  max-width: 100vw;

  &:hover {
    transform: translateY(-0.6rem);
    box-shadow: var(--shadow);
  }

  &.dragging {
    opacity: 0.3;
    pointer-events: none;
  }

  // 拖拽指示器
  &.drag-over-before::before,
  &.drag-over-after::after,
  &.drag-over-before-vertical::before,
  &.drag-over-after-vertical::after {
    content: '';
    position: absolute;
    background-color: var(--blue);
    z-index: 10;
    border-radius: 2px;
  }

  // 水平指示器（多卡片行）
  &.drag-over-before::before,
  &.drag-over-after::after {
    top: 0;
    bottom: 0;
    width: 4px;
  }

  &.drag-over-before::before {
    left: -12px;
  }

  &.drag-over-after::after {
    right: -12px;
  }

  // 垂直指示器（单卡片行/窄屏）
  &.drag-over-before-vertical::before,
  &.drag-over-after-vertical::after {
    left: 0;
    right: 0;
    height: 4px;
  }

  &.drag-over-before-vertical::before {
    top: -12px;
  }

  &.drag-over-after-vertical::after {
    bottom: -12px;
  }

  .article-header {
    display: flex;
    align-items: center;
    padding: 0.15rem 0;

    // 防止图标影响文本选择
    user-select: text;

    .scope-icon-wrapper {
      margin-right: 0.6rem;
      padding: 0.3rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      flex-shrink: 0;
      color: var(--black);
      cursor: pointer;

      // 禁用图标选择
      user-select: none;

      // 防止点击时出现焦点框
      outline: none;
      -webkit-tap-highlight-color: transparent;

      // 统一的图标悬浮动画效果
      &:hover {
        opacity: 0.85;
        transform: scale(1.12);
        background-color: rgba(24, 144, 255, 8%);
        filter: brightness(1.1);
      }

      &:active {
        transform: scale(0.95);
        opacity: 0.7;
      }

      &:focus {
        outline: none;
      }

      // 确保内部 SVG 也不能被选中
      svg {
        user-select: none;
        pointer-events: none; // 让点击事件穿透到父元素
        transition: inherit;
      }
    }

    .article-title {
      cursor: pointer;
      font-size: 1.1rem;
      font-weight: bold;
      color: var(--black);
      transition: color 0.2s ease;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        color: var(--blue);
        text-decoration: underline;
      }
    }
  }

  .card-tag {
    margin: 0.6rem 0.1rem;
  }

  // 下载图标样式
  .flex-between-center {
    // 允许标签文本被选择
    user-select: text;

    // 但是图标不能被选择，并应用统一的悬浮动画
    svg {
      user-select: none;
      -webkit-tap-highlight-color: transparent;
      outline: none;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        opacity: 0.85;
        transform: scale(1.1);
        filter: brightness(1.1);
      }

      &:active {
        transform: scale(0.95);
        opacity: 0.7;
      }

      &:focus {
        outline: none;
      }
    }
  }

  .article-content {
    border-radius: 0.25rem;
    padding: 0.25rem;
    height: 19rem;
    background-color: var(--white-1);

    :deep(.image-wrapper),
    :deep(img) {
      max-width: 100%;
      height: auto !important;
      object-fit: contain;
    }

    @media (width <= 768px) {
      :deep(.ProseMirror) {
        p > .image-wrapper,
        p > img {
          max-width: 100% !important;
          min-width: unset !important;
          width: auto !important;
        }
      }
    }
  }

  .article-avatar {
    cursor: grab;
    user-select: none;
    touch-action: none;
    -webkit-tap-highlight-color: transparent;
    outline: none;

    &:active {
      cursor: grabbing;
    }

    &:focus {
      outline: none;
    }
  }
}

/*
 * ArticleList 组件样式
 * 文章列表组件的样式定义，包括容器布局和无限滚动
 */

.article-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow-x: hidden;

  .infinite-scroll-container {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .infinite-load-info {
    width: 100%;
    padding: 1.25rem 0;
    text-align: center;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
