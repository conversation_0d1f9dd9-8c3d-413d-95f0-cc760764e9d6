<template>
  <div class="notification-btn" style="cursor: pointer" ref="buttonRef">
    <NBadge
      :max="99"
      :value="unreadCount"
      :show-zero="false"
      :show="notificationReceiveType !== NotificationReceiveType.CLOSE"
    >
      <IosNotificationsOutline
        v-if="notificationReceiveType !== NotificationReceiveType.CLOSE"
        :size="24"
      />
      <IosNotificationsOff v-else :size="24" />
    </NBadge>
  </div>
</template>

<script setup lang="ts">
import { NBadge } from 'naive-ui'
import { ref } from 'vue'

import { NotificationReceiveType } from '@/constants/notification/notification-receive-type.constants'
import { IosNotificationsOutline, IosNotificationsOff } from '@/icons'

defineProps({
  unreadCount: {
    type: Number,
    default: 0,
  },
  notificationReceiveType: {
    type: Number,
    required: true,
  },
})

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const emit = defineEmits(['click', 'long-press'])

const buttonRef = ref<HTMLElement | null>(null)
</script>

<style lang="scss" scoped>
@use '@/styles/notification/notification-btn-modal';
</style>
