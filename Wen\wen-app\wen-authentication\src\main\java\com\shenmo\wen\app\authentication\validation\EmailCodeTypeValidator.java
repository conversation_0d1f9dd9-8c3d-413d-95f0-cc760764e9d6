package com.shenmo.wen.app.authentication.validation;

import com.shenmo.wen.app.authentication.enums.EmailCodeType;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * 邮箱验证码类型验证器
 * 
 * <AUTHOR>
 */
public class EmailCodeTypeValidator implements ConstraintValidator<ValidEmailCodeType, String> {
    
    @Override
    public void initialize(ValidEmailCodeType constraintAnnotation) {
        // 初始化方法，可以在这里获取注解参数
    }
    
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 如果值为空，由@NotBlank注解处理
        if (value == null || value.trim().isEmpty()) {
            return true;
        }
        
        // 验证是否为有效的验证码类型
        return EmailCodeType.isValid(value);
    }
}
