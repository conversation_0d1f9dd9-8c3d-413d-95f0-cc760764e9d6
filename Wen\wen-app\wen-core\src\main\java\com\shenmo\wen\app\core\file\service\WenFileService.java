package com.shenmo.wen.app.core.file.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.function.Consumer;

/**
 *
 * <AUTHOR>
 */
public interface WenFileService {
    String uploadImage(String bucket, MultipartFile file) throws IOException;

    String uploadImage(String bucket, MultipartFile file, Consumer<String> fileUriConsumer) throws IOException;
}
