/* 通知样式在深色模式下的调整 */
.dark-theme .notification-table .n-data-table-th {
  background-color: var(--white-1);
  color: var(--black-contrast);
}

.dark-theme .notification-table .n-data-table-td {
  background-color: #2c2c2c; /* 自定义介于creamy-white-2(#262626)和creamy-white-3(#333333)之间的颜色 */
  color: var(--black-contrast);
}

.dark-theme .notification-table .n-data-table__pagination {
  background-color: var(--creamy-white-2);
}

/* 确保通知右上角按钮和弹出内容在暗色模式下正确显示 */
.dark-theme .notification-container .notification-btn {
  color: var(--black);
}

.dark-theme .n-badge .n-badge-sup {
  background-color: var(--red);
  color: var(--black-contrast);
}

/* 通知消息的暗色模式样式 */
.dark-theme .n-notification {
  background-color: var(--white-2);
  color: var(--black);
  border: 1px solid var(--gray-3);
}

.dark-theme .n-notification .n-notification-main .n-notification-main__content {
  color: var(--black);
}

.dark-theme .n-notification .n-notification-main .n-notification-main__title {
  color: var(--blue);
}

.dark-theme .n-notification .n-notification-main .n-notification-main__avatar .n-avatar {
  border: 1px solid var(--gray-3);
}

.dark-theme .n-notification .n-notification-main .n-notification-main__action .n-button {
  color: var(--blue);
}

.dark-theme .n-notification .n-notification-main .n-notification-main__close {
  color: var(--gray-5);
}

.dark-theme .n-notification .n-notification-main .n-notification-main__close:hover {
  color: var(--black);
}
