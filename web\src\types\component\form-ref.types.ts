import type { FormValidateCallback } from './form-validate-callback.types'

/**
 * 表单组件引用类型定义
 * 用于 Naive UI 表单组件的引用
 */

/**
 * 表单组件引用接口
 * 定义表单组件的常用方法
 */
export interface FormRef {
  /** 验证表单 */
  validate: (callback?: FormValidateCallback) => Promise<void> | void
  /** 恢复验证状态 */
  restoreValidation: () => void
  /** 重置表单 */
  reset?: () => void
}

// 重新导出TurnstileRef类型以保持兼容性
export type { TurnstileRef } from './turnstile-ref.types'
