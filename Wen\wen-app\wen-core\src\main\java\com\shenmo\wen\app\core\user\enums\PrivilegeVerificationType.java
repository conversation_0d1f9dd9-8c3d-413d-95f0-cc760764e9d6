package com.shenmo.wen.app.core.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 特权验证类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PrivilegeVerificationType {

    /**
     * 短信验证
     */
    SMS(0, "短信验证"),

    /**
     * 二维码验证
     */
    QR_CODE(1, "二维码验证");

    private final Integer code;
    private final String description;

    /**
     * 根据类型码获取枚举
     */
    public static PrivilegeVerificationType fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PrivilegeVerificationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
