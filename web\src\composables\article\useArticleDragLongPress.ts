import { ref } from 'vue'

import type { Article } from '@/types/article/article.types'
import logger from '@/utils/log/log'

/**
 * 文章拖拽长按检测组合式函数返回值类型
 */
export interface UseArticleDragLongPressReturn {
  /** 是否长按激活 */
  isLongPressActive: import('vue').Ref<boolean>
  /** 开始长按检测 */
  startLongPress: (
    event: MouseEvent | TouchEvent,
    article: Article,
    avatarElement: HTMLElement,
    onLongPressTriggered: (
      event: MouseEvent | TouchEvent,
      article: Article,
      avatarElement: HTMLElement,
    ) => void,
  ) => void
  /** 取消长按 */
  cancelLongPress: () => void
  /** 清理长按状态 */
  cleanup: () => void
}

/**
 * 文章拖拽长按检测组合式函数
 * 提供长按检测和处理功能
 */
export function useArticleDragLongPress(): UseArticleDragLongPressReturn {
  // 长按定时器
  let longPressTimer: ReturnType<typeof setTimeout> | null = null
  const longPressDelay = 500

  // 触摸移动阈值（像素）
  const moveThreshold = 10
  let startPosition = { x: 0, y: 0 }
  const isLongPressActive = ref(false)

  // 获取事件坐标
  const getEventCoordinates = (event: MouseEvent | TouchEvent) => {
    if (event instanceof MouseEvent) {
      return { clientX: event.clientX, clientY: event.clientY }
    } else if (event instanceof TouchEvent) {
      // 优先使用 touches，如果为空则使用 changedTouches（适用于 touchend 事件）
      const touch = event.touches.length > 0 ? event.touches[0] : event.changedTouches[0]
      if (touch) {
        return { clientX: touch.clientX, clientY: touch.clientY }
      }
    }
    return { clientX: 0, clientY: 0 }
  }

  // 清理函数
  const cleanup = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer)
      longPressTimer = null
    }
    isLongPressActive.value = false
  }

  // 开始长按
  const startLongPress = (
    event: MouseEvent | TouchEvent,
    article: Article,
    avatarElement: HTMLElement,
    onLongPressTriggered: (
      event: MouseEvent | TouchEvent,
      article: Article,
      avatarElement: HTMLElement,
    ) => void,
  ) => {
    logger.debug('开始长按检测:', article.title)
    event.preventDefault()
    event.stopPropagation()

    cleanup()

    // 记录初始位置
    const { clientX, clientY } = getEventCoordinates(event)
    startPosition = { x: clientX, y: clientY }
    isLongPressActive.value = true

    // 添加临时的移动监听器来检测是否超过阈值
    const tempMoveHandler = (moveEvent: MouseEvent | TouchEvent) => {
      if (!isLongPressActive.value) return

      const { clientX: currentX, clientY: currentY } = getEventCoordinates(moveEvent)
      const distance = Math.sqrt(
        Math.pow(currentX - startPosition.x, 2) + Math.pow(currentY - startPosition.y, 2),
      )

      // 如果移动距离超过阈值，取消长按
      if (distance > moveThreshold) {
        logger.debug('移动距离超过阈值，取消长按')
        cleanup()
        removeTempListeners()
      }
    }

    const tempEndHandler = () => {
      cleanup()
      removeTempListeners()
    }

    const removeTempListeners = () => {
      isLongPressActive.value = false
      document.removeEventListener('mousemove', tempMoveHandler)
      document.removeEventListener('touchmove', tempMoveHandler)
      document.removeEventListener('mouseup', tempEndHandler)
      document.removeEventListener('touchend', tempEndHandler)
      document.removeEventListener('touchcancel', tempEndHandler)
    }

    // 添加临时监听器
    document.addEventListener('mousemove', tempMoveHandler, { passive: false })
    document.addEventListener('touchmove', tempMoveHandler, { passive: false })
    document.addEventListener('mouseup', tempEndHandler)
    document.addEventListener('touchend', tempEndHandler)
    document.addEventListener('touchcancel', tempEndHandler)

    longPressTimer = setTimeout(() => {
      if (isLongPressActive.value) {
        logger.debug('长按触发，开始拖拽:', article.title)
        removeTempListeners()
        onLongPressTriggered(event, article, avatarElement)
      }
    }, longPressDelay)
  }

  // 取消长按
  const cancelLongPress = () => {
    logger.debug('取消长按')

    // 只在长按检测阶段取消，拖拽状态由全局事件处理
    if (isLongPressActive.value) {
      logger.debug('取消长按检测')
      cleanup()
    }
  }

  return {
    isLongPressActive,
    startLongPress,
    cancelLongPress,
    cleanup,
  }
}
