/**
 * 通知API相关类型定义
 */

/**
 * 通知API响应数据类型
 */
export interface NotificationApiResponse<T = Record<string, unknown>> {
  /** 请求是否成功 */
  success: boolean
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data?: T
}

/**
 * 通知请求参数类型
 */
export interface NotificationRequestParams {
  /** 页码（从1开始） */
  pageNum?: number
  /** 每页数量 */
  pageSize?: number
  /** 通知类型 */
  type?: number
  /** 其他请求参数 */
  [key: string]: string | number | boolean | null | undefined
}
