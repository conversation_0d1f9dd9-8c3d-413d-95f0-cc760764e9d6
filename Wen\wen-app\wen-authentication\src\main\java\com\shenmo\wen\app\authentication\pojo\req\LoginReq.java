package com.shenmo.wen.app.authentication.pojo.req;

import com.shenmo.wen.app.authentication.validation.LoginValidationGroups;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;


/**
 * 登录请求
 *
 * <AUTHOR>
 */
@Data
public class LoginReq {

    /**
     * 手机号（手机号登录时必填）
     */
    @NotBlank(message = "手机号不能为空", groups = LoginValidationGroups.PhoneLogin.class)
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式错误", groups = LoginValidationGroups.PhoneLogin.class)
    private String phone;

    /**
     * 邮箱（邮箱登录时必填）
     */
    @NotBlank(message = "邮箱不能为空", groups = LoginValidationGroups.EmailLogin.class)
    @Email(message = "邮箱格式错误", groups = LoginValidationGroups.EmailLogin.class)
    private String email;

    /**
     * 密码（手机号登录时必填）
     */
    @NotBlank(message = "密码不能为空", groups = LoginValidationGroups.PhoneLogin.class)
    @Length(min = 6, message = "密码长度至少为 6 个字符", groups = LoginValidationGroups.PhoneLogin.class)
    private String password;

    /**
     * Turnstile验证码（所有登录方式都必填）
     */
    @NotBlank(message = "验证码不能为空", groups = {LoginValidationGroups.PhoneLogin.class, LoginValidationGroups.EmailLogin.class})
    private String cftt;

    /**
     * 邮箱验证码（邮箱登录时必填）
     */
    @NotBlank(message = "邮箱验证码不能为空", groups = LoginValidationGroups.EmailLogin.class)
    private String emailCode;

    /**
     * 判断是否为手机号登录
     *
     * @return 是否为手机号登录
     */
    public boolean isPhoneLogin() {
        return phone != null && !phone.trim().isEmpty();
    }

    /**
     * 判断是否为邮箱登录
     *
     * @return 是否为邮箱登录
     */
    public boolean isEmailLogin() {
        return email != null && !email.trim().isEmpty();
    }
}
