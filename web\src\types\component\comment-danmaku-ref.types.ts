import type { Comment } from '@/types/comment/comment.types'

/**
 * 评论弹幕组件引用类型定义
 * 定义评论弹幕组件的属性和方法接口
 */
export interface CommentDanmakuRef {
  /** 弹幕是否循环播放 */
  danmakuLoop: boolean
  /** 暂停弹幕播放 */
  pause: () => void
  /** 开始弹幕播放 */
  play: () => void
  /** 订阅评论更新 */
  subscribeComment: () => void
  /** 取消订阅评论更新 */
  unsubscribeComment: () => void
  /** 清空弹幕内容 */
  clearDanmaku: () => void
  /** 调整弹幕容器大小 */
  resize: () => void
  /** 添加评论列表到弹幕 */
  addCommentList: (comments: Comment[]) => void
}
