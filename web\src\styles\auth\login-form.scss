/*
 * LoginForm 组件样式
 * 登录表单组件的样式定义
 */

.login-form {
  padding: 0.6rem;

  :deep(.n-form-item-feedback-wrapper) {
    min-height: 0.75rem;
  }
}

.login-form-ipt {
  width: 11rem;
  background-color: var(--creamy-white-1);
}

.login-form-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem 1.25rem;
}

.login-btn {
  width: 6.25rem;
}

// 登录模式切换
.login-mode-switch {
  margin-bottom: 0.5rem;
}

// 验证码输入容器
.email-code-container {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.email-code-input {
  width: 65%;
  flex-shrink: 0;
}

// 通用文本按钮样式
.send-code-btn {
  flex-shrink: 0;
  font-size: 13px;
  padding: 0;
  min-width: auto;
  height: auto;
}

// 忘记密码链接样式
.forgot-password-link {
  text-align: right;
}
