import config from '@/config'

/**
 * 日志级别枚举
 */
const logLevels = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
} as const

/**
 * 日志级别类型
 */
type LogLevel = (typeof logLevels)[keyof typeof logLevels]

/**
 * 日志参数类型 - 支持常见的可序列化类型
 */
type LogArgument = string | number | boolean | null | undefined | object | Error

const currentLogLevel = (config.logLevel as LogLevel) || logLevels.INFO // 默认是 INFO 级别

/**
 * 判断日志级别是否满足输出要求
 * @param level 目标日志级别
 * @returns 是否应该输出日志
 */
function shouldLog(level: LogLevel): boolean {
  const levels = Object.values(logLevels)
  const currentLevelIndex = levels.indexOf(currentLogLevel)
  const targetLevelIndex = levels.indexOf(level)
  return targetLevelIndex >= currentLevelIndex
}

/**
 * 日志工具类
 * 提供分级日志输出功能，支持配置日志级别
 */
const logger = {
  levels: logLevels,

  /**
   * 输出调试级别日志
   * @param message 日志消息
   * @param args 附加参数
   */
  debug(message: string, ...args: LogArgument[]) {
    if (shouldLog(logLevels.DEBUG)) {
      console.debug(message, ...args)
    }
  },

  /**
   * 输出信息级别日志
   * @param message 日志消息
   * @param args 附加参数
   */
  info(message: string, ...args: LogArgument[]) {
    if (shouldLog(logLevels.INFO)) {
      console.info(message, ...args)
    }
  },

  /**
   * 输出警告级别日志
   * @param message 日志消息
   * @param args 附加参数
   */
  warn(message: string, ...args: LogArgument[]) {
    if (shouldLog(logLevels.WARN)) {
      console.warn(message, ...args)
    }
  },

  /**
   * 输出错误级别日志
   * @param message 日志消息
   * @param args 附加参数
   */
  error(message: string, ...args: LogArgument[]) {
    if (shouldLog(logLevels.ERROR)) {
      console.error(message, ...args)
    }
  },
}

export default logger
