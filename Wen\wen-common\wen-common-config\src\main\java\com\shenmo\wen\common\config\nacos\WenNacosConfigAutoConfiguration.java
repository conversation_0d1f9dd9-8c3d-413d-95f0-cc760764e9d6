
package com.shenmo.wen.common.config.nacos;

import com.alibaba.cloud.nacos.NacosConfigEnabledCondition;
import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.cloud.nacos.annotation.NacosAnnotationProcessor;
import org.springframework.beans.factory.BeanFactoryUtils;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;

/**
 *
 * <AUTHOR>
 */
@AutoConfiguration
@Conditional(NacosConfigEnabledCondition.class)
public class WenNacosConfigAutoConfiguration {

	@Bean
	public NacosConfigProperties nacosConfigProperties(ApplicationContext context) {
		ApplicationContext parent = context.getParent();
		if (parent != null && BeanFactoryUtils.beanNamesForTypeIncludingAncestors(parent,
				NacosConfigProperties.class).length > 0) {
			return BeanFactoryUtils.beanOfTypeIncludingAncestors(parent, NacosConfigProperties.class);
		}
		if (NacosConfigManager.getInstance() == null) {
			return new NacosConfigProperties();
		} else {
			return NacosConfigManager.getInstance().getNacosConfigProperties();
		}
	}

	@Bean
	public static NacosAnnotationProcessor nacosAnnotationProcessor() {
		return new NacosAnnotationProcessor();
	}
}
