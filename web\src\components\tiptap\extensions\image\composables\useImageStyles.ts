/**
 * 图片样式计算相关的组合式函数
 */
import { computed, readonly, type Ref } from 'vue'

import type { NodeViewProps } from '@tiptap/core'

export function useImageStyles(
  props: NodeViewProps,
  isResizing: Ref<boolean>,
  resizeWidth: Ref<string>,
  resizeHeight: Ref<string>,
) {
  // 控制点定义 - 使用 readonly 优化性能
  const cornerHandles = readonly(['top-left', 'top-right', 'bottom-left', 'bottom-right'])
  const edgeHandles = readonly(['top', 'right', 'bottom', 'left'])

  // 缓存计算结果 - 优化控制点样式计算
  const handleStylesCache = new Map<string, Record<string, unknown>>()

  // 基础状态
  const isEditable = computed(() => props.editor.isEditable)
  const isSelected = computed(() => props.selected)

  // 计算样式 - 使用 shallowReactive 优化性能
  const wrapperStyles = computed(() => ({
    display: 'inline-block' as const,
    position: 'relative' as const,
    margin: '0',
    padding: '4px',
    verticalAlign: 'baseline' as const,
    lineHeight: '1',
    transform: 'translateY(0)',
    transition: 'none',
    zIndex: '1',
    willChange: 'transform' as const,
    boxSizing: 'border-box' as const,
    width: 'fit-content' as const,
    maxWidth: '100%',
    border: '0',
    userSelect: 'none' as const,
    pointerEvents: 'auto' as const,
  }))

  const imageStyles = computed(() => ({
    display: 'block' as const,
    maxWidth: '100%',
    margin: '0',
    padding: '0',
    verticalAlign: 'baseline' as const,
    transform: 'translateY(0)',
    transition: 'none',
    willChange: 'transform' as const,
    lineHeight: 'normal' as const,
    fontSize: 'inherit' as const,
    width: isResizing.value ? resizeWidth.value : props.node.attrs.width || '',
    height: isResizing.value ? resizeHeight.value : props.node.attrs.height || '',
  }))

  // 获取控制点光标样式 - 使用静态对象优化性能
  const cursorMap = Object.freeze({
    'top-left': 'nw-resize',
    'top-right': 'ne-resize',
    'bottom-left': 'sw-resize',
    'bottom-right': 'se-resize',
    top: 'n-resize',
    right: 'e-resize',
    bottom: 's-resize',
    left: 'w-resize',
  })

  const getCursorForHandle = (position: string) => {
    return cursorMap[position as keyof typeof cursorMap] || 'pointer'
  }

  // 计算控制点样式的核心函数
  const calculateHandleStyle = (position: string) => {
    const baseStyle = {
      position: 'absolute' as const,
      background: '#2d8cf0',
      border: '1px solid white',
      borderRadius: '50%',
      zIndex: '100',
      display: isSelected.value && isEditable.value ? 'block' : 'none',
      visibility: (isSelected.value && isEditable.value ? 'visible' : 'hidden') as
        | 'visible'
        | 'hidden',
      opacity: isSelected.value && isEditable.value ? '1' : '0',
    }

    // 角控制点样式
    if (cornerHandles.includes(position)) {
      const cornerStyles = {
        width: '8px',
        height: '8px',
        cursor: getCursorForHandle(position),
      }

      const positionStyles: Record<string, Record<string, string>> = {
        'top-left': { top: '-1px', left: '-1px' },
        'top-right': { top: '-1px', right: '-1px' },
        'bottom-left': { bottom: '-1px', left: '-1px' },
        'bottom-right': { bottom: '-1px', right: '-1px' },
      }

      return { ...baseStyle, ...cornerStyles, ...positionStyles[position] }
    }
    // 边控制点样式
    else if (edgeHandles.includes(position)) {
      const edgeStyles = {
        borderRadius: '2px',
        cursor: getCursorForHandle(position),
      }

      const positionStyles: Record<string, Record<string, string>> = {
        top: {
          top: '0px',
          left: 'calc(50% - 6px)',
          width: '12px',
          height: '4px',
        },
        right: {
          right: '0px',
          top: 'calc(50% - 6px)',
          width: '4px',
          height: '12px',
        },
        bottom: {
          bottom: '0px',
          left: 'calc(50% - 6px)',
          width: '12px',
          height: '4px',
        },
        left: {
          left: '0px',
          top: 'calc(50% - 6px)',
          width: '4px',
          height: '12px',
        },
      }

      return { ...baseStyle, ...edgeStyles, ...positionStyles[position] }
    }

    return baseStyle
  }

  // 获取控制点样式 - 优化缓存策略，缩放时不使用缓存
  const getHandleStyle = (position: string) => {
    // 缩放时不使用缓存，确保实时更新
    if (isResizing.value) {
      return calculateHandleStyle(position)
    }

    // 检查缓存
    const cacheKey = `${position}-${isSelected.value}-${isEditable.value}`
    if (handleStylesCache.has(cacheKey)) {
      return handleStylesCache.get(cacheKey)!
    }

    // 计算并缓存结果
    const result = calculateHandleStyle(position)
    handleStylesCache.set(cacheKey, result)
    return result
  }

  return {
    // 常量
    cornerHandles,
    edgeHandles,

    // 计算属性
    isEditable,
    isSelected,
    wrapperStyles,
    imageStyles,

    // 方法
    getCursorForHandle,
    getHandleStyle,
  }
}
