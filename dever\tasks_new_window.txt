请按照项目中 `.augment/rules/web.md` 文件中定义的规范对当前项目进行重构。在重构过程中，必须严格遵循以下要求：

1. **功能保持原则（最高优先级）**：
   - 绝对不能破坏任何现有功能
   - 所有用户界面交互必须保持原有行为
   - 所有API接口必须保持兼容性
   - 所有数据流和业务逻辑必须保持不变

2. **重构范围**：
   - 仅进行结构调整和代码抽取
   - 优化代码组织结构
   - 提取可复用组件
   - 改善代码可维护性

3. **执行步骤**：
   - 首先阅读并理解 `.augment/rules/web.md` 中的具体规范要求
   - 分析当前项目结构，识别需要重构的部分
   - 制定详细的重构计划，确保每一步都不会影响功能
   - 逐步执行重构，每次修改后验证功能完整性
   - 建议编写或更新测试用例来确保重构后功能正常

4. **验证要求**：
   - 重构完成后必须进行全面测试
   - 确认所有原有功能都能正常工作
   - 如发现任何功能异常，立即回滚并重新调整方案

请在开始重构前先查看规范文件内容，并制定详细的执行计划。
注意：先不要动src/icons/index.ts文件内容，这个文件不去修改。一定不要破坏功能一定不要破坏功能一定不要破坏功能！