<template>
  <div
    :class="{
      'user-comment-container-fixed': comment.fixed,
      'user-comment-container': !comment.fixed,
      'comment-flash': flashCommentId === comment.id,
    }"
  >
    <!-- 用户信息 -->
    <div class="user-info-row">
      <NAvatar
        round
        size="large"
        object-fit="cover"
        :src="comment.publisherAvatar ? fileApi.getResourceURL(comment.publisherAvatar) : ''"
      />
      <div class="user-detail-col">
        <span class="user-nickname">
          {{ comment.publisher }}
        </span>
        <span class="user-extra-info">
          {{ comment.publisherJob }} |
          <span class="time-clickable" @click="toggleTimeFormat(comment)">
            {{ comment.showExactTime ? comment.exactPublishedAt : comment.publishedAt }}
          </span>
          |
          {{ comment.ipLocation }}
        </span>
      </div>
    </div>

    <!-- 评论内容 -->
    <div class="comment-content-row">
      <TiptapEditor
        :file-bucket="COMMENT"
        v-model="comment.contentObj"
        :extensions="[...COMMENT_EXTENSIONS]"
        :editable="false"
      />
    </div>

    <!-- 评论交互栏 - 使用拆分出的控制组件 -->
    <CommentControls
      :comment="comment"
      :showReplyListBtn="showReplyListBtn"
      @show-reply-list="$emit('showReplyList', comment)"
      @handle-comment-reply-click="$emit('handleCommentReplyClick', comment)"
      @interaction-btn="(comment, actionType) => $emit('interactionBtn', comment, actionType)"
      @favorite-btn="$emit('favoriteBtn', comment)"
    />

    <!-- 评论回复栏 -->
    <Transition name="comment-reply" appear>
      <div class="comment-reply-row" v-show="commentInputVisible === comment.id">
        <TiptapEditor
          v-model="comment.quickCommentReply"
          class="comment-reply-tiptap-editor"
          :ref="(el) => el && updateEditor(comment.id, el as unknown as TiptapEditorComponentRef)"
          :editor-props="{
            attributes: {
              class: 'ProseMirrorInput',
              'data-comment-id': comment.id,
            },
          }"
          :file-bucket="COMMENT"
          :placeholder="'说是你的自由，但是...'"
          :show-character-count="true"
          :extensions="editorExtensions"
          :toolbar="true"
          @keydown.alt.enter.prevent="$emit('quickReplyComment', comment)"
        />
        <NButton
          class="comment-reply-send-btn"
          text
          type="info"
          :loading="quickReplyLoading && comment.id ? quickReplyLoading.get(comment.id) : false"
          @click="$emit('quickReplyComment', comment)"
        >
          <ArrowReplyDown24Filled :size="28" />
        </NButton>
      </div>
    </Transition>
  </div>
</template>

<script lang="ts" setup>
import { NAvatar, NButton } from 'naive-ui'
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { computed, reactive } from 'vue'

import fileApi from '@/api/file'
import CommentControls from '@/components/comment/CommentControls.vue'
import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'
import { COMMENT } from '@/constants/comment/bucket.constants'
import { COMMENT_EXTENSIONS } from '@/constants/tiptap/tiptap.constants'
import { ArrowReplyDown24Filled } from '@/icons'
import type { Comment } from '@/types/comment/comment.types'
import type { TiptapEditorComponentRef } from '@/types/component/tiptap-editor-ref.types'
import type { EditorWithFormatPainter } from '@/types/tiptap/editor-with-format-painter.types'

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const props = defineProps<{
  comment: Comment
  flashCommentId: string
  showReplyListBtn: boolean
  commentInputVisible: string
  quickReplyLoading: Map<string, boolean>
}>()

const emit = defineEmits<{
  (e: 'showReplyList', comment: Comment): void
  (e: 'handleCommentReplyClick', comment: Comment): void
  (e: 'interactionBtn', comment: Comment, actionType: number): void
  (e: 'favoriteBtn', comment: Comment): void
  (e: 'quickReplyComment', comment: Comment): void
  (e: 'updateEditor', commentId: string, editor: EditorWithFormatPainter): void
}>()

const editorExtensions = [...COMMENT_EXTENSIONS, 'characterCount']

const updateEditor = (commentId: string, el: TiptapEditorComponentRef) => {
  if (el && 'editor' in el) {
    emit('updateEditor', commentId, el.editor as EditorWithFormatPainter)
  }
}

// 切换时间显示格式
const toggleTimeFormat = (comment: Comment) => {
  if (comment.showExactTime === undefined) {
    comment.showExactTime = true
  } else {
    comment.showExactTime = !comment.showExactTime
  }
}
</script>

<style lang="scss" scoped>
@use '@/styles/comment/comment-list-item';
</style>
