import { createDiscreteApi } from 'naive-ui'
import { darkTheme } from 'naive-ui'
import { ref, watchEffect } from 'vue'

import { activeTheme, ThemeType } from '@/utils/theme/theme'

import type { NotificationProviderInst } from 'naive-ui'

// 使用watchEffect监听主题变化并重新创建通知API
const notificationRef = ref<NotificationProviderInst | null>(null)

// 创建并更新通知API
watchEffect(() => {
  const isDarkTheme = activeTheme.value === ThemeType.DARK
  const { notification } = createDiscreteApi(['notification'], {
    configProviderProps: {
      theme: isDarkTheme ? darkTheme : null,
    },
  })
  notificationRef.value = notification
})

// 添加代理以确保任何时候访问notification都能获取最新的实例
const notificationProxy = new Proxy({} as NotificationProviderInst, {
  get(_, property: string) {
    if (!notificationRef.value) {
      // 首次访问时确保已创建
      const isDarkTheme = activeTheme.value === ThemeType.DARK
      const { notification } = createDiscreteApi(['notification'], {
        configProviderProps: {
          theme: isDarkTheme ? darkTheme : null,
        },
      })
      notificationRef.value = notification
    }
    return notificationRef.value[property as keyof NotificationProviderInst]
  },
})

export default notificationProxy
