package com.shenmo.wen.app.core.article.service;

import java.io.IOException;
import java.util.List;

import com.shenmo.wen.app.core.article.pojo.domain.WenHotTag;
import com.shenmo.wen.app.core.article.pojo.req.WenArticleSaveReq;
import com.shenmo.wen.app.core.article.pojo.req.WenArticleSearchReq;
import com.shenmo.wen.app.core.article.pojo.req.WenArticleUpdateReq;
import com.shenmo.wen.app.core.article.pojo.resp.WenArticleResp;

/**
 * <AUTHOR>
 */
public interface WenArticleService {

    List<WenArticleResp> search(WenArticleSearchReq req);

    Long save(WenArticleSaveReq req);

    void edit(Long id, WenArticleUpdateReq req);

    String title(Long id);

    WenArticleResp detail(Long id);

    void download(Long id) throws IOException;

    /**
     * 切换文章发布范围
     * 
     * @param id 文章ID
     */
    void togglePublishedScope(Long id);

    /**
     * 获取热门标签
     * 
     * @param limit 返回的标签数量
     * @return 标签统计列表，按使用次数降序排序
     */
    List<WenHotTag> hotTags(int limit);

    /**
     * 删除文章及其所有关联数据
     * 
     * @param id 文章ID
     */
    void delete(Long id);
}
