package com.shenmo.wen.app.core.notification.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shenmo.wen.app.core.notification.pojo.entity.WenNotificationUserRead;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 通知用户已读关系映射器
 * 
 * <AUTHOR>
 */
public interface WenNotificationUserReadMapper extends BaseMapper<WenNotificationUserRead> {

    /**
     * 检查通知是否已读
     */
    @Select("select count(1) from wen_notification_user_read where notification_id = #{notificationId} and user_id = #{userId}")
    Integer checkRead(@Param("notificationId") Long notificationId, @Param("userId") Long userId);
}