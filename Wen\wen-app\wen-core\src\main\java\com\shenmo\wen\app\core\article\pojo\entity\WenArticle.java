package com.shenmo.wen.app.core.article.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shenmo.wen.common.mybatis.handler.TimestampToLongTypeHandler;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "wen_article",autoResultMap = true)
public class WenArticle {
    /**
     * 文章ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 文章标题
     */
    private String title;

    /**
     * 文章标签
     */
    private String tag;

    /**
     * 文章内容
     */
    private String content;

    /**
     * 文章发布人ID
     */
    private Long userId;

    /**
     * 文章发布时的IP地址
     */
    private String ip;

    /**
     * 文章发布时的IP归属地
     */
    private String ipLocation;

    /**
     * 发布时间
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class,jdbcType = JdbcType.TIMESTAMP)
    private Long publishedAt;

    /**
     * 点赞数，默认值为0
     */
    private Integer likeCount;

    /**
     * 点踩数，默认值为0
     */
    private Integer dislikeCount;

    /**
     * 收藏数，默认值为0
     */
    private Integer favoriteCount;

    /**
     * 评论数，默认值为0
     */
    private Integer commentCount;

    /**
     * 操作等级，默认值为0
     */
    private Integer operationLevel;

    /**
     * 发布范围：0-公开，1-个人<br>
     * 默认值为1
     */
    private Integer publishedScope;

    /**
     * 创建时间
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class,jdbcType = JdbcType.TIMESTAMP)
    private Long ctTm;

    /**
     * 修改时间
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class,jdbcType = JdbcType.TIMESTAMP)
    private Long mdTm;
}
