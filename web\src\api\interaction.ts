import type { InteractionResponse } from '@/types/api/interaction-response.types'
import type { RequestParams } from '@/types/api/request.types'
import { type ResponseData } from '@/types/api/response-data.types'
import api from '@/utils/api/api'

/** 互动操作请求参数类型 */
interface InteractionParams extends RequestParams {
  /** 目标ID（文章ID或评论ID） */
  targetId: string
  /** 互动类型（1: 点赞, 2: 不喜欢, 3: 分享） */
  type: number
  /** 目标类型（1: 文章, 2: 评论） */
  targetType: number
}

/** 互动切换请求参数类型 */
interface InteractionToggleParams extends RequestParams {
  /** 目标ID（文章ID或评论ID） */
  targetId: string
  /** 互动类型（1: 点赞, 2: 不喜欢, 3: 分享） */
  type: number
  /** 目标类型（1: 文章, 2: 评论） */
  targetType: number
}

/**
 * 互动相关API接口
 * 提供点赞、分享等互动功能
 */
const interactionApi = {
  /** API基础路径 */
  URL: '/core/interactions',

  /**
   * 保存互动记录
   * 创建新的互动记录（点赞、分享等）
   * @param params 互动参数，包含目标ID、互动类型等
   * @returns 返回互动操作结果
   */
  save: async (params: InteractionParams): Promise<ResponseData<InteractionResponse>> => {
    const response = await api.post<ResponseData<InteractionResponse>>(interactionApi.URL, params)
    return response.data
  },

  /**
   * 切换互动状态
   * 切换用户对目标内容的互动状态（如点赞/取消点赞）
   * @param params 互动切换参数，包含目标ID、互动类型等
   * @returns 返回切换后的互动状态和统计信息
   */
  toggle: async (params: InteractionToggleParams): Promise<ResponseData<InteractionResponse>> => {
    const response = await api.post<ResponseData<InteractionResponse>>(interactionApi.URL, params)
    return response.data
  },
}

export default interactionApi
