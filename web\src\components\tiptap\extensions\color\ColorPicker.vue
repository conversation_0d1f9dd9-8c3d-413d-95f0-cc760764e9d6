<template>
  <TiptapBtn
    :icon="icon"
    :show="show"
    :trigger="(e) => trigger(type, e)"
    :is-active="isActiveStyle"
    :tooltip="tooltipText"
  >
    <template #content>
      <NColorPicker
        :show-preview="false"
        size="small"
        placement="top"
        to="body"
        v-model:value="colorValue"
        :popover-style="popoverStyle"
        :render-label="renderLabel"
        :swatches="swatches"
        :show="isVisible"
        @update:value="handleColorChange"
      >
      </NColorPicker>
    </template>
  </TiptapBtn>
</template>

<script lang="ts" setup>
import { NColorPicker } from 'naive-ui'
import { ref, computed, onUnmounted, nextTick } from 'vue'

import TiptapBtn from '@/components/tiptap/toolbar/TiptapBtn.vue'
import { Color24Regular, TextColor24Regular } from '@/icons'

const props = defineProps({
  show: {
    type: Boolean,
    default: true,
  },
  type: {
    type: String,
    required: true,
  },
  editor: {
    type: Object,
    required: true,
  },
  colorType: {
    type: String,
    default: 'color', // 'color' 或 'backgroundColor'
  },
  tooltip: {
    type: String,
    default: '',
  },
})

const icon = computed(() => {
  return props.colorType === 'color' ? TextColor24Regular : Color24Regular
})

const tooltipText = computed(() => {
  if (props.tooltip) {
    return props.tooltip
  }
  return props.colorType === 'color' ? '文字颜色' : '背景色'
})

const isActiveStyle = computed(() => {
  if (!props.editor) return false

  try {
    if (props.colorType === 'color') {
      // 检查textStyle标记是否激活并有color属性
      const hasColor = props.editor.isActive('textStyle')
      const textStyleAttrs = props.editor.getAttributes('textStyle')
      return hasColor && !!textStyleAttrs.color
    } else {
      // 检查highlight标记是否激活
      return props.editor.isActive('highlight')
    }
  } catch (error) {
    console.error('Error checking active style:', error)
    return false
  }
})

const colorState = ref({
  visible: false,
  value: '#000000FF',
  type: '',
})

const colorValue = computed({
  get: () => colorState.value.value,
  set: (val) => {
    colorState.value.value = val
  },
})

const isVisible = computed(() => colorState.value.visible && colorState.value.type === props.type)

const renderLabel = () => ''
const swatches = ['#FFFFFF', '#18A058', '#2080F0', '#F0A020', 'rgba(208, 48, 80, 1)']
const popoverStyle = 'min-width: 220px; z-index: 10001; pointer-events: auto;'

// 当前触发按钮的引用
let currentTriggerButton: HTMLElement | null = null

const trigger = (type: string, event: MouseEvent) => {
  // 阻止事件冒泡，防止立即触发外部点击
  event.stopPropagation()

  // 记录当前触发按钮
  currentTriggerButton = event.currentTarget as HTMLElement

  const wasVisible = colorState.value.visible && colorState.value.type === type
  colorState.value.visible = !wasVisible
  colorState.value.type = type

  if (colorState.value.visible) {
    // 延迟添加外部点击监听器，避免立即触发
    nextTick(() => {
      setTimeout(() => {
        document.addEventListener('click', handleOutsideClick, true)
        document.addEventListener('mousedown', handleOutsideClick, true)
      }, 0)
    })
  } else {
    // 颜色选择器关闭时，取消内容选中，表示颜色设置完毕
    closeColorPicker()
  }
}

// 关闭颜色选择器并取消内容选中
const closeColorPicker = () => {
  removeOutsideClickListener()
  // 取消选中，将光标移到选择区域末尾
  const selection = props.editor?.state.selection
  if (selection && !selection.empty) {
    props.editor?.chain().setTextSelection(selection.to).run()
  }
}

// 处理外部点击事件
const handleOutsideClick = (event: Event) => {
  const target = event.target as HTMLElement

  // 检查是否点击了颜色选择器内部
  const colorPickerElements = document.querySelectorAll(
    '.n-color-picker, .n-color-picker-panel, .n-popover',
  )
  let clickedInside = false

  colorPickerElements.forEach((element) => {
    if (element.contains(target)) {
      clickedInside = true
    }
  })

  // 检查是否点击了当前触发按钮
  if (currentTriggerButton && currentTriggerButton.contains(target)) {
    return // 不关闭，让按钮的点击事件处理
  }

  // 如果点击在颜色选择器外部，关闭选择器
  if (!clickedInside) {
    colorState.value.visible = false
    closeColorPicker()
  }
}

// 移除外部点击监听器
const removeOutsideClickListener = () => {
  document.removeEventListener('click', handleOutsideClick, true)
  document.removeEventListener('mousedown', handleOutsideClick, true)
  currentTriggerButton = null
}

const handleColorChange = (color: string) => {
  if (props.colorType === 'color') {
    props.editor?.chain().setColor(color).run()
  } else {
    props.editor?.chain().setHighlight({ color }).run()
  }
}

// 组件卸载时清理事件监听器
onUnmounted(() => {
  removeOutsideClickListener()
})
</script>

<style lang="scss" scoped>
@use '@/styles/tiptap/image';

// 确保颜色选择器有足够高的层级
:deep(.n-color-picker-panel) {
  z-index: 10002;
}
</style>
