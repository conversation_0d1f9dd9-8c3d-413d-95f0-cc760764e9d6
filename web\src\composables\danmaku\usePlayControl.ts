import type { <PERSON><PERSON> } from '@/types/danmaku/danmu.types'

import type { Ref } from 'vue'

export function usePlayControl(
  dmContainer: Ref<HTMLDivElement>,
  danmuList: Ref<Danmu[]>,
  index: Ref<number>,
  paused: Ref<boolean>,
  hidden: Ref<boolean>,
  danChannel: Record<string, unknown>,
  timer: Ref<number>,
  draw: () => void,
  debounce: number | Ref<number>,
) {
  /**
   * 播放弹幕
   */
  function play() {
    paused.value = false
    if (!timer.value) {
      const debounceValue = debounce instanceof Object ? debounce.value : debounce
      timer.value = window.setInterval(() => draw(), debounceValue)
    }
  }

  /**
   * 清除定时器
   */
  function clearTimer() {
    clearInterval(timer.value)
    timer.value = 0
  }

  /**
   * 清空弹幕
   */
  function clear() {
    clearTimer()
    index.value = 0
  }

  /**
   * 停止弹幕
   */
  function stop() {
    Object.assign(dan<PERSON>hannel, {})
    dmContainer.value.innerHTML = ''
    paused.value = true
    hidden.value = false
    clear()
  }

  /**
   * 暂停弹幕
   */
  function pause(): void {
    paused.value = true
  }

  /**
   * 显示弹幕
   */
  function show(): void {
    hidden.value = false
  }

  /**
   * 隐藏弹幕
   */
  function hide(): void {
    hidden.value = true
  }

  /**
   * 获取播放状态
   */
  function getPlayState(): boolean {
    return !paused.value
  }

  return {
    play,
    clearTimer,
    clear,
    stop,
    pause,
    show,
    hide,
    getPlayState,
  }
}
