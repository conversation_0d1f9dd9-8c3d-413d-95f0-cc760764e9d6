import type { JSONContent } from '@tiptap/vue-3'

export interface Comment {
  id: string
  content: string
  contentObj: JSONContent
  articleId: string
  parentCommentId: string
  publisher: string
  publisherAvatar: string
  publisherJob: string
  isOwner: boolean
  ipLocation: string
  publishedAt: string
  likeCount: number
  isLike: boolean
  dislikeCount: number
  isDislike: boolean
  replyCount: number
  favoriteCount: number
  isFavorite: boolean
  fixed: boolean
  quickCommentReply: JSONContent | undefined
  exactPublishedAt?: string
  showExactTime?: boolean
}
