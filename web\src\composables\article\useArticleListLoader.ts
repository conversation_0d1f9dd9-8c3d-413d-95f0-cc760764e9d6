import { nextTick, type Ref, type ComputedRef } from 'vue'

import articleApi from '@/api/article'
import fileApi from '@/api/file'
import type { ArticleList } from '@/types/article/article-list.types'
import type { Article } from '@/types/article/article.types'
import type { SearchCondition } from '@/types/search/search-condition.types'
import logger from '@/utils/log/log'
import tiptap from '@/utils/tiptap/tiptap'

/**
 * 文章列表加载器组合式函数返回值类型
 */
export interface UseArticleListLoaderReturn {
  /** 重置列表 */
  resetList: () => void
  /** 加载文章 */
  loadArticles: (loadMore?: boolean, signal?: AbortSignal, forceLoadSize?: number) => Promise<void>
}

/**
 * 文章列表加载器组合式函数
 * 提供文章列表的数据加载功能
 */
export function useArticleListLoader(
  searchCondition: Ref<SearchCondition>,
  articleList: Ref<Article[]>,
  loading: Ref<boolean>,
  noMore: Ref<boolean>,
  currentLoadedArticlesCount: Ref<number>,
  calculatedLoadSize: ComputedRef<number>,
): UseArticleListLoaderReturn {
  // 重置文章列表
  const resetList = (): void => {
    articleList.value = []
    noMore.value = false
    // 重置已加载数量
    currentLoadedArticlesCount.value = 0
    loadArticles()
  }

  // 获取资源URL
  const getResourceURL = (uri: string): string => {
    return fileApi.getResourceURL(uri)
  }

  // 添加文章到列表
  const addArticles = (list: Article[]): boolean => {
    const existingArticleIds = new Set(articleList.value.map((article) => article.id))
    const newArticles = list.filter((article) => !existingArticleIds.has(article.id))

    if (newArticles.length === 0) return false

    const processedList = newArticles.map((article) => ({
      ...article,
      contentObj: tiptap.toJsonObject(article.content),
      publisherAvatar: getResourceURL(article.publisherAvatar),
      tags: article.tag?.split(',') || [],
    }))

    articleList.value = [...articleList.value, ...processedList]
    return true
  }

  // 加载文章
  const loadArticles = (
    loadMore = false,
    signal?: AbortSignal,
    forceLoadSize?: number,
  ): Promise<void> => {
    logger.debug('loadArticles called:', {
      loadMore,
      loading: loading.value,
      noMore: noMore.value,
      searchCondition: searchCondition,
      forceLoadSize,
    })
    // 使用 forceLoadSize 或计算属性的加载大小
    const loadSize = forceLoadSize !== undefined ? forceLoadSize : calculatedLoadSize.value

    if (loading.value || noMore.value) {
      logger.debug('loadArticles early return:', { loading: loading.value, noMore: noMore.value })
      return Promise.resolve()
    }

    loading.value = true

    const lastArticle =
      articleList.value.length > 0 ? articleList.value[articleList.value.length - 1] : null
    const requestId = lastArticle?.id

    return new Promise<void>((resolve, reject) => {
      nextTick(() => {
        const searchParams = { ...(searchCondition.value || {}), id: requestId, loadSize: loadSize }
        logger.debug('Making API request with params:', searchParams)

        articleApi
          .search(searchParams, signal)
          .then((res) => {
            logger.debug('API response received:', res)
            if (!res || !res.data) {
              resolve()
              return
            }

            const data = res.data as ArticleList
            if (data.length === 0) {
              noMore.value = true
              resolve()
              return
            }

            if (data.length < loadSize) {
              noMore.value = true
            }

            const newArticlesAdded = addArticles(data)

            if (!newArticlesAdded) {
              noMore.value = true
              resolve()
              return
            }
            // 更新已加载文章数量
            currentLoadedArticlesCount.value += data.length
          })
          .catch((error) => {
            if (error.name === 'CanceledError' || error.message === 'canceled') {
              resolve()
              return
            }

            logger.error('加载文章失败:', error)
            noMore.value = false
            if (!signal?.aborted) {
              reject(error)
            } else {
              resolve()
            }
          })
          .finally(() => {
            if (!signal?.aborted) {
              loading.value = false
            }
          })
      })
    })
  }

  return {
    resetList,
    loadArticles,
  }
}
