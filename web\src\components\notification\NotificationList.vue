<template>
  <div class="notification-list-container">
    <n-data-table
      class="notification-table"
      :remote="true"
      :loading="loading"
      :data="notifications"
      :row-props="rowProps"
      :columns="notificationColumns"
      :bordered="false"
    />
    <NPagination
      class="notification-pagination"
      :page="pagination.page"
      :page-size="pagination.pageSize"
      :show-size-picker="pagination.showSizePicker"
      :show-quick-jumper="pagination.showQuickJumper"
      :page-slot="pagination.pageSlot"
      :page-sizes="pagination.pageSizes"
      :size="pagination.size"
      :show-quick-jump-dropdown="pagination.showQuickJumpDropdown"
      :prefix="pagination.prefix"
      :suffix="pagination.suffix"
      :itemCount="pagination.itemCount"
      @update:page="pagination.onUpdatePage"
      @update:page-size="pagination.onUpdatePageSize"
    />
  </div>
</template>

<script lang="ts" setup>
import { NDataTable, NPagination, NPop<PERSON>, <PERSON><PERSON><PERSON>ge, <PERSON><PERSON>utton } from 'naive-ui'
import { ref, watch, onMounted, h } from 'vue'

import notificationApi from '@/api/notification'
import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'
import { COMMENT } from '@/constants/comment/bucket.constants'
import { COMMENT_EXTENSIONS } from '@/constants/tiptap/tiptap.constants'
import { ArrowRight20Filled } from '@/icons'
import type {
  NotificationListResponse,
  NotificationListItem,
} from '@/types/api/notification-list-response.types'
import type { NotificationListEmits } from '@/types/component/notification-list-emits.types'
import type { NotificationListProps } from '@/types/component/notification-list-props.types'
import type { PaginationInfo } from '@/types/ui/pagination-info.types'
import dateTime from '@/utils/date/date-time'
import logger from '@/utils/log/log'
import tiptap from '@/utils/tiptap/tiptap'

import type { PaginationProps } from 'naive-ui'

// 定义组件 Props，使用明确的类型定义
const props = defineProps<NotificationListProps>()

// 定义组件 Emits，使用明确的类型定义
const emit = defineEmits<NotificationListEmits>()

const loading = ref(false)
const notifications = ref<NotificationListItem[]>([])
const commentFlag = '评论了：'

// 分页配置
const pageSizes = [
  { label: '', value: 5 },
  { label: '', value: 10 },
  { label: '', value: 15 },
]
pageSizes.forEach((item) => {
  item.label = `${item.value}/页`
})

const pagination = ref<PaginationProps>({
  page: 1,
  pageSize: 5,
  showSizePicker: true,
  showQuickJumper: false,
  pageSlot: 5,
  pageSizes,
  size: 'medium',
  showQuickJumpDropdown: false,
  prefix: (info: PaginationInfo) => {
    return h('span', `第 ${info.page} 页 `)
  },
  suffix: (info: PaginationInfo) => {
    return h('span', `共 ${info.itemCount} 条`)
  },
  onUpdatePage: (page: number) => {
    pagination.value.page = page
    loadNotificationPage()
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
    loadNotificationPage()
  },
})

// 表格列定义
const notificationColumns = [
  {
    title: '通知时间',
    key: 'ctTm',
    width: 162,
  },
  {
    title: '通知内容',
    key: 'content',
    ellipsis: true,
    render(row: NotificationListItem) {
      const commentId = row.commentId
      const content = row.content
      const index = content.indexOf(commentFlag)
      return h('span', [
        h(NBadge, {
          style: 'position: absolute;',
          dot: row.isRead === 0,
          offset: [-4, 0],
        }),
        h(
          NPopover,
          {
            trigger: 'click',
            placement: 'top-start',
            style: 'max-width:min(555px,84vw); margin-left:min(-180px,40vw)',
            flip: false,
          },
          {
            trigger: () =>
              h(
                'span',
                {
                  class: 'cursor-pointer notification-content',
                  onClick: (e: Event) => e.stopPropagation(),
                },
                commentId
                  ? content.substring(0, index + commentFlag.length) +
                      tiptap.serializeContent(
                        safeJsonParse(content.substring(index + commentFlag.length)),
                      )
                  : row.content,
              ),
            default: () =>
              h('div', { class: 'notification-popover-content' }, [
                commentId
                  ? h('div', { style: 'margin: 10px' }, [
                      content.substring(0, index + commentFlag.length),
                      h(TiptapEditor, {
                        fileBucket: COMMENT,
                        modelValue: safeJsonParse(content.substring(index + commentFlag.length)),
                        extensions: [...COMMENT_EXTENSIONS],
                        editable: false,
                      }),
                    ])
                  : h('div', row.content),
                h(
                  NButton,
                  {
                    style: 'margin-left:auto',
                    class: 'flex-column-end',
                    text: true,
                    type: 'primary',
                    onClick: () => {
                      handleNotificationClick(row)
                    },
                  },
                  ['让我看看', h(ArrowRight20Filled, { size: 16 })],
                ),
              ]),
          },
        ),
      ])
    },
  },
]

// 行属性
const rowProps = (_row: NotificationListItem) => {
  return {}
}

// 处理通知点击
const handleNotificationClick = (notification: NotificationListItem) => {
  emit('notification-click', notification)
}

// 加载通知数据
const loadNotificationPage = () => {
  loading.value = true

  // 加载通知列表
  notificationApi
    .load({
      pageNum: pagination.value.page,
      pageSize: pagination.value.pageSize,
    })
    .then((res) => {
      const data = res?.data
      if (data) {
        // 处理时间格式
        const notificationData: NotificationListResponse = data
        notificationData.rows?.forEach((e: NotificationListItem) => {
          e.ctTm = dateTime.toTimeString(e.ctTm)
        })
        pagination.value.itemCount = notificationData.totalRows || 0
        pagination.value.pageCount = notificationData.totalPage || 0
        notifications.value = notificationData.rows || []
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 监听可见性变化，当显示时加载通知 - 确保在loadNotificationPage定义之后
watch(
  () => props.visible,
  (newValue) => {
    if (newValue) {
      loadNotificationPage()
    }
  },
  { immediate: true }, // 立即触发一次监听器
)

// 组件挂载时，如果可见则加载数据
onMounted(() => {
  if (props.visible) {
    loadNotificationPage()
  }
})

// 导出加载方法，供外部调用
defineExpose({
  loadNotificationPage,
  resetPagination: () => {
    // 重置分页到第一页
    pagination.value.page = 1
    pagination.value.pageSize = 5
  },
})

const safeJsonParse = (content: string) => {
  if (!content) {
    return { type: 'doc', content: [{ type: 'paragraph', content: [] }] }
  }

  try {
    // 正常情况下使用tiptap处理
    return tiptap.toJsonObject(content)
  } catch (error) {
    // 如果解析失败，创建一个包含原始内容的文本结构
    logger.error('通知内容JSON解析失败:', error as Error)
    return {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [
            {
              type: 'text',
              text: typeof content === 'string' ? content : '内容无法显示',
            },
          ],
        },
      ],
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/styles/notification/notification-btn-modal';
</style>
