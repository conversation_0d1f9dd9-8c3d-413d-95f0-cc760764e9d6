import { computed } from 'vue'

import type { DanmakuEmitFunction } from '@/types/danmaku/danmaku-model-binding.types'
import type { DanmakuModelProps } from '@/types/danmaku/danmaku-model-props.types'
import type { DanmakuValueTranslater } from '@/types/danmaku/danmaku-value-translater.types'

/**
 * 创建双向绑定数据模型
 * 用于实现类似v-model的Props双向绑定功能
 *
 * @param props 组件属性对象
 * @param emit 事件发射函数
 * @param name 属性名称，默认为modelValue
 * @param translater 可选的数据转换函数
 * @returns 响应式计算属性
 */
export function createDanmuModelBinding<T>(
  props: DanmakuModelProps,
  emit: DanmakuEmitFunction,
  name = 'modelValue',
  translater?: DanmakuValueTranslater,
) {
  return computed<T>({
    get: () => props[name] as T,
    set: (value: T) => {
      emit(`update:${name}`, translater ? translater(value) : value)
    },
  })
}
