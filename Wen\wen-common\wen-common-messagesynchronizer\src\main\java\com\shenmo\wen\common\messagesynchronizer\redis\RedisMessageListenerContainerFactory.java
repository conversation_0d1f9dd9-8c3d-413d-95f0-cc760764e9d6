package com.shenmo.wen.common.messagesynchronizer.redis;

import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.Topic;
import org.springframework.lang.NonNull;

import java.util.Arrays;

/**
 * Redis 消息监听器容器工厂
 * <p>
 * {@link RedisMessageListenerContainer}
 *
 * <AUTHOR>
 */
public class RedisMessageListenerContainerFactory {

    /**
     * redis连接工厂
     */
    private final RedisConnectionFactory redisConnectionFactory;


    /**
     * 私有构造方法
     * <p>
     * 使用{@link #of(RedisConnectionFactory)}
     *
     * @param redisConnectionFactory redis连接工厂
     * <AUTHOR>
     */
    private RedisMessageListenerContainerFactory(@NonNull RedisConnectionFactory redisConnectionFactory) {
        this.redisConnectionFactory = redisConnectionFactory;
    }

    /**
     * 静态工厂方法构建{@link RedisMessageListenerContainerFactory}
     *
     * @param redisConnectionFactory redis连接工厂
     * <AUTHOR>
     */
    public static RedisMessageListenerContainerFactory of(@NonNull RedisConnectionFactory redisConnectionFactory) {

        return new RedisMessageListenerContainerFactory(redisConnectionFactory);
    }

    /**
     * 构建{@link RedisMessageListenerContainer}
     *
     * @param keyMessageListener 抽象的redis key消息监听器
     * @return Redis 消息监听器容器
     * <AUTHOR>
     */
    public RedisMessageListenerContainer build(AbstractRedisMessageListener<?> keyMessageListener) {

        return build(keyMessageListener, new ChannelTopic(keyMessageListener.getChannel()));
    }


    /**
     * 获取一个初始的{@link RedisMessageListenerContainer}
     *
     * @return Redis 消息监听器容器
     * <AUTHOR>
     */
    public RedisMessageListenerContainer get() {

        RedisMessageListenerContainer redisMessageListenerContainer = new RedisMessageListenerContainer();
        redisMessageListenerContainer.setConnectionFactory(redisConnectionFactory);
        return redisMessageListenerContainer;
    }

    /**
     * 构建{@link RedisMessageListenerContainer}
     *
     * @param keyMessageListener 抽象的redis key消息监听器
     * @param topics             redis 消息主题
     * @return Redis 消息监听器容器
     * <AUTHOR>
     */
    public RedisMessageListenerContainer build(AbstractRedisMessageListener<?> keyMessageListener, Topic... topics) {

        RedisMessageListenerContainer redisMessageListenerContainer = new RedisMessageListenerContainer();
        redisMessageListenerContainer.setConnectionFactory(redisConnectionFactory);
        redisMessageListenerContainer.addMessageListener(keyMessageListener, Arrays.asList(topics));
        return redisMessageListenerContainer;
    }

    /**
     * 启动{@link RedisMessageListenerContainer}
     *
     * @param keyMessageListener 抽象的redis key消息监听器
     * <AUTHOR>
     */
    public void start(AbstractRedisMessageListener<?> keyMessageListener) {
        final RedisMessageListenerContainer build = build(keyMessageListener, new ChannelTopic(keyMessageListener.getChannel()));
        build.afterPropertiesSet();
        build.start();
    }
}
