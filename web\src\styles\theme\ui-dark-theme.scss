/* UI 组件深色模式样式 */

/* 输入框和滚动条在深色模式下的样式 */
.dark-theme .n-input {
  background-color: var(--white-2);
  color: var(--black);
}

.dark-theme .n-scrollbar-thumb {
  background-color: var(--gray-4);
}

/* TipTap编辑器在深色模式下的样式 */
.dark-theme .ProseMirrorInput {
  background-color: var(--prosemirror-input-bg);
  border-color: var(--gray-3);
  color: var(--black);
}

/* 全屏模式下编辑器的暗色主题 */
.dark-theme .tiptap-fullscreen {
  background-color: var(--white-2);
  color: var(--black);
}

/* 全屏模式下编辑器内容区的暗色主题 */
.dark-theme .tiptap-fullscreen .editor-content {
  background-color: var(--white-2);
}

/* 确保评论输入区域的TipTap编辑器背景与容器一致 */
.dark-theme .comment-input-row .tiptap-editor-wrapper .editor-content,
.dark-theme .comment-reply-row .tiptap-editor-wrapper .editor-content {
  background-color: var(--white-1);
}

/* 确保评论内容的TipTap编辑器背景与评论容器一致 */
.dark-theme .comment-content-row .tiptap-editor-wrapper .editor-content {
  background-color: var(--white-2);
}

/* 确保编辑器内容区的背景色与其父容器一致 */
.dark-theme .tiptap-editor-wrapper .editor-content {
  background-color: transparent;
}

.dark-theme .tiptap-toolbar {
  background-color: var(--white-2);
  border-color: var(--gray-3);
}

.dark-theme .tiptap-toolbar button {
  color: var(--black);
}

.dark-theme .tiptap-toolbar button:hover {
  background-color: var(--gray-3);
}

/* 编辑器字数计数器颜色 */
.dark-theme .character-count {
  color: var(--gray-5);
  background-color: var(--dark-gray);
}

/* 快捷回复框中的字数计数器 */
.dark-theme .comment-reply-row .character-count {
  background-color: var(--deep-gray);
}

/* 首页样式在深色模式下 */
.dark-theme .common-layout-top {
  background-color: var(--creamy-white-2);
  color: var(--black);
}

.dark-theme .common-layout-content {
  background-color: var(--creamy-white-1);
}

.dark-theme .online-info,
.dark-theme .search-container {
  color: var(--black);
}

.dark-theme .n-tabs .n-tab {
  color: var(--black);
}

.dark-theme .n-tabs .n-tab--active {
  color: var(--blue);
}

.dark-theme .n-tabs-tab-wrapper {
  border-bottom-color: var(--gray-3);
}

/* 用户信息弹框在深色模式下的样式 */
.dark-theme .user-info {
  background-color: var(--white-2);
  color: var(--black);
}

.dark-theme .info-row {
  border-color: var(--gray-3);
}

/* 深色主题下的开关样式 */
.dark-theme .n-switch {
  --n-rail-color: var(--white-1);
  --n-rail-color-active: var(--blue);
}

/* 确保所有卡片和容器在深色模式下有正确的背景色 */
.dark-theme .n-card {
  background-color: var(--white-2);
  color: var(--black);
}

.dark-theme .n-card-header {
  border-bottom-color: var(--gray-3);
}

.dark-theme .n-modal {
  background-color: var(--white-2);
  color: var(--black);
}

.dark-theme .n-drawer-content {
  background-color: var(--white-2);
  color: var(--black);
}

.dark-theme .n-dialog {
  background-color: var(--white-2);
  color: var(--black);
}

/* 对话框标题在暗色模式下的样式 */
.dark-theme .n-dialog__title {
  color: var(--black-contrast);
}

.dark-theme .n-popover {
  background-color: var(--white-2);
  color: var(--black);
}

.dark-theme .n-dropdown-menu {
  background-color: var(--white-2);
  color: var(--black);
}

.dark-theme .n-dropdown-option {
  color: var(--black);
}

.dark-theme .n-dropdown-option:hover {
  background-color: var(--gray-3);
}

/* 按钮和交互元素在深色模式下 */
.dark-theme .n-button--ghost {
  border-color: var(--gray-3);
  color: var(--black);
}

.dark-theme .n-button--ghost:hover {
  border-color: var(--blue);
  color: var(--blue);
}

.dark-theme .n-button--text {
  color: var(--blue);
}

/* 确保登录页面在深色模式下正确显示 */
.dark-theme .layout-container {
  background-color: var(--creamy-white-3);
}

.dark-theme .login-form-ipt,
.dark-theme .register-form-ipt {
  background-color: var(--white-1);
  color: var(--black);
}
