// 主题颜色配置 - 自动生成，请勿手动修改
// 此文件由 scripts/sync-theme-colors.js 从 SCSS 变量同步生成
// 最后更新时间: 2025-07-27T09:41:18.674Z

/**
 * 主题颜色接口定义
 * 包含所有可用的颜色变量名称和类型
 */
export interface ThemeColors {
  white: string
  'white-1': string
  'white-2': string
  'creamy-white-1': string
  'creamy-white-2': string
  'creamy-white-3': string
  black: string
  'black-contrast': string
  'dark-gray': string
  'dark-gray-1': string
  'deep-gray': string
  'gray-1': string
  'gray-2': string
  'gray-3': string
  'gray-4': string
  'gray-5': string
  green: string
  blue: string
  'blue-light': string
  purple: string
  'purple-contrast': string
  'purple-light': string
  'yellow-contrast': string
  yellow: string
  'yellow-light': string
  red: string
  'red-light': string
  'border-1': string
  shadow: string
  'code-text': string
  'code-comment': string
  'code-keyword': string
  'code-string': string
  'code-number': string
  'code-function': string
  'code-variable': string
  'code-tag': string
  'code-attribute': string
  'code-builtin': string
  'code-meta': string
  'code-deletion-color': string
  'code-deletion-bg': string
  'code-addition-color': string
  'code-addition-bg': string
}

/**
 * 亮色主题颜色配置
 * 包含所有亮色模式下的颜色值
 */
export const lightThemeColors: ThemeColors = {
  white: '#fff',
  'white-1': '#f0f0f0',
  'white-2': '#ddd',
  'creamy-white-1': '#eeece4',
  'creamy-white-2': '#e4e1d8',
  'creamy-white-3': '#dcd8ca',
  black: '#2e2b29',
  'black-contrast': '#110f0e',
  'dark-gray': '#191919',
  'dark-gray-1': '#202020',
  'deep-gray': '#111',
  'gray-1': 'rgba(61, 37, 20, 0.05)',
  'gray-2': 'rgba(61, 37, 20, 0.08)',
  'gray-3': 'rgba(61, 37, 20, 0.12)',
  'gray-4': 'rgba(53, 38, 28, 0.3)',
  'gray-5': 'rgba(28, 25, 23, 0.6)',
  green: '#22c55e',
  blue: '#4ba3fd',
  'blue-light': '#e6f3ff',
  purple: '#6a00f5',
  'purple-contrast': '#5800cc',
  'purple-light': 'rgba(88, 5, 255, 0.05)',
  'yellow-contrast': '#facc15',
  yellow: 'rgba(250, 204, 21, 0.4)',
  'yellow-light': '#fffae5',
  red: '#ff5c33',
  'red-light': '#ffebe5',
  'border-1': '0.1rem solid rgba(61, 37, 20, 0.12)',
  shadow: '0 0.25rem 0.6rem rgba(0, 0, 0, 0.1)',
  'code-text': '#24292e',
  'code-comment': '#6a737d',
  'code-keyword': '#d73a49',
  'code-string': '#032f62',
  'code-number': '#005cc5',
  'code-function': '#6f42c1',
  'code-variable': '#e36209',
  'code-tag': '#22863a',
  'code-attribute': '#6f42c1',
  'code-builtin': '#005cc5',
  'code-meta': '#6a737d',
  'code-deletion-color': '#b31d28',
  'code-deletion-bg': '#ffeef0',
  'code-addition-color': '#22863a',
  'code-addition-bg': '#f0fff4',
}

/**
 * 暗色主题颜色配置
 * 包含所有暗色模式下的颜色值
 */
export const darkThemeColors: ThemeColors = {
  white: '#121212',
  'white-1': '#242424',
  'white-2': '#363636',
  'creamy-white-1': '#1a1a1a',
  'creamy-white-2': '#262626',
  'creamy-white-3': '#333',
  black: '#e0e0e0',
  'black-contrast': '#fff',
  'dark-gray': '#191919',
  'dark-gray-1': '#202020',
  'deep-gray': '#111',
  'gray-1': 'rgba(200, 200, 200, 0.05)',
  'gray-2': 'rgba(200, 200, 200, 0.08)',
  'gray-3': 'rgba(200, 200, 200, 0.12)',
  'gray-4': 'rgba(200, 200, 200, 0.3)',
  'gray-5': 'rgba(200, 200, 200, 0.6)',
  green: '#22c55e',
  blue: '#4ba3fd',
  'blue-light': '#2a3745',
  purple: '#9d6dff',
  'purple-contrast': '#8a5cf5',
  'purple-light': 'rgba(154, 92, 255, 0.15)',
  'yellow-contrast': '#facc15',
  yellow: 'rgba(250, 204, 21, 0.4)',
  'yellow-light': '#3f3a14',
  red: '#ff5c33',
  'red-light': '#3d1a12',
  'border-1': '0.1rem solid rgba(200, 200, 200, 0.12)',
  shadow: '0 0.25rem 0.6rem rgba(255, 255, 255, 0.1)',
  'code-text': '#e6edf3',
  'code-comment': '#8b949e',
  'code-keyword': '#ff7b72',
  'code-string': '#a5d6ff',
  'code-number': '#79c0ff',
  'code-function': '#d2a8ff',
  'code-variable': '#ffa657',
  'code-tag': '#7ee787',
  'code-attribute': '#d2a8ff',
  'code-builtin': '#79c0ff',
  'code-meta': '#8b949e',
  'code-deletion-color': '#ffa198',
  'code-deletion-bg': '#490202',
  'code-addition-color': '#56d364',
  'code-addition-bg': '#0f5132',
}

/**
 * 获取指定主题的颜色配置
 * @param isDark 是否为暗色主题
 * @returns 对应主题的颜色配置对象
 */
export function getThemeColors(isDark: boolean): ThemeColors {
  return isDark ? darkThemeColors : lightThemeColors
}

/**
 * 将颜色对象转换为 CSS 变量对象
 * @param colors 颜色配置对象
 * @returns CSS 变量对象，键名带有 -- 前缀
 */
export function colorsToCSS(colors: ThemeColors): Record<string, string> {
  const cssVars: Record<string, string> = {}

  for (const [key, value] of Object.entries(colors)) {
    cssVars[`--${key}`] = value
  }

  return cssVars
}

/**
 * 将颜色配置应用到 DOM 根元素
 * @param colors 颜色配置对象
 */
export function applyColorsToDOM(colors: ThemeColors): void {
  const root = document.documentElement
  const cssVars = colorsToCSS(colors)

  for (const [property, value] of Object.entries(cssVars)) {
    root.style.setProperty(property, value)
  }
}

/**
 * 获取指定颜色的值
 * @param colorName 颜色名称
 * @param isDark 是否为暗色主题
 * @returns 颜色值，如果不存在则返回 undefined
 */
export function getColorValue(colorName: keyof ThemeColors, isDark: boolean): string | undefined {
  const colors = getThemeColors(isDark)
  return colors[colorName]
}
