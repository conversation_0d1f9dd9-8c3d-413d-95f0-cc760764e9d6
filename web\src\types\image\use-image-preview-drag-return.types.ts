import type { ImagePreviewDragState } from './image-preview-drag-state.types'

/**
 * 图片预览拖拽组合式函数返回值接口类型定义
 */
export interface UseImagePreviewDragReturn {
  /** 拖拽状态 */
  state: ImagePreviewDragState
  /** 滚轮缩放处理 */
  handleWheelZoom: (e: WheelEvent) => void
  /** 初始化拖拽功能 */
  initialize: () => void
  /** 清理拖拽功能 */
  cleanup: () => void
  /** 重置变换状态 */
  resetTransform: () => void
  /** 更新图片变换 */
  updateTransform: () => void
}
