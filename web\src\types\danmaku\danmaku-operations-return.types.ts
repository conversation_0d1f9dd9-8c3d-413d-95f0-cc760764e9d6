/**
 * 弹幕操作组合式函数返回值类型定义
 */

import type { DanmakuInsertData, DanmakuAddData } from './danmaku-insert-data.types'

/**
 * 弹幕操作组合式函数返回值接口
 */
export interface UseDanmakuOperationsReturn {
  /** 绘制弹幕 */
  draw: () => void
  /** 插入弹幕 */
  insert: (data?: DanmakuInsertData) => void
  /** 添加弹幕到当前位置 */
  add: (danmu: DanmakuAddData) => void
  /** 添加弹幕到末尾 */
  push: (danmu: DanmakuAddData) => void
  /** 清除定时器 */
  clearTimer: () => void
  /** 播放弹幕 */
  play: () => void
  /** 暂停弹幕 */
  pause: () => void
  /** 停止弹幕 */
  stop: () => void
  /** 显示弹幕 */
  show: () => void
  /** 隐藏弹幕 */
  hide: () => void
  /** 重置弹幕 */
  reset: () => void
  /** 获取播放状态 */
  getPlayState: () => boolean
}
