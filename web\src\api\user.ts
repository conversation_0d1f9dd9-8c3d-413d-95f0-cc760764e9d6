import { type ResponseData } from '@/types/api/response-data.types'
import api from '@/utils/api/api'

/** 用户信息响应数据类型，与后端WenUserResp保持一致 */
interface UserInfoResponse {
  /** 用户ID */
  id: string
  /** 用户名 */
  username: string
  /** 手机号 */
  phone: string
  /** 邮箱地址 */
  email: string
  /** 头像URL */
  avatar: string
  /** IP归属地 */
  ipLocation: string
  /** 职业信息 */
  job: string
  /** 用户等级 */
  level: number
  /** 用户经验值 */
  experience: number
  /** 通知接收类型 */
  notificationReceiveType: number
  /** 创建时间 */
  ctTm: string
  /** 修改时间 */
  mdTm: string
}

/**
 * 用户相关API接口
 * 提供用户信息管理、状态查询等功能
 */
const userApi = {
  /** API基础路径 */
  URL: '/core/users',

  /**
   * 获取在线用户数量
   * 查询当前在线用户的数量
   * @returns 返回在线用户数量
   */
  online: async (): Promise<ResponseData<number>> => {
    const response = await api.get<ResponseData<number>>(userApi.URL + '/online')
    return response.data
  },

  /**
   * 获取用户详细信息
   * 获取当前登录用户的完整个人信息
   * @returns 返回用户详细信息
   */
  info: async (): Promise<ResponseData<UserInfoResponse>> => {
    const response = await api.get<ResponseData<UserInfoResponse>>(userApi.URL + '/me')
    return response.data
  },

  /**
   * 更换用户头像
   * 上传新的头像文件并更新用户头像
   * @param file 头像图片文件
   * @returns 返回新头像的URL
   */
  changeAvatar: async (file: File): Promise<ResponseData<string>> => {
    const formData = new FormData()
    formData.append('file', file)
    const response = await api.putFormData<ResponseData<string>>(
      userApi.URL + '/me/avatar',
      formData,
      {
        headers: { 'Content-Type': 'multipart/form-data' },
      },
    )
    return response.data
  },

  /**
   * 更新通知接收类型
   * 设置用户接收通知的类型偏好
   * @param type 通知接收类型（0: 不接收, 1: 仅重要通知, 2: 全部通知）
   * @returns 返回更新操作结果
   */
  updateNotificationReceiveType: async (type: number): Promise<ResponseData<boolean>> => {
    const response = await api.put<ResponseData<boolean>>(
      userApi.URL + '/me/notification-settings',
      { type },
    )
    return response.data
  },

  /**
   * 搜索用户
   * 根据用户名搜索匹配的用户列表
   * @param username 要搜索的用户名关键词
   * @returns 返回匹配的用户列表，与后端WenSearchUser保持一致
   */
  searchUser: async (
    username: string,
  ): Promise<
    ResponseData<Array<{ id: string; username: string; avatar: string; phone: string }>>
  > => {
    const response = await api.get<
      ResponseData<Array<{ id: string; username: string; avatar: string; phone: string }>>
    >(userApi.URL, { username })
    return response.data
  },
}

export default userApi
