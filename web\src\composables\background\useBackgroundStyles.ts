import { computed, type ComputedRef } from 'vue'

import { activeTheme, ThemeType } from '@/utils/theme/theme'

/**
 * 背景样式自定义属性类型
 */
interface BackgroundStylesProps {
  /** 自定义浅色主题渐变 */
  customLightGradient?: string
  /** 自定义深色主题渐变 */
  customDarkGradient?: string
  /** 层级索引 */
  zIndex?: number
}

/**
 * 背景样式组合式函数返回值类型
 */
interface UseBackgroundStylesReturn {
  /** 是否为深色主题 */
  isDarkTheme: ComputedRef<boolean>
  /** 背景样式 */
  backgroundStyle: ComputedRef<{
    background: string
    zIndex: number
  }>
}

/**
 * 背景样式管理及主题切换逻辑
 * 抽取自BackgroundAnimation组件
 * @param customProps 自定义属性配置
 */
export function useBackgroundStyles(
  customProps?: BackgroundStylesProps,
): UseBackgroundStylesReturn {
  const isDarkTheme = computed(() => activeTheme.value === ThemeType.DARK)

  // 计算背景样式，支持自定义渐变
  const backgroundStyle = computed(() => {
    if (isDarkTheme.value) {
      return {
        background:
          customProps?.customDarkGradient ||
          'linear-gradient(to top, #0a0a0f, #121218 60%, #1c1c26 100%)',
        zIndex: customProps?.zIndex || 0,
      }
    } else {
      return {
        background:
          customProps?.customLightGradient ||
          'linear-gradient(to top, var(--creamy-white-3), var(--creamy-white-2) 70%, rgba(232, 240, 242, 0.8) 100%)',
        zIndex: customProps?.zIndex || 0,
      }
    }
  })

  return {
    isDarkTheme,
    backgroundStyle,
  }
}
