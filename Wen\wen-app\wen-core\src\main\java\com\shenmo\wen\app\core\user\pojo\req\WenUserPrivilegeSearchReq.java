package com.shenmo.wen.app.core.user.pojo.req;

import com.shenmo.wen.app.core.pojo.req.WenSearchReq;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 用户特权搜索请求
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WenUserPrivilegeSearchReq extends WenSearchReq {

    /**
     * 最后一条记录的ID，用于基于游标的分页
     */
    private Long id;

    /**
     * 加载数量限制，默认20条
     */
    private Integer loadSize = 20;
}
