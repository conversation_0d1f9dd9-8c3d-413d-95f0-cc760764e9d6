/**
 * 特权激活码生成请求类型
 */
export interface PrivilegeCodeReq
  extends Record<string, string | number | boolean | null | undefined> {
  /** 用户ID */
  userId: number

  /** 特权类型：0-模板，1-付费 */
  privilegeType: 0 | 1

  /** 模板ID（当privilegeType为0时必填） */
  templateId?: number

  /** 付费面额（当privilegeType为1时必填） */
  amount?: number

  /** 过期时间 */
  expireTime: string | number | null

  /** 验证类型：0-短信验证，1-二维码验证 */
  verificationType: 0 | 1
}
