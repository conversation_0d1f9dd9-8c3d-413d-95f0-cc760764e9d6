package com.shenmo.wen.app.core.interaction.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shenmo.wen.common.mybatis.handler.TimestampToLongTypeHandler;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "wen_interaction", autoResultMap = true)
public class WenInteraction {
    /**
     * 互动ID，作为每条互动记录的唯一标识符，在数据库中是主键，不能为空。
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID，记录参与互动的用户的唯一标识，可为空（具体取决于业务逻辑，可能存在一些特殊情况的互动记录）。
     */
    private Long userId;

    /**
     * 目标类型，用整数表示互动的目标对象类型，0代表评论，1代表文章，可为空（需根据实际业务规则判断赋值情况）。
     */
    private Integer targetType;

    /**
     * 目标ID，根据目标类型的不同，对应文章的ID或者评论的ID，用于准确关联到具体的互动目标，可为空（结合业务场景确定其合法性）。
     */
    private Long targetId;

    /**
     * 互动类型，用整数表示具体的互动行为，0表示点踩，1表示点赞，可为空（依据业务逻辑规范其取值情况）。
     */
    private Integer actionType;

    /**
     * 互动时间，记录互动行为发生的时间戳，默认取当前时间，在创建互动记录时自动赋值。
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long ctTm;
}
