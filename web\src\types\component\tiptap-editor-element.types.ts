/**
 * TiptapEditor 组件元素类型定义
 * 定义TipTap编辑器组件元素的基础属性和方法
 */

/**
 * TipTap编辑器元素接口
 */
export interface TiptapEditorElement {
  /** 编辑器元素ID */
  id?: string
  /** 编辑器CSS类名 */
  className?: string
  /** 聚焦编辑器 */
  focus?: () => void
  /** 失焦编辑器 */
  blur?: () => void
  /** Vue组件DOM元素 */
  $el?: HTMLElement
  /** Vue组件引用 */
  $refs?: Record<string, unknown>
  /** 编辑器实例 */
  editor?: {
    setContent: (content: unknown) => void
    getMarkdown: () => string
  }
}
