package com.shenmo.wen.common.pojo.response;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Data;

import java.util.List;

/**
 * 分页结果集
 *
 * <AUTHOR>
 */
@Data
public class PageResult<T> {

    /**
     * 第几页
     *
     */
    private Integer pageNo = 1;

    /**
     * 每页条数
     *
     */
    private Integer pageSize = 10;

    /**
     * 总页数
     *
     */
    private Integer totalPage = 0;

    /**
     * 总记录数
     *
     */
    private Integer totalRows = 0;

    /**
     * 结果集
     *
     */
    private List<T> rows;

    public PageResult() {
    }

    /**
     * 将mybatis-plus的page转成自定义的PageResult，扩展了totalPage总页数
     *
     * <AUTHOR>
     */
    public PageResult(IPage<T> page) {
        this.setRows(page.getRecords());
        this.setTotalRows(longToInt(page.getTotal()));
        this.setPageNo(longToInt(page.getCurrent()));
        this.setPageSize(longToInt(page.getSize()));
        this.setTotalPage(longToInt(page.getPages()));
    }

    /**
     * 将mybatis-plus的page转成自定义的PageResult，扩展了totalPage总页数
     * 可单独设置rows
     *
     * <AUTHOR>
     */
    public PageResult(IPage<?> page, List<T> t) {
        this.setRows(t);
        this.setTotalRows(longToInt(page.getTotal()));
        this.setPageNo(longToInt(page.getCurrent()));
        this.setPageSize(longToInt(page.getSize()));
        this.setTotalPage(longToInt(page.getPages()));
    }

    private int longToInt(long i) {
        return Long.valueOf(i).intValue();
    }
}
