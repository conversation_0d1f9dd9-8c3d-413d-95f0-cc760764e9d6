/**
 * 气泡菜单组合式函数
 * 提供气泡菜单的显示逻辑和配置管理
 */

import type { BubbleMenuProps } from '@/types/tiptap/bubble-menu-props.types'
import type { ExcludedNodeType } from '@/types/tiptap/excluded-node-type.types'

// 气泡菜单配置常量
export const BUBBLE_MENU_CONFIG = {
  ANIMATION_DURATION: 100,
  APPEND_TO: 'parent' as const,
} as const

// 不支持气泡菜单的节点类型
const EXCLUDED_NODE_TYPES: ExcludedNodeType[] = ['image', 'bilibili', 'codeBlock']

/**
 * 检查是否为排除的节点类型
 */
export const isExcludedNodeType = (nodeName: string): nodeName is ExcludedNodeType => {
  return EXCLUDED_NODE_TYPES.includes(nodeName as ExcludedNodeType)
}

/**
 * 检查选择范围内是否包含代码块
 */
export const hasCodeBlockInSelection = (
  state: BubbleMenuProps['state'],
  from: number,
  to: number,
): boolean => {
  let hasCodeBlock = false
  state.doc.nodesBetween(from, to, (node: { type: { name: string } }) => {
    if (node.type.name === 'codeBlock') {
      hasCodeBlock = true
      return false // 停止遍历
    }
  })
  return hasCodeBlock
}

/**
 * 检查基本显示条件
 */
export const checkBasicConditions = (
  editor: BubbleMenuProps['editor'],
  selection: BubbleMenuProps['state']['selection'],
): boolean => {
  // 如果选择是空的(只有光标)，不显示
  if (selection.empty) return false

  // 如果编辑器不可编辑，不显示
  if (!editor.isEditable) return false

  return true
}

/**
 * 气泡菜单显示逻辑
 */
export const shouldShowBubbleMenu = (props: BubbleMenuProps): boolean => {
  const { editor, state } = props
  const { selection } = state

  // 检查基本条件
  if (!checkBasicConditions(editor, selection)) return false

  // 如果当前选中的是排除的节点类型，不显示气泡菜单
  const node = selection.node
  if (node?.type?.name && isExcludedNodeType(node.type.name)) {
    return false
  }

  // 检查选择范围内是否包含代码块
  const { from, to } = selection
  if (hasCodeBlockInSelection(state, from, to)) return false

  // 显示选择菜单
  return true
}

/**
 * 气泡菜单组合式函数返回值类型
 */
interface UseBubbleMenuReturn {
  /** Tippy 配置选项 */
  tippyOptions: {
    duration: number
    appendTo: 'parent'
  }
  /** 是否显示气泡菜单 */
  shouldShow: (props: BubbleMenuProps) => boolean
  /** 是否为排除的节点类型 */
  isExcludedNodeType: (nodeName: string) => nodeName is ExcludedNodeType
  /** 检查选择范围内是否包含代码块 */
  hasCodeBlockInSelection: (state: BubbleMenuProps['state'], from: number, to: number) => boolean
  /** 检查基本显示条件 */
  checkBasicConditions: (
    editor: BubbleMenuProps['editor'],
    selection: BubbleMenuProps['state']['selection'],
  ) => boolean
}

/**
 * 气泡菜单组合式函数
 * 提供气泡菜单的显示逻辑和配置管理
 */
export function useBubbleMenu(): UseBubbleMenuReturn {
  // Tippy 配置选项
  const tippyOptions = {
    duration: BUBBLE_MENU_CONFIG.ANIMATION_DURATION,
    appendTo: BUBBLE_MENU_CONFIG.APPEND_TO as 'parent',
  }

  return {
    tippyOptions,
    shouldShow: shouldShowBubbleMenu,
    isExcludedNodeType,
    hasCodeBlockInSelection,
    checkBasicConditions,
  }
}
