/* 最小化过渡效果，仅用于背景和文本颜色 */
:root {
  transition:
    background-color 0.1s ease-out,
    color 0.1s ease-out;
}

/* 为滞后组件添加即时切换 */
.search-container,
.n-input,
.user-info-group,
.user-info,
.avatar-container,
.n-dropdown-menu,
.notification-container {
  /* 移除过渡，使用即时切换 */
  transition: none;
}

/* 评论和编辑器组件的即时切换 */
.comment-info-container,
.comment-list-container,
.user-comment-container,
.user-comment-container-fixed,
.comment-content-row,
.comment-input-row,
.comment-reply-row,
.tiptap-editor-wrapper,
.editor-content,
.ProseMirror,
.ProseMirrorInput,
.article-content,
.article-info-container,
.tiptap-fullscreen {
  transition: none;
  will-change: background-color, color;
}

/* 定义暗色模式下的组件颜色变量 */
[data-theme='dark'] {
  --comment-info-bg: var(--dark-gray);
  --comment-list-bg: var(--dark-gray);
  --comment-container-bg: var(--deep-gray);
  --comment-fixed-bg: var(--blue-light);
  --comment-input-bg: var(--dark-gray);
  --comment-reply-bg: var(--deep-gray);
  --comment-reply-btn-bg: var(--deep-gray);
  --comment-fixed-reply-bg: var(--blue-light);
  --comment-fixed-reply-btn-bg: var(--blue-light);
  --prosemirror-input-bg: var(--white-2);
  --comment-input-prosemirror-bg: var(--dark-gray);
  --comment-reply-prosemirror-bg: var(--deep-gray);
  --comment-fixed-prosemirror-bg: var(--blue-light);
}

/* 定义浅色模式下的组件颜色变量 */
[data-theme='light'] {
  --comment-info-bg: var(--creamy-white-1);
  --comment-list-bg: var(--creamy-white-1);
  --comment-container-bg: var(--white-1);
  --comment-fixed-bg: var(--blue-light);
  --comment-input-bg: var(--creamy-white-1);
  --comment-reply-bg: var(--white-1);
  --comment-reply-btn-bg: var(--white-1);
  --comment-fixed-reply-bg: var(--blue-light);
  --comment-fixed-reply-btn-bg: var(--blue-light);
  --prosemirror-input-bg: var(--white-1);
  --comment-input-prosemirror-bg: var(--creamy-white-1);
  --comment-reply-prosemirror-bg: var(--white-1);
  --comment-fixed-prosemirror-bg: var(--blue-light);
}

/* 评论容器和组件的背景色 */
[data-theme] .comment-info-container {
  background-color: var(--comment-info-bg);
}

[data-theme] .comment-list-container {
  background-color: var(--comment-list-bg);
}

[data-theme] .user-comment-container {
  background-color: var(--comment-container-bg);
}

[data-theme] .user-comment-container-fixed,
[data-theme] .comment-flash {
  background-color: var(--comment-fixed-bg);
  opacity: 1;
}

/* 评论内容区域的背景色 */
[data-theme] .comment-content-row .ProseMirror,
[data-theme] .comment-input-row .ProseMirror,
[data-theme] .comment-reply-row .ProseMirror {
  background-color: inherit;
  color: var(--black);
}

/* 评论回复区域的背景色 */
[data-theme] .user-comment-container .comment-reply-row {
  background-color: var(--comment-reply-bg);
}

[data-theme] .user-comment-container .comment-reply-send-btn {
  background-color: var(--comment-reply-btn-bg);
}

[data-theme] .user-comment-container .comment-reply-row .tiptap-editor-wrapper,
[data-theme] .user-comment-container .comment-reply-row .editor-content {
  background-color: var(--comment-reply-bg);
}

/* 固定评论回复区域的背景色 */
[data-theme] .user-comment-container-fixed .comment-reply-row,
[data-theme] .comment-flash .comment-reply-row,
[data-theme] .user-comment-container-fixed .comment-reply-send-btn,
[data-theme] .comment-flash .comment-reply-send-btn,
[data-theme] .user-comment-container-fixed .comment-reply-row .tiptap-editor-wrapper,
[data-theme] .user-comment-container-fixed .comment-reply-row .editor-content,
[data-theme] .comment-flash .comment-reply-row .tiptap-editor-wrapper,
[data-theme] .comment-flash .comment-reply-row .editor-content {
  background-color: var(--comment-fixed-reply-bg);
  opacity: 1;
}

/* 输入框的背景色 */
[data-theme] .user-comment-container-fixed .comment-reply-row .ProseMirrorInput,
[data-theme] .comment-flash .comment-reply-row .ProseMirrorInput {
  background-color: var(--comment-fixed-prosemirror-bg);
  border-color: var(--gray-3);
}

[data-theme] .user-comment-container .comment-reply-row .ProseMirrorInput {
  background-color: var(--comment-reply-prosemirror-bg);
  border-color: var(--gray-3);
}

[data-theme] .comment-input-row {
  background-color: var(--comment-input-bg);
}

[data-theme] .comment-input-row .tiptap-editor-wrapper,
[data-theme] .comment-input-row .editor-content,
[data-theme] .comment-input-row .ProseMirrorInput {
  background-color: var(--comment-input-prosemirror-bg);
}

/* 自定义卡片悬浮效果 - 不干预主题 */
.card-item {
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.card-item:hover {
  transform: translateY(-0.6rem);
  box-shadow: var(--shadow);
}

/* 主题切换按钮保持自定义动画效果 */
.theme-toggle-scene * {
  transition: none;
}

/* 高效的容器过渡 - 仅用于布局变化 */
.article-container,
.comment-container {
  width: 100%;
  overflow: hidden;
}
