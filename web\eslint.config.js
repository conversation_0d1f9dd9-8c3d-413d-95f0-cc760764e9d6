import pluginVitest from '@vitest/eslint-plugin'
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting'
import vueTsEslintConfig from '@vue/eslint-config-typescript'
import importPlugin from 'eslint-plugin-import'

export default [
  {
    name: 'app/files-to-lint',
    files: ['**/*.{ts,mts,tsx,vue}'],
  },

  {
    name: 'app/files-to-ignore',
    ignores: [
      '**/dist/**',
      '**/dist-ssr/**',
      '**/coverage/**',
      '**/*.local',
      '**/node_modules/**',
      '**/.vscode/**',
      '**/.husky/**',
      '**/public/**',
      '**/*.d.ts',
      '**/.cursor/**',
    ],
  },

  {
    files: ['**/*.{vue,tsx,jsx}'],
    languageOptions: {
      parser: '@typescript-eslint/parser',
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
  },

  ...vueTsEslintConfig(),

  {
    ...pluginVitest.configs.recommended,
    files: ['src/**/__tests__/*'],
  },

  // 添加更严格的TypeScript规则
  {
    files: ['**/*.{ts,mts,tsx,vue}'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'error', // 启用any类型检查，符合规范要求
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
        },
      ],
      '@typescript-eslint/naming-convention': 'off',
    },
  },

  // 为 icons/index.ts 文件特殊配置
  {
    files: ['src/icons/index.ts'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
    },
  },

  // 为复杂类型文件临时禁用严格检查
  {
    files: [
      'src/composables/danmaku/useDanmakuOperations.ts',
      'src/utils/tiptap/tiptapExtensions.ts',
      // 重构后的文件临时允许any类型
      'src/composables/comment/useCommentInteractionReply.ts',
      'src/composables/comment/useCommentLoader.ts',
      'src/composables/comment/useCommentReply.ts',
      'src/composables/comment/useCommentScroll.ts',
      'src/composables/comment/useCommentInteraction.ts',
      'src/composables/home/<USER>',
      'src/composables/home/<USER>',
      'src/composables/image/useImagePreviewDrag.ts',
      'src/views/Login.vue',
    ],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
    },
  },

  // 添加导入规范
  {
    plugins: {
      import: importPlugin,
    },
    rules: {
      'import/order': [
        'error',
        {
          groups: [
            'builtin', // Node.js内置模块
            'external', // 第三方模块
            'internal', // 内部模块
            'parent', // 父级目录
            'sibling', // 同级目录
            'index', // 当前目录
            'object', // 对象导入
            'type', // 类型导入
          ],
          'newlines-between': 'always',
          alphabetize: {
            order: 'asc',
            caseInsensitive: true,
          },
          pathGroups: [
            {
              pattern: '@/**',
              group: 'internal',
              position: 'after',
            },
            {
              pattern: '*.{css,scss,less}',
              group: 'index',
              position: 'after',
            },
          ],
        },
      ],
    },
  },

  skipFormatting,
]
