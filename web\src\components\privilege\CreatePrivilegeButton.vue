<template>
  <Add24Regular
    :size="36"
    color="var(--blue)"
    @click="handleClick"
    class="cursor-pointer create-privilege-button"
    ref="createButtonRef"
    :class="{ 'is-rotating': isRotating }"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  />
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'

import { Add24Regular } from '@/icons'

const emit = defineEmits(['click'])

// 按钮旋转状态
const isRotating = ref(false)
const rotationTimer = ref<number | null>(null)
const autoRotationTimer = ref<number | null>(null)

// 处理按钮点击
const handleClick = () => {
  emit('click')
}

// 处理鼠标悬浮，触发旋转
const handleMouseEnter = () => {
  if (isRotating.value) return

  if (autoRotationTimer.value) {
    clearTimeout(autoRotationTimer.value)
    autoRotationTimer.value = null
  }

  isRotating.value = true

  rotationTimer.value = window.setTimeout(() => {
    isRotating.value = false
    scheduleNextAutoRotation()
  }, 1500)
}

// 处理鼠标离开
const handleMouseLeave = () => {
  if (!isRotating.value) {
    scheduleNextAutoRotation()
  }
}

// 计划下一次自动旋转
const scheduleNextAutoRotation = () => {
  if (autoRotationTimer.value) {
    clearTimeout(autoRotationTimer.value)
    autoRotationTimer.value = null
  }

  const nextRotationTime = 5000 + Math.random() * 10000
  autoRotationTimer.value = window.setTimeout(() => {
    triggerAutoRotation()
  }, nextRotationTime)
}

// 触发自动旋转
const triggerAutoRotation = () => {
  if (isRotating.value) {
    scheduleNextAutoRotation()
    return
  }

  isRotating.value = true

  rotationTimer.value = window.setTimeout(() => {
    isRotating.value = false
    scheduleNextAutoRotation()
  }, 1500)
}

onMounted(() => {
  scheduleNextAutoRotation()
})

onUnmounted(() => {
  if (rotationTimer.value) {
    clearTimeout(rotationTimer.value)
    rotationTimer.value = null
  }

  if (autoRotationTimer.value) {
    clearTimeout(autoRotationTimer.value)
    autoRotationTimer.value = null
  }
})
</script>

<style lang="scss" scoped>
@use '@/styles/ui-elements/long-press';

.create-privilege-button {
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}
</style>
