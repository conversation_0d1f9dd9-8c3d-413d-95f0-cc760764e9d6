/*
 * CommentListItem 组件样式
 * 评论列表项组件的样式定义，包括评论容器、用户信息、内容和交互
 */

.user-comment-container {
  border: var(--border-1);
  border-radius: 0.6rem;
  padding: 0.6rem 1.25rem;
  margin-bottom: 3%;
  background-color: var(--comment-container-bg, var(--white-1));

  /* 确保回复框也继承普通评论容器背景 */
  .comment-reply-row {
    background-color: var(--comment-reply-bg, var(--white-1));

    .comment-reply-tiptap-editor {
      :deep(.tiptap-editor-wrapper),
      :deep(.editor-content) {
        background-color: var(--comment-reply-bg, var(--white-1));
      }

      :deep(.ProseMirrorInput) {
        background-color: var(--comment-reply-prosemirror-bg, var(--white-1));
        border: 1px solid var(--gray-3);
      }
    }
  }

  /* 覆盖普通评论回复框中的样式 */
  .comment-reply-send-btn {
    background-color: inherit; /* 继承父容器背景色，确保一致性 */
  }
}

.user-comment-container-fixed {
  border: var(--border-1);
  border-radius: 0.6rem;
  padding: 0.6rem 1.25rem;
  margin-bottom: 3%;
  background-color: var(--comment-fixed-bg, var(--blue-light));
  position: sticky;
  top: 0;
  z-index: 2;
  opacity: 1;
}

.comment-flash {
  animation: flash 1s ease-in-out;
}

/* 确保评论内容的背景色与评论容器一致 */
.user-comment-container-fixed,
.comment-flash {
  :deep(.tiptap-editor-wrapper),
  :deep(.editor-content),
  :deep(.ProseMirror),
  :deep(p),
  :deep(h1),
  :deep(h2),
  :deep(h3),
  :deep(h4),
  :deep(h5),
  :deep(h6) {
    background-color: var(--comment-fixed-bg, var(--blue-light));
  }

  /* 引用块不设置背景色，只通过字体颜色和边框区分 */
  :deep(blockquote) {
    background-color: transparent;
  }

  /* 确保回复框也继承蓝色背景 */
  .comment-reply-row {
    background-color: var(--comment-fixed-reply-bg, var(--blue-light));

    .comment-reply-tiptap-editor {
      :deep(.tiptap-editor-wrapper),
      :deep(.editor-content) {
        background-color: var(--comment-fixed-reply-bg, var(--blue-light));
      }

      :deep(.ProseMirrorInput) {
        background-color: var(--comment-fixed-prosemirror-bg, var(--blue-light));
        border: 1px solid var(--gray-3);
        opacity: 1; /* 确保不透明 */
      }
    }
  }
}

.user-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.6rem;

  .user-detail-col {
    margin-left: 0.6rem;

    .user-nickname {
      display: block;
      font-weight: bold;
    }

    .user-extra-info {
      display: block;

      .time-clickable {
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}

.comment-content-row {
  margin-bottom: 0.25rem;
  font-size: 1rem;
}

.comment-interaction-reply {
  display: flex;

  .comment-reply-info {
    display: flex;
    align-items: center;
    width: 60%;
    margin-right: 1rem;
    font-size: 0.8rem;
    gap: 0.3rem;

    .comment-reply-list-btn {
      margin-right: 1%;
    }
  }

  .comment-interaction-btn {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    font-size: 0.8rem;
  }
}

.comment-reply-row {
  display: flex;
  justify-content: center;
  align-items: end;
  background-color: inherit; /* 继承父容器背景色 */
  padding: 0.5rem 0; /* 减少padding */
  overflow: hidden; /* 确保动画过程中内容不会溢出 */
  position: relative; /* 为绝对定位提供参考 */
  z-index: 10; /* 确保在其他内容之上 */
  box-sizing: border-box; /* 确保padding不影响布局计算 */
  margin-bottom: 0; /* 移除底部边距 */

  .comment-reply-tiptap-editor {
    max-width: 80%;
    margin-right: 1.25rem;
    box-sizing: border-box;
  }
}

.comment-reply-send-btn {
  margin-bottom: 1.5rem;
  transition: all 0.2s ease;

  /* 覆盖固定回复框中的样式 */
  .user-comment-container-fixed &,
  .comment-flash & {
    background-color: var(--comment-fixed-reply-btn-bg, var(--blue-light));
    opacity: 1;
  }

  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

@keyframes flash {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.02);
    opacity: 0.9;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 评论回复框动画样式 */
.comment-reply-enter-active {
  transition: all 0.35s cubic-bezier(0.25, 0.8, 0.25, 1);
  transform-origin: top center;
  overflow: hidden; /* 防止动画期间内容溢出 */
}

.comment-reply-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
  transform-origin: top center;
  overflow: hidden; /* 防止动画期间内容溢出 */
}

.comment-reply-enter-from {
  opacity: 0;
  transform: translateY(-15px) scaleY(0.7);
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  margin-bottom: 0; /* 防止额外的边距 */
}

.comment-reply-leave-to {
  opacity: 0;
  transform: translateY(-8px) scaleY(0.85);
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  margin-bottom: 0; /* 防止额外的边距 */
}

/* 为回复框内的编辑器添加动画延迟，创建层次感 */
.comment-reply-enter-active .comment-reply-tiptap-editor {
  transition: all 0.35s cubic-bezier(0.25, 0.8, 0.25, 1) 0.1s;
}

.comment-reply-enter-active .comment-reply-send-btn {
  transition: all 0.35s cubic-bezier(0.25, 0.8, 0.25, 1) 0.15s;
}

.comment-reply-enter-from .comment-reply-tiptap-editor,
.comment-reply-enter-from .comment-reply-send-btn {
  opacity: 0;
  transform: translateY(-10px);
}

/*
 * CommentMainInput 组件样式
 * 评论主输入组件的样式定义，包括输入框容器和响应式设计
 */

.comment-input-affix {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: var(--comment-input-bg, var(--creamy-white-1));
  z-index: 1500;
  transition: transform 0.3s ease;

  @media (width <= 768px) {
    position: fixed; /* 保持固定定位，方便用户随时发送评论 */
    width: 100vw;
    bottom: 0;
    left: 0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1); /* 添加阴影增强层次感 */
  }
}

.comment-input-row {
  display: flex;
  align-items: end;
  justify-content: center;
  background-color: var(--comment-input-bg, var(--creamy-white-1));
  border-top: var(--border-1);
  padding: 0.5rem 1.25rem;
  width: 100%;
  box-sizing: border-box;

  @media (width <= 768px) {
    padding: 0.5rem;
  }

  .comment-tiptap-editor {
    max-width: 75%;
    margin-right: 1.25rem;
    transition: all 0.3s ease;

    @media (width <= 768px) {
      max-width: 70%;
    }

    :deep(.tiptap-editor-wrapper),
    :deep(.editor-content),
    :deep(.ProseMirrorInput) {
      background-color: var(--comment-input-prosemirror-bg, var(--creamy-white-1));
      transition: all 0.3s ease;
    }
  }

  // 发送按钮动画
  .comment-reply-send-btn {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: translateY(-2px) scale(1.05);
    }

    &:active {
      transform: translateY(0) scale(1);
    }

    &:disabled {
      opacity: 0.6;
      transform: none;
    }
  }
}

/*
 * CommentDanmaku 组件样式
 * 评论弹幕组件的样式定义，包括弹幕容器和发布者信息
 */

.comment-container {
  height: calc(100vh - 10rem);
  height: calc(100dvh - 10rem);
  width: 100%;
  position: absolute;
  left: 0;
  overflow: hidden;

  .comment-danmaku {
    width: 100%;
    height: 100%;

    .comment-danmaku-item {
      display: flex;
      align-items: flex-end;
      width: fit-content;

      /* 发布者信息显示样式 */
      .comment-danmaku-publisher {
        display: flex;
        align-items: center;
        margin-bottom: 0.2rem;
        font-size: 1.2rem;
        color: var(--black);

        .n-avatar {
          margin-right: 4px;

          /* 确保发布者头像保持圆形不变形 */
          ::v-deep(.n-avatar__img) {
            object-fit: cover;
            aspect-ratio: 1 / 1;
          }
        }
      }

      /* 弹幕内容样式 */
      .comment-danmaku-content {
        max-width: 31rem;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 1.2rem;
        line-height: 1.5;
        vertical-align: bottom;
        margin-bottom: 0.25rem;
        color: var(--black);
      }
    }
  }
}
