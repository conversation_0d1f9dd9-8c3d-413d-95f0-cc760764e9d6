/**
 * 编辑器验证相关类型定义
 */

export interface EditorContent {
  type?: string
  content?: EditorContent[]
  text?: string
  marks?: EditorMark[]
  attrs?: Record<string, unknown>
}

export interface EditorMark {
  type: string
  attrs?: Record<string, unknown>
}

export interface ValidationResult {
  isValid: boolean
  errors?: string[]
  warnings?: string[]
}

export interface EditorValidationOptions {
  maxLength?: number
  minLength?: number
  allowedTypes?: string[]
  requiredFields?: string[]
}
