<template>
  <NConfigProvider :theme="themeObject">
    <NLoadingBarProvider>
      <NDialogProvider>
        <NNotificationProvider>
          <NMessageProvider>
            <router-view />
          </NMessageProvider>
        </NNotificationProvider>
      </NDialogProvider>
    </NLoadingBarProvider>
  </NConfigProvider>
</template>

<script lang="ts" setup>
import {
  NConfigProvider,
  NMessageProvider,
  NDialogProvider,
  NNotificationProvider,
  NLoadingBarProvider,
} from 'naive-ui'
import { computed, onMounted, watch, nextTick } from 'vue'

import logger from '@/utils/log/log'
import { activeTheme, THEME_OBJECTS, loadSavedTheme } from '@/utils/theme/theme'

// 计算当前主题对象
const themeObject = computed(() => {
  return THEME_OBJECTS[activeTheme.value]
})

onMounted(() => {
  // 加载保存的主题
  loadSavedTheme()
})

// 监听主题变化，确保UI组件和CSS变量同步
watch(activeTheme, (newTheme) => {
  // 使用nextTick确保在下一个DOM更新周期处理
  nextTick(() => {
    // 搜索特定组件并强制刷新
    const targetSelectors = [
      '.search-container',
      '.n-input',
      '.user-info-group',
      '.user-info',
      '.avatar-container',
      // 评论相关组件
      '.comment-info-container',
      '.comment-list-container',
      '.user-comment-container',
      '.user-comment-container-fixed',
      '.comment-content-row',
      '.comment-input-row',
      '.comment-reply-row',
      '.tiptap-editor-wrapper',
      '.editor-content',
      '.ProseMirror',
      '.ProseMirrorInput',
    ]

    // 立即添加主题优先级
    targetSelectors.forEach((selector) => {
      const elements = document.querySelectorAll(selector)
      elements.forEach((el) => {
        if (el instanceof HTMLElement) {
          // 添加优先级类名
          el.classList.add('theme-priority')
        }
      })
    })

    // 特殊处理编辑器组件
    document.querySelectorAll('.ProseMirror, .editor-content').forEach((el) => {
      if (el instanceof HTMLElement) {
        // 做一个临时属性设置并移除，强制刷新
        el.setAttribute('data-force-update', Date.now().toString())
        setTimeout(() => el.removeAttribute('data-force-update'), 10)
      }
    })

    logger.debug(`Theme changed to: ${newTheme}`)
  })
})
</script>

<style>
/* 优先级组件的特殊处理 */
.theme-priority {
  /* 仅优化过渡速度，不改变实际样式 */
  transition: none !important;
  will-change: background-color, color;

  /* 提示浏览器为变化做准备 */
}

/* 评论编辑器特殊处理 */
[data-theme-refresh='true'],
[data-force-update] {
  transition: none !important;
  animation: instant-refresh 0.01s;
}

@keyframes instant-refresh {
  0% {
    opacity: 0.99;
  }

  100% {
    opacity: 1;
  }
}
</style>
