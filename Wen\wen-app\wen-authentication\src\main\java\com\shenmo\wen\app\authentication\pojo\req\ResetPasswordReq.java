package com.shenmo.wen.app.authentication.pojo.req;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 重置密码请求
 *
 * <AUTHOR>
 */
@Data
public class ResetPasswordReq {

    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式错误")
    private String email;

    @NotBlank(message = "邮箱验证码不能为空")
    private String emailCode;

    @NotBlank(message = "新密码不能为空")
    @Length(min = 6, message = "密码长度至少为 6 个字符")
    private String newPassword;

    @NotBlank(message = "验证码不能为空")
    private String cftt;
}
