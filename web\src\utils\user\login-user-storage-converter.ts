/**
 * 登录用户存储转换工具
 */
import type { LoginUserStorage } from '@/types/user/login-user-storage.types'
import type { LoginUser } from '@/types/user/login-user.types'

/**
 * 将 LoginUser 转换为 LoginUserStorage
 */
export function toLoginUserStorage(loginUser: LoginUser): LoginUserStorage {
  return {
    ...loginUser,
  }
}

/**
 * 将 LoginUserStorage 转换为 LoginUser
 */
export function fromLoginUserStorage(storage: LoginUserStorage): LoginUser {
  return {
    id: storage.id,
    username: storage.username,
    phone: storage.phone,
    email: storage.email,
    avatar: storage.avatar,
    ipLocation: storage.ipLocation,
    job: storage.job,
    level: storage.level,
    notificationReceiveType: storage.notificationReceiveType,
    config: storage.config,
  }
}
