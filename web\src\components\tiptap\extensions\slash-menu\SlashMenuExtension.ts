import { Extension } from '@tiptap/core'
import { Plugin, PluginKey } from '@tiptap/pm/state'
import { Decoration, DecorationSet } from '@tiptap/pm/view'
import Suggestion from '@tiptap/suggestion'

import { SlashMenuView } from './SlashMenuView'

import type { SlashMenuItem } from './types'

export interface SlashMenuOptions {
  items: SlashMenuItem[]
  dictionary: {
    lineEmpty: string
    lineSlash: string
    queryEmpty: string
  }
  imageUploadTrigger?: () => void
  modalTrigger?: (title: string, callback: () => void, needInput?: boolean) => void
}

export const SlashMenuExtension = Extension.create<SlashMenuOptions>({
  name: 'slashMenu',

  addOptions() {
    return {
      items: [],
      dictionary: {
        lineEmpty: '',
        lineSlash: ' ...',
        queryEmpty: '...',
      },
      imageUploadTrigger: undefined,
      modalTrigger: undefined,
    }
  },

  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
        pluginKey: new PluginKey(`${this.name}-suggestion`),
        char: '/',
        // 添加 allow 函数来控制在哪些节点类型中允许触发斜杆菜单
        allow: ({ editor: _editor, state, range: _range }) => {
          const { $from } = state.selection
          const parent = $from.parent

          // 不允许在代码块内部触发斜杆菜单
          if (parent.type.name === 'codeBlock') {
            return false
          }

          // 只允许在段落中触发
          return parent.type.name === 'paragraph'
        },
        items: ({ query }) => {
          const filtered: SlashMenuItem[] = []

          for (const item of this.options.items) {
            if (item === '|') {
              filtered.push(item)
              continue
            }

            if (query !== '') {
              const q = query.toLowerCase()
              if (
                !item.name.toLowerCase().includes(q) &&
                !item.keywords.toLowerCase().includes(q)
              ) {
                continue
              }
            }

            filtered.push({
              ...item,
              action: (editor) => {
                // 修复清除搜索文本的逻辑，只删除斜杆和查询文本，不清空整行
                const { state, dispatch } = editor.view

                // 计算需要删除的范围：从斜杆开始位置到当前光标位置
                const $from = state.selection.$from
                const slashPos = $from.pos - (query.length + 1) // +1 是斜杆字符
                const tr = state.tr.deleteRange(slashPos, $from.pos)
                dispatch(tr)

                // 对于图片项，使用图片上传触发器
                if (item.id === 'image' && this.options.imageUploadTrigger) {
                  this.options.imageUploadTrigger()
                } else {
                  // 执行原始命令（包括链接和B站视频，它们会在action中调用modalTrigger）
                  item.action(editor)
                }

                // 聚焦编辑器
                editor.view.focus()
              },
            })
          }

          // 过滤连续的分隔符
          const items: SlashMenuItem[] = []
          for (let i = 0; i < filtered.length; i++) {
            const item = filtered[i]
            if (item === '|') {
              if (i === 0 || i === filtered.length - 1) {
                continue
              }
              if (filtered[i + 1] === '|') {
                continue
              }
              if (items.length === 0) {
                continue
              }
              if (items[items.length - 1] === '|') {
                continue
              }
            }
            items.push(item)
          }

          return items
        },
        render: SlashMenuView.create({
          editor: this.editor,
          dictionary: {
            empty: this.options.dictionary.queryEmpty,
          },
        }),
      }),

      // 占位符插件
      new Plugin({
        key: new PluginKey(`${this.name}-placeholder`),
        props: {
          decorations: (state) => {
            const { $from } = state.selection
            const parent = $from.parent

            if (parent.type.name !== 'paragraph') {
              return null
            }

            const decorations: Decoration[] = []
            const isEmpty = parent.content.size === 0
            const isSlash = parent.textContent === '/'
            const isTopLevel = $from.depth === 1

            if (isTopLevel) {
              if (isEmpty) {
                decorations.push(
                  Decoration.node($from.start() - 1, $from.end() + 1, {
                    class: 'slash-menu-placeholder',
                    'data-placeholder': this.options.dictionary.lineEmpty,
                  }),
                )
              }

              if (isSlash) {
                decorations.push(
                  Decoration.node($from.start() - 1, $from.end() + 1, {
                    class: 'slash-menu-placeholder',
                    'data-placeholder': ` ${this.options.dictionary.lineSlash}`,
                  }),
                )
              }

              return DecorationSet.create(state.doc, decorations)
            }

            return null
          },
        },
      }),
    ]
  },
})
