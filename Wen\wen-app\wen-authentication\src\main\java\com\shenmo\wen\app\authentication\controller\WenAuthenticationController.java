package com.shenmo.wen.app.authentication.controller;

import com.shenmo.wen.app.authentication.pojo.req.LoginReq;
import com.shenmo.wen.app.authentication.pojo.req.RegisterReq;
import com.shenmo.wen.app.authentication.pojo.req.ResetPasswordReq;
import com.shenmo.wen.app.authentication.pojo.req.SendEmailCodeReq;
import com.shenmo.wen.app.authentication.service.WenEmailVerificationService;
import com.shenmo.wen.app.authentication.service.WenAuthenticationService;
import com.shenmo.wen.common.pojo.response.ResponseData;
import com.shenmo.wen.modules.user.pojo.resp.WenUserResp;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class WenAuthenticationController {
    private final WenAuthenticationService service;
    private final WenEmailVerificationService emailVerificationService;

    @PostMapping("/sessions")
    public ResponseData<WenUserResp> login(@RequestBody LoginReq req) {
        return ResponseData.success(service.login(req));
    }

    @DeleteMapping("/sessions")
    public ResponseData<Void> logout() {
        service.logout();
        return ResponseData.success();
    }

    @PostMapping("/accounts")
    public ResponseData<WenUserResp> register(@Validated @RequestBody RegisterReq req) {
        return ResponseData.success(service.register(req));
    }

    @PostMapping("/email-codes")
    public ResponseData<Boolean> sendEmailCode(@Validated @RequestBody SendEmailCodeReq req) {
        return ResponseData.success(emailVerificationService.sendVerificationCode(req));
    }

    @PutMapping("/accounts/password")
    public ResponseData<Void> resetPassword(@Validated @RequestBody ResetPasswordReq req) {
        service.resetPassword(req);
        return ResponseData.success();
    }
}
