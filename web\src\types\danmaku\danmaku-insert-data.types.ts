/**
 * 弹幕插入数据类型定义
 */

import type { CustomDanmu } from './custom-danmu.types'

/**
 * 弹幕插入数据接口
 */
export interface DanmakuInsertData {
  /** 评论ID */
  commentId?: string
  /** 弹幕内容 */
  content: string | CustomDanmu
  /** 弹幕类型 */
  type?: string
  /** 弹幕时间戳 */
  timestamp?: number
  /** 用户信息 */
  user?: {
    id: string
    name: string
    avatar?: string
  }
}

// 重新导出DanmakuAddData以保持向后兼容性
export type { DanmakuAddData } from './danmaku-add-data.types'
