import { ref, computed, type Ref, type ComputedRef } from 'vue'

import message from '@/utils/ui/message'

/**
 * 通知类型枚举
 */
export enum NotificationType {
  /** 信息 */
  INFO = 'info',
  /** 成功 */
  SUCCESS = 'success',
  /** 警告 */
  WARNING = 'warning',
  /** 错误 */
  ERROR = 'error',
}

/**
 * 通知项类型
 */
interface NotificationItem {
  /** 通知ID */
  id: string
  /** 通知类型 */
  type: NotificationType
  /** 通知标题 */
  title: string
  /** 通知内容 */
  content: string
  /** 创建时间 */
  createdAt: Date
  /** 是否已读 */
  isRead: boolean
  /** 是否持久显示 */
  persistent?: boolean
}

/**
 * 通知组合式函数返回值类型
 */
interface UseNotificationReturn {
  /** 通知列表 */
  notifications: Ref<NotificationItem[]>
  /** 未读通知数量 */
  unreadCount: ComputedRef<number>
  /** 是否有未读通知 */
  hasUnread: ComputedRef<boolean>
  /** 添加通知 */
  addNotification: (
    type: NotificationType,
    title: string,
    content: string,
    persistent?: boolean,
  ) => string
  /** 移除通知 */
  removeNotification: (id: string) => void
  /** 标记通知为已读 */
  markAsRead: (id: string) => void
  /** 标记所有通知为已读 */
  markAllAsRead: () => void
  /** 清除所有通知 */
  clearAll: () => void
  /** 清除已读通知 */
  clearRead: () => void
  /** 显示成功消息 */
  showSuccess: (title: string, content?: string) => string
  /** 显示错误消息 */
  showError: (title: string, content?: string) => string
  /** 显示警告消息 */
  showWarning: (title: string, content?: string) => string
  /** 显示信息消息 */
  showInfo: (title: string, content?: string) => string
}

/**
 * 通知管理组合式函数
 * 提供应用内通知的创建、管理和显示功能
 */
export function useNotification(): UseNotificationReturn {
  // 通知列表
  const notifications = ref<NotificationItem[]>([])

  // 未读通知数量
  const unreadCount = computed(() => {
    return notifications.value.filter((notification) => !notification.isRead).length
  })

  // 是否有未读通知
  const hasUnread = computed(() => unreadCount.value > 0)

  /**
   * 生成唯一ID
   * @returns 唯一标识符
   */
  const generateId = (): string => {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 添加通知
   * @param type 通知类型
   * @param title 通知标题
   * @param content 通知内容
   * @param persistent 是否持久显示
   * @returns 通知ID
   */
  const addNotification = (
    type: NotificationType,
    title: string,
    content: string,
    persistent: boolean = false,
  ): string => {
    const id = generateId()
    const notification: NotificationItem = {
      id,
      type,
      title,
      content,
      createdAt: new Date(),
      isRead: false,
      persistent,
    }

    notifications.value.unshift(notification)

    // 如果不是持久通知，5秒后自动移除
    if (!persistent) {
      setTimeout(() => {
        removeNotification(id)
      }, 5000)
    }

    return id
  }

  /**
   * 移除通知
   * @param id 通知ID
   */
  const removeNotification = (id: string): void => {
    const index = notifications.value.findIndex((notification) => notification.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  /**
   * 标记通知为已读
   * @param id 通知ID
   */
  const markAsRead = (id: string): void => {
    const notification = notifications.value.find((n) => n.id === id)
    if (notification) {
      notification.isRead = true
    }
  }

  /**
   * 标记所有通知为已读
   */
  const markAllAsRead = (): void => {
    notifications.value.forEach((notification) => {
      notification.isRead = true
    })
  }

  /**
   * 清除所有通知
   */
  const clearAll = (): void => {
    notifications.value = []
  }

  /**
   * 清除已读通知
   */
  const clearRead = (): void => {
    notifications.value = notifications.value.filter((notification) => !notification.isRead)
  }

  /**
   * 显示成功消息
   * @param title 消息标题
   * @param content 消息内容
   * @returns 通知ID
   */
  const showSuccess = (title: string, content: string = ''): string => {
    message.success(title)
    return addNotification(NotificationType.SUCCESS, title, content)
  }

  /**
   * 显示错误消息
   * @param title 消息标题
   * @param content 消息内容
   * @returns 通知ID
   */
  const showError = (title: string, content: string = ''): string => {
    message.error(title)
    return addNotification(NotificationType.ERROR, title, content, true) // 错误消息持久显示
  }

  /**
   * 显示警告消息
   * @param title 消息标题
   * @param content 消息内容
   * @returns 通知ID
   */
  const showWarning = (title: string, content: string = ''): string => {
    message.warning(title)
    return addNotification(NotificationType.WARNING, title, content)
  }

  /**
   * 显示信息消息
   * @param title 消息标题
   * @param content 消息内容
   * @returns 通知ID
   */
  const showInfo = (title: string, content: string = ''): string => {
    message.info(title)
    return addNotification(NotificationType.INFO, title, content)
  }

  return {
    notifications,
    unreadCount,
    hasUnread,
    addNotification,
    removeNotification,
    markAsRead,
    markAllAsRead,
    clearAll,
    clearRead,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  }
}
