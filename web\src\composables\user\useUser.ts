import { ref, computed } from 'vue'

import fileApi from '@/api/file'
import {
  createLoginUser,
  type LoginUserCreationParams,
} from '@/types/user/login-user-creation.types'
import type { UseUserReturn } from '@/types/user/use-user-return.types'
import type { UserInfo } from '@/types/user/user-info.types'
import type { UserStats } from '@/types/user/user-stats.types'
import localStorage from '@/utils/storage/local-storage'

/**
 * 用户管理组合式函数
 * 提供用户信息管理、统计数据、权限检查等功能
 */
export function useUser(): UseUserReturn {
  // 用户信息
  const userInfo = ref<UserInfo | null>(null)

  // 用户统计信息
  const userStats = ref<UserStats>({
    articleCount: 0,
    commentCount: 0,
    likeCount: 0,
    favoriteCount: 0,
  })

  // 是否已登录
  const isLoggedIn = computed(() => userInfo.value !== null)

  // 用户头像URL
  const avatarUrl = computed(() => {
    return getAvatarUrl(userInfo.value?.avatar)
  })

  // 用户等级显示
  const levelDisplay = computed(() => {
    if (!userInfo.value) return 'Lv0'
    return `Lv${userInfo.value.level}`
  })

  // 是否为高级用户（等级5以上）
  const isVipUser = computed(() => {
    return userInfo.value ? userInfo.value.level >= 5 : false
  })

  /**
   * 获取用户信息
   * 优先从内存获取，如果没有则从本地存储获取
   * @returns 用户信息或null
   */
  const getUserInfo = (): UserInfo | null => {
    if (userInfo.value) {
      return userInfo.value
    }

    // 从本地存储获取用户信息
    const storedUser = localStorage.getLoginUser()
    if (storedUser) {
      // 将 LoginUser 转换为 UserInfo
      userInfo.value = {
        id: storedUser.id,
        username: storedUser.username,
        level: storedUser.level,
        avatar: storedUser.avatar,
        email: storedUser.email,
      }
      return userInfo.value
    }

    return null
  }

  /**
   * 更新用户信息
   * @param info 要更新的用户信息
   */
  const updateUserInfo = (info: Partial<UserInfo>): void => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...info }
      // 将用户信息转换为LoginUser格式
      const loginUserCreationParams: LoginUserCreationParams = {
        id: userInfo.value.id,
        username: userInfo.value.username,
        level: userInfo.value.level,
        avatar: userInfo.value.avatar,
        email: userInfo.value.email,
        bio: userInfo.value.bio,
        createdAt: userInfo.value.createdAt,
        lastLoginAt: userInfo.value.lastLoginAt,
      }
      const loginUserData = createLoginUser(loginUserCreationParams)
      localStorage.setLoginUser(loginUserData)
    } else if (info.id) {
      // 如果当前没有用户信息但提供了完整信息，则创建新的用户信息
      userInfo.value = info as UserInfo
      // 将用户信息转换为LoginUser格式
      const loginUserCreationParams: LoginUserCreationParams = {
        id: userInfo.value.id,
        username: userInfo.value.username,
        level: userInfo.value.level,
        avatar: userInfo.value.avatar,
        email: userInfo.value.email,
        bio: userInfo.value.bio,
        createdAt: userInfo.value.createdAt,
        lastLoginAt: userInfo.value.lastLoginAt,
      }
      const loginUserData = createLoginUser(loginUserCreationParams)
      localStorage.setLoginUser(loginUserData)
    }
  }

  /**
   * 更新用户统计信息
   * @param stats 要更新的统计信息
   */
  const updateUserStats = (stats: Partial<UserStats>): void => {
    userStats.value = { ...userStats.value, ...stats }
  }

  /**
   * 清除用户数据
   * 清除内存和本地存储中的用户数据
   */
  const clearUserData = (): void => {
    userInfo.value = null
    userStats.value = {
      articleCount: 0,
      commentCount: 0,
      likeCount: 0,
      favoriteCount: 0,
    }
    localStorage.removeLoginUser()
  }

  /**
   * 获取用户头像URL
   * @param avatarUri 头像URI
   * @returns 完整的头像URL
   */
  const getAvatarUrl = (avatarUri?: string): string => {
    if (!avatarUri) {
      // 返回默认头像
      return '/default-avatar.png'
    }
    return fileApi.getResourceURL(avatarUri)
  }

  /**
   * 检查用户权限等级
   * @param requiredLevel 所需的最低等级
   * @returns 是否满足等级要求
   */
  const checkUserLevel = (requiredLevel: number): boolean => {
    const user = getUserInfo()
    return user ? user.level >= requiredLevel : false
  }

  // 初始化时从本地存储加载用户信息
  getUserInfo()

  return {
    userInfo,
    userStats,
    isLoggedIn,
    avatarUrl,
    levelDisplay,
    isVipUser,
    getUserInfo,
    updateUserInfo,
    updateUserStats,
    clearUserData,
    getAvatarUrl,
    checkUserLevel,
  }
}
