<template>
  <div v-show="show" class="article-info-container">
    <div class="article-header">
      <div class="article-header-content-wrapper" style="max-width: 80%">
        <div class="article-header-content">
          <NSkeleton :width="600" text size="large" style="max-width: 100%; margin: 1.25rem 0" />
          <div class="article-tag-container">
            <NSkeleton style="width: 60%; margin-bottom: 0.5rem" round text size="small" />
          </div>
        </div>
      </div>
      <div class="flex-column-center" style="width: 80%; gap: 0.25rem">
        <NSkeleton
          style="width: 30%; margin-bottom: 0.25rem"
          text
          :height="10"
          size="small"
          :repeat="3"
        />
      </div>
      <div class="action-buttons-container">
        <div class="interaction-container">
          <NSkeleton :width="60" round style="max-width: 100%" text size="small" />
        </div>
        <div class="comment-count-container" style="margin-right: 0">
          <NSkeleton :height="20" :width="100" round style="max-width: 100%" text size="small" />
        </div>
      </div>
    </div>
    <div class="article-content flex-column-gap24">
      <NSkeleton style="width: 100%" round text size="large" :repeat="8" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { NSkeleton } from 'naive-ui'

defineProps({
  show: {
    type: Boolean,
    default: false,
  },
})
</script>
