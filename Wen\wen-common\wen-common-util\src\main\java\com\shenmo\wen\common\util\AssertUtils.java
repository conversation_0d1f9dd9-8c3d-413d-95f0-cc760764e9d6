package com.shenmo.wen.common.util;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.shenmo.wen.common.exception.BaseException;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnumOption;
import com.shenmo.wen.common.exception.enumeration.ExpEnumCodeFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

import java.lang.reflect.InvocationTargetException;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * 断言工具类
 *
 * <AUTHOR>
 */
public abstract class AssertUtils {

    /**
     * 如果不满足断言标记抛出异常
     *
     * @param flag    断言标记
     * @param message 异常信息
     * <AUTHOR>
     */
    public static void isTrue(boolean flag, String message) {

        isTrue(flag, HttpStatus.INTERNAL_SERVER_ERROR, message);
    }

    /**
     * 如果不满足断言标记抛出异常
     *
     * @param flag       断言标记
     * @param httpStatus http状态
     * @param message    异常信息
     * <AUTHOR>
     */
    public static void isTrue(boolean flag, HttpStatus httpStatus, String message) {

        isTrue(flag, ExceptionEnumOption.of(httpStatus, message));
    }

    /**
     * 如果不满足断言标记抛出异常
     *
     * @param flag    断言标记
     * @param expEnum 异常枚举
     * <AUTHOR>
     */
    public static void isTrue(boolean flag, ExceptionEnum expEnum) {

        final Class<? extends BaseException> cls = ExpEnumCodeFactory.getExceptionType(expEnum.getClass());
        BaseException baseException;
        try {
            baseException = cls.getConstructor(ExceptionEnum.class).newInstance(expEnum);
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException
                | NoSuchMethodException e) {
            baseException = new BaseException(expEnum);
        }
        isTrue(flag, baseException);
    }

    /**
     * 如果不满足断言标记抛出异常
     *
     * @param flag          断言标记
     * @param baseException 通用异常{@link BaseException}
     * <AUTHOR>
     */
    public static void isTrue(boolean flag, BaseException baseException) {
        if (!flag) {

            throw baseException;
        }
    }

    /**
     * 不允许对象为null
     * <p>
     * 如果{@link Objects#isNull(Object)}抛出异常
     *
     * @param obj           对象
     * @param baseException 通用异常{@link BaseException}
     * <AUTHOR>
     */
    public static void isNotNull(Object obj, BaseException baseException) {

        if (Objects.isNull(obj)) {

            throw baseException;
        }
    }

    /**
     * 不允许对象为null
     * <p>
     * 如果{@link Objects#isNull(Object)}抛出异常
     *
     * @param obj     对象
     * @param expEnum 异常枚举
     * <AUTHOR>
     */
    public static void isNotNull(Object obj, ExceptionEnum expEnum) {

        isTrue(Objects.nonNull(obj), expEnum);
    }

    /**
     * 不允许对象不为null
     * <p>
     * 如果{@link Objects#nonNull(Object)}抛出异常
     *
     * @param obj           对象
     * @param baseException 通用异常{@link BaseException}
     * <AUTHOR>
     */
    public static void isNull(Object obj, BaseException baseException) {

        if (Objects.nonNull(obj)) {

            throw baseException;
        }
    }

    /**
     * 不允许集合为空
     * <p>
     * 如果{@link CollectionUtils#isNotEmpty(Collection)}抛出异常
     *
     * @param collection 集合{@link Collection}
     * @param expEnum    异常枚举
     * <AUTHOR>
     */
    public static void isNotEmpty(Collection<?> collection, ExceptionEnum expEnum) {

        isTrue(CollectionUtils.isNotEmpty(collection), expEnum);
    }

    /**
     * 不允许集合为空
     * <p>
     * 如果{@link CollectionUtils#isNotEmpty(Map)}抛出异常
     *
     * @param map     集合{@link Map}
     * @param expEnum 异常枚举
     * <AUTHOR>
     */
    public static void isNotEmpty(Map<?, ?> map, ExceptionEnum expEnum) {

        isTrue(CollectionUtils.isNotEmpty(map), expEnum);
    }

    /**
     * 不允许集合为空
     * <p>
     * 如果{@link CollectionUtils#isEmpty(Collection)}抛出异常
     *
     * @param collection    集合{@link Collection}
     * @param baseException 通用异常{@link BaseException}
     * <AUTHOR>
     */
    public static void isNotEmpty(Collection<?> collection, BaseException baseException) {

        if (CollectionUtils.isEmpty(collection)) {

            throw baseException;
        }
    }

    /**
     * 不允许集合不为空
     * <p>
     * 如果{@link CollectionUtils#isNotEmpty(Collection)}抛出异常
     *
     * @param collection    集合{@link Collection}
     * @param baseException 通用异常{@link BaseException}
     * <AUTHOR>
     */
    public static void isEmpty(Collection<?> collection, BaseException baseException) {

        if (CollectionUtils.isNotEmpty(collection)) {

            throw baseException;
        }
    }

    /**
     * 不允许集合为空
     * <p>
     * 如果{@link CollectionUtils#isEmpty(Map)}抛出异常
     *
     * @param map           集合{@link Map}
     * @param baseException 通用异常{@link BaseException}
     * <AUTHOR>
     */
    public static void isNotEmpty(Map<?, ?> map, BaseException baseException) {

        if (CollectionUtils.isEmpty(map)) {

            throw baseException;
        }
    }

    /**
     * 不允许集合不为空
     * <p>
     * 如果{@link CollectionUtils#isNotEmpty(Map)}抛出异常
     *
     * @param map           集合{@link Map}
     * @param baseException 通用异常{@link BaseException}
     * <AUTHOR>
     */
    public static void isEmpty(Map<?, ?> map, BaseException baseException) {

        if (CollectionUtils.isNotEmpty(map)) {

            throw baseException;
        }
    }

    /**
     * 不允许字符序列为空
     * <p>
     * 如果{@link StringUtils#isBlank(CharSequence)}抛出异常
     *
     * @param charSequence  字符序列{@link CharSequence}
     * @param baseException 通用异常{@link BaseException}
     * <AUTHOR>
     */
    public static void isNotBlank(CharSequence charSequence, BaseException baseException) {

        if (StringUtils.isBlank(charSequence)) {

            throw baseException;
        }
    }

    /**
     * 不允许字符序列不为空
     * <p>
     * 如果{@link StringUtils#isNotBlank(CharSequence)}抛出异常
     *
     * @param charSequence  字符序列{@link CharSequence}
     * @param baseException 通用异常{@link BaseException}
     * <AUTHOR>
     */
    public static void isBlank(CharSequence charSequence, BaseException baseException) {

        if (StringUtils.isNotBlank(charSequence)) {

            throw baseException;
        }
    }
}
