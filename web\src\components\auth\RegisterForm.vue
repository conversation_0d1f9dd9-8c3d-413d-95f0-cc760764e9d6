<!--
  注册表单组件

  功能说明：
  - 用户注册表单
  - 集成 Turnstile 人机验证
  - 邮箱验证码发送功能
  - 表单验证和错误提示
  - 密码确认验证
-->
<template>
  <NForm
    label-placement="left"
    :model="form"
    :rules="registerFormRules"
    ref="formRef"
    :label-width="100"
    class="register-form"
  >
    <NFormItem label="用户名" path="username">
      <NInput
        class="register-form-ipt"
        v-model:value="form.username"
        placeholder="请输入用户名"
        @keyup.enter="$emit('register')"
      />
    </NFormItem>
    <NFormItem label="手机号" path="phone">
      <NInput
        class="register-form-ipt"
        maxlength="11"
        v-model:value="form.phone"
        @input="registerInputNum"
        placeholder="请输入手机号"
        @keyup.enter="$emit('register')"
      />
    </NFormItem>
    <NFormItem label="邮箱" path="email">
      <NInput
        class="register-form-ipt"
        v-model:value="form.email"
        placeholder="请输入邮箱地址"
        @keyup.enter="$emit('register')"
      />
    </NFormItem>
    <NFormItem label="邮箱验证码" path="emailCode">
      <EmailCodeInput
        v-model:value="form.emailCode"
        :email-code-state="emailCodeState"
        :disabled="!isValidEmail || !isTurnstileVerified"
        button-type="primary"
        @send-code="$emit('send-email-code', EmailCodeType.REGISTER)"
        @keyup.enter="$emit('register')"
      />
    </NFormItem>
    <NFormItem label="密码" path="password">
      <NInput
        class="register-form-ipt"
        v-model:value="form.password"
        type="password"
        placeholder="请输入密码"
        show-password-on="click"
        @keyup.enter="$emit('register')"
      />
    </NFormItem>
    <NFormItem label="确认密码" path="reenteredPassword">
      <NInput
        class="register-form-ipt"
        v-model:value="form.reenteredPassword"
        type="password"
        :disabled="!form.password"
        placeholder="请确认密码"
        show-password-on="click"
        @keyup.enter="$emit('register')"
      />
    </NFormItem>
    <NFormItem label="职业" path="job">
      <NInput
        class="register-form-ipt"
        v-model:value="form.job"
        placeholder="请输入职业"
        @keyup.enter="$emit('register')"
      />
    </NFormItem>

    <!-- Turnstile 验证组件 -->
    <TurnstileVerification
      ref="turnstileRef"
      @success="$emit('turnstile-success', $event)"
      @error="$emit('turnstile-error')"
    />

    <!-- 按钮容器 -->
    <div class="register-form-btn">
      <NButton class="login-btn" type="info" @click="$emit('register')">注册并登录</NButton>
      <NButton class="flip-btn" @click="$emit('flip-card')">登录</NButton>
    </div>
  </NForm>
</template>

<script lang="ts" setup>
import { NForm, NFormItem, NButton, NInput } from 'naive-ui'
import { ref, computed } from 'vue'

import TurnstileVerification from '@/components/verification/TurnstileVerification.vue'
import type { EmailCodeState } from '@/types/component/email-code-state.types'
import type { NFormRef } from '@/types/component/form-element-ref.types'
import type { RegisterFormData } from '@/types/component/register-form-data.types'
import { EmailCodeType } from '@/types/email/email-code-type.types'
import type { FormRules } from '@/types/form/form-rules.types'

import EmailCodeInput from './EmailCodeInput.vue'

import type { FormItemRule } from 'naive-ui'

// 定义组件 Props
interface RegisterFormProps {
  form: RegisterFormData
  emailCodeState: EmailCodeState
  isValidEmail: boolean
  isTurnstileVerified: boolean
}

const props = defineProps<RegisterFormProps>()

// 定义组件 Emits
interface RegisterFormEmits {
  'update:form': [value: RegisterFormData]
  register: []
  'flip-card': []
  'send-email-code': [type: EmailCodeType]
  'turnstile-success': [token: string]
  'turnstile-error': []
}

const emit = defineEmits<RegisterFormEmits>()

// 组件引用
const formRef = ref<NFormRef | null>(null)
const turnstileRef = ref()

// 双向绑定
const form = computed({
  get: () => props.form,
  set: (value) => emit('update:form', value),
})

/**
 * 验证密码开头是否匹配
 */
const validatePasswordStartWith = (rule: FormItemRule, value: string): boolean => {
  return (
    !!props.form.password &&
    props.form.password.startsWith(value) &&
    props.form.password.length >= value.length
  )
}

/**
 * 验证两次密码是否相同
 */
const validatePasswordSame = (rule: FormItemRule, value: string): boolean => {
  return value === props.form.password
}

/**
 * 注册表单验证规则
 */
const registerFormRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 10, message: '用户名长度在 3 到 10 个字符之间', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '手机号不能为空', trigger: 'blur' },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入有效的手机号',
      trigger: 'blur',
    },
  ],
  email: [
    { required: true, message: '邮箱不能为空', trigger: 'blur' },
    {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: '请输入有效的邮箱地址',
      trigger: 'blur',
    },
  ],
  emailCode: [
    { required: true, message: '请输入邮箱验证码', trigger: 'blur' },
    {
      pattern: /^\d{6}$/,
      message: '验证码格式不正确',
      trigger: 'blur',
    },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少需要6个字符', trigger: 'blur' },
  ],
  reenteredPassword: [
    {
      required: true,
      message: '请再次输入密码',
      trigger: ['input', 'blur'],
    },
    {
      validator: validatePasswordStartWith,
      message: '两次密码输入不一致',
      trigger: 'input',
    },
    {
      validator: validatePasswordSame,
      message: '两次密码输入不一致',
      trigger: ['blur', 'password-input'],
    },
  ],
  job: [
    { required: true, message: '请输入职业', trigger: 'blur' },
    { min: 2, max: 8, message: '职业名长度在 2 到 8 个字符之间', trigger: 'blur' },
  ],
}

/**
 * 注册表单手机号输入过滤
 */
const registerInputNum = (e: string): string => {
  return (form.value.phone = e.replace(/\D/g, ''))
}

/**
 * 表单验证方法
 */
const validate = (callback?: (errors?: boolean) => void) => {
  return formRef.value?.validate(callback)
}

/**
 * 重置表单验证
 */
const restoreValidation = () => {
  formRef.value?.restoreValidation()
}

/**
 * 重置 Turnstile
 */
const resetTurnstile = () => {
  turnstileRef.value?.reset()
}

// 暴露方法给父组件
defineExpose({
  validate,
  restoreValidation,
  resetTurnstile,
})
</script>

<style lang="scss" scoped>
@use '@/styles/auth/register-form';
</style>
