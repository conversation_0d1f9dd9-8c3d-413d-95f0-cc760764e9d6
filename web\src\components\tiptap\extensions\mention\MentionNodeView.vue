<template>
  <span
    class="mention"
    :data-type="'mention'"
    :data-id="node.attrs.id"
    :data-label="node.attrs.label"
    :data-avatar="node.attrs.avatar || ''"
    contenteditable="false"
  >
    <span class="mention-name">@{{ node.attrs.label }}</span>
    <img
      v-if="node.attrs.avatar"
      :src="avatarUrl"
      :alt="node.attrs.label"
      class="mention-avatar"
      loading="lazy"
    />
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

import fileApi from '@/api/file'

import type { NodeViewProps } from '@tiptap/core'
import type { Node } from '@tiptap/pm/model'

interface Props extends Omit<NodeViewProps, 'node'> {
  node: Node & {
    attrs: {
      id: string
      label: string
      avatar?: string
    }
  }
}

const props = defineProps<Props>()

/**
 * 计算头像 URL
 */
const avatarUrl = computed(() => {
  if (!props.node.attrs.avatar) return ''
  return fileApi.getResourceURL(props.node.attrs.avatar)
})

// 实现必要的 NodeViewProps 方法
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const updateAttributes = (attrs: Record<string, unknown>) => {
  props.updateAttributes(attrs)
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const deleteNode = () => {
  props.deleteNode()
}
</script>

<style lang="scss" scoped>
@use '@/styles/tiptap/tiptap-editor';
</style>
