/*
 * CreateButton 组件样式
 * 创建按钮组件的样式定义，包括旋转缩放动画
 */

.create-button {
  transition: transform 1.5s cubic-bezier(0.34, 1.56, 0.64, 1); /* 使用回弹效果的贝塞尔曲线 */
  transform-origin: center;
  will-change: transform;

  &.is-rotating {
    animation: rotate-and-scale 1.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  }
}

@keyframes rotate-and-scale {
  0% {
    transform: rotate(0deg) scale(1);
  }

  50% {
    transform: rotate(180deg) scale(1.5);
  }

  100% {
    transform: rotate(360deg) scale(1);
  }
}
