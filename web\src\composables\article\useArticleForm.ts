import { ref, computed, nextTick, type Ref, type ComputedRef } from 'vue'

import articleApi from '@/api/article'
import { ArticlePublishedScope } from '@/constants/article/article-published-scope.constants'
import { ARTICLE_SUBMIT, ARTICLE_QUICK_SAVE } from '@/constants/article/frequency-key.constants'
import type { ResponseData } from '@/types/api/response-data.types'
import { type ArticleForm } from '@/types/article/article-form.types'
import type { ArticleSaveResponse } from '@/types/article/article-save-response.types'
import { type Article } from '@/types/article/article.types'
import type { SearchUserSelectExpose } from '@/types/component/search-user-select-expose.types'
import type { TiptapEditorComponentRef } from '@/types/component/tiptap-editor-ref.types'
import { type EditorContent } from '@/types/editor/editor-validation.types'
import {
  validateEditorContent,
  checkLoadingState,
  setLoadingState,
} from '@/utils/editor/editor-validation'
import logger from '@/utils/log/log'
import frequencyLimit from '@/utils/performance/frequency-limit'
import localStorage from '@/utils/storage/local-storage'
import tiptap from '@/utils/tiptap/tiptap'
import message from '@/utils/ui/message'

/**
 * 文章表单组合式函数返回值类型
 */
interface UseArticleFormReturn {
  /** 文章表单数据 */
  articleForm: Ref<ArticleForm>
  /** 文章表单引用 */
  articleFormRef: Ref<{ validate: () => Promise<void> } | null>
  /** 文章编辑器引用 */
  articleTiptapEditorRef: Ref<TiptapEditorComponentRef | null>
  /** 分享用户选择器引用 */
  shareUserSelectRef: Ref<SearchUserSelectExpose | null>
  /** 生成等级选项 */
  generateCommentLevel: ComputedRef<Array<{ label: string; value: number }>>
  /** 重置表单 */
  resetArticleForm: () => void
  /** 设置表单数据 */
  setFormData: (articleData: Article) => void
  /** 提交表单 */
  submitArticleForm: (
    isEditingArticle: { value: boolean },
    submitLoading: { value: boolean },
    isArticleDialogVisible: { value: boolean },
    emit: (event: 'success') => void,
  ) => void
  /** 快速保存表单 */
  quickSaveArticleForm: (
    isEditingArticle: { value: boolean },
    quickSaveLoading: { value: boolean },
    isArticleDialogVisible: { value: boolean },
    emit: (event: 'success') => void,
  ) => void
}

/**
 * 文章表单管理组合式函数
 * 提供文章表单的数据管理、验证和提交功能
 */
export function useArticleForm(): UseArticleFormReturn {
  // 表单数据和引用
  const articleForm = ref<ArticleForm>(getInitArticleForm())
  const articleFormRef = ref<{ validate: () => Promise<void> } | null>(null)
  const articleTiptapEditorRef = ref<TiptapEditorComponentRef | null>(null)
  const shareUserSelectRef = ref<SearchUserSelectExpose | null>(null)

  // 初始化表单数据
  function getInitArticleForm(): ArticleForm {
    return {
      id: '',
      title: '',
      tags: [],
      operationLevel: 0,
      publishedScope: ArticlePublishedScope.PERSONAL,
      shareUsers: [],
      contentObj: {},
    }
  }

  // 生成等级选项
  const generateCommentLevel = computed(() => {
    const optionsArray = []
    const userLevel = localStorage.getLoginUser()?.level ?? 0
    for (let i = 0; i <= userLevel; i++) {
      optionsArray.push({
        label: 'Lv' + i,
        value: i,
      })
    }
    return optionsArray
  })

  // 重置表单
  const resetArticleForm = () => {
    articleForm.value = getInitArticleForm()
    shareUserSelectRef.value?.reset()
    // 清空 Tiptap 编辑器内容
    const editor = articleTiptapEditorRef.value?.editor
    if (editor) {
      editor.commands.clearContent(true) // true 表示同时触发更新事件
    }
  }

  // 设置表单数据（用于编辑模式）
  const setFormData = (articleData: Article) => {
    articleForm.value = {
      id: articleData.id,
      title: articleData.title,
      tags: articleData.tags,
      operationLevel: articleData.operationLevel,
      publishedScope: articleData.publishedScope,
      contentObj: articleData.contentObj,
      shareUsers: articleData.shareUsers || [],
    }
    logger.debug('edit article form: ', articleForm.value)
  }

  // 提交表单
  const submitArticleForm = (
    isEditingArticle: { value: boolean },
    submitLoading: { value: boolean },
    isArticleDialogVisible: { value: boolean },
    emit: (event: 'success') => void,
  ) => {
    if (submitLoading.value) {
      return
    }
    // 确保编辑器引用在 debounce 延迟期间不会丢失
    const currentEditor = articleTiptapEditorRef.value?.editor
    frequencyLimit.debounce(
      ARTICLE_SUBMIT,
      () => {
        // 如果编辑器引用丢失，恢复之前保存的引用
        if (!articleTiptapEditorRef.value?.editor && currentEditor) {
          // 使用unknown进行安全的类型转换
          articleTiptapEditorRef.value = {
            editor: currentEditor,
            setContent: () => {},
            clearContent: () => {},
            getMarkdown: () => undefined,
            handleSave: () => {},
          } as unknown as TiptapEditorComponentRef
        }
        saveArticle(true, isEditingArticle, submitLoading, isArticleDialogVisible, emit) // 保存并关闭弹窗
      },
      300,
    )
  }

  // 快速保存方法（不关闭弹框）
  const quickSaveArticleForm = (
    isEditingArticle: { value: boolean },
    quickSaveLoading: { value: boolean },
    isArticleDialogVisible: { value: boolean },
    emit: (event: 'success') => void,
  ) => {
    if (quickSaveLoading.value) {
      return
    }
    frequencyLimit.debounce(
      ARTICLE_QUICK_SAVE,
      () => {
        saveArticle(false, isEditingArticle, quickSaveLoading, isArticleDialogVisible, emit) // 保存但不关闭弹窗
      },
      300,
    )
  }

  // 通用保存文章方法
  const saveArticle = (
    closeDialogAfterSave: boolean,
    isEditingArticle: { value: boolean },
    loadingRef: { value: boolean },
    isArticleDialogVisible: { value: boolean },
    emit: (event: 'success') => void,
  ) => {
    // 使用 naive-ui 表单验证的正确方式 - 使用 Promise 方式
    articleFormRef.value
      ?.validate()
      .then(() => {
        // 验证通过
        logger.debug('表单验证通过')

        // 1. 检查是否正在保存中
        if (!checkLoadingState(loadingRef)) {
          return
        }

        // 2. 验证编辑器内容
        const editor = articleTiptapEditorRef.value?.editor
        const contentRef = { value: articleForm.value.contentObj as EditorContent }
        const validationResult = validateEditorContent(editor, contentRef, '文章内容不能为空哦~')

        if (!validationResult.isValid) {
          return
        }

        // 3. 设置loading状态并获取最新内容
        setLoadingState(loadingRef, true)
        const latestContent = editor?.getJSON()

        // 构建保存参数
        const params = {
          title: articleForm.value.title,
          tag: articleForm.value.tags.join(','),
          operationLevel: articleForm.value.operationLevel,
          publishedScope: articleForm.value.publishedScope,
          content: latestContent ? tiptap.toJsonString(latestContent) : '', // 使用编辑器的最新内容
          shareUserIds: articleForm.value.shareUsers.map((user) => user.id),
        }

        // 编辑模式需要添加id
        if (isEditingArticle.value && articleForm.value.id) {
          Object.assign(params, { id: articleForm.value.id })
        }

        // 根据模式选择API
        const apiMethod =
          isEditingArticle.value && articleForm.value.id ? articleApi.edit : articleApi.save

        apiMethod(params)
          .then((res: ResponseData<ArticleSaveResponse>) => {
            if (res?.success) {
              // 如果是新建文章，保存成功后立即更新状态
              if (!isEditingArticle.value && res.data) {
                isEditingArticle.value = true
                articleForm.value.id = res.data.id
                // 强制更新状态，确保下次保存时使用编辑模式
                nextTick(() => {
                  logger.debug('Article state updated:', {
                    isEditing: isEditingArticle.value,
                    articleId: articleForm.value.id,
                  })
                })
              }

              if (closeDialogAfterSave) {
                isArticleDialogVisible.value = false
                message.success(isEditingArticle.value ? '修改成功' : '创建成功')
              } else {
                message.success('保存成功')
              }
              emit('success')
            }
          })
          .catch((error) => {
            // 如果是新建文章失败，重置编辑状态
            if (!isEditingArticle.value) {
              isEditingArticle.value = false
              articleForm.value.id = ''
            }
            message.error(error.message || '保存失败')
          })
          .finally(() => {
            loadingRef.value = false
          })
      })
      .catch((validationErrors: Error) => {
        // 验证失败
        logger.debug('表单验证失败:', validationErrors)
      })
  }

  return {
    // 状态
    articleForm,
    articleFormRef,
    articleTiptapEditorRef: articleTiptapEditorRef as Ref<TiptapEditorComponentRef | null>,
    shareUserSelectRef,
    generateCommentLevel,

    // 方法
    resetArticleForm,
    setFormData,
    submitArticleForm,
    quickSaveArticleForm,
  }
}
