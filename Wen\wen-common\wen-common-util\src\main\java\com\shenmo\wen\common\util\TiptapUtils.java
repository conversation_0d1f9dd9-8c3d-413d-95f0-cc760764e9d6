package com.shenmo.wen.common.util;

import com.fasterxml.jackson.databind.JsonNode;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.shenmo.wen.common.constant.TiptapConstant.MENTION;

/**
 *
 * <AUTHOR>
 */
public abstract class TiptapUtils {

    public static List<String> mentionUsers(String json) {
        return extractFieldValues(json, MENTION, "attrs.label");
    }

    /**
     * 从 JSON 中提取指定字段的值，并限定 type
     *
     * @param json        JSON 字符串
     * @param typeFilter  限定 type 值（如 "mention"）
     * @param fieldPath   目标字段路径（如 "attrs.label"）
     * @return 提取到的字段值列表
     */
    public static List<String> extractFieldValues(String json, String typeFilter, String fieldPath) {
        List<String> values = new ArrayList<>();
        final JsonNode jsonNode = JacksonUtils.toJsonNode(json);
        if (Objects.isNull(jsonNode)) {
            return values;
        }
        extractFieldValues(jsonNode, fieldPath, typeFilter, values);
        return values;
    }

    /**
     * 递归遍历 JSON 树，提取指定字段的值，并限定 type
     *
     * @param node        当前 JSON 节点
     * @param fieldPath   目标字段路径
     * @param typeFilter  限定 type 值
     * @param values      存储提取到的字段值
     */
    private static void extractFieldValues(JsonNode node, String fieldPath, String typeFilter, List<String> values) {
        if (node.isObject()) {
            // 检查当前节点是否有 type 字段，并且是否匹配 typeFilter
            if (typeFilter == null || (node.has("type") && node.get("type").asText().equals(typeFilter))) {
                // 如果匹配 typeFilter 或未限定 type，提取目标字段的值
                extractFieldValue(node, fieldPath, values);
            }

            // 递归遍历所有字段
            node.fieldNames().forEachRemaining(fieldName -> extractFieldValues(node.get(fieldName), fieldPath, typeFilter, values));
        } else if (node.isArray()) {
            // 如果当前节点是数组，递归遍历每个元素
            node.forEach(element -> extractFieldValues(element, fieldPath, typeFilter, values));
        }
    }

    /**
     * 提取目标字段的值
     *
     * @param node      当前 JSON 节点
     * @param fieldPath 目标字段路径
     * @param values    存储提取到的字段值
     */
    private static void extractFieldValue(JsonNode node, String fieldPath, List<String> values) {
        String[] pathParts = fieldPath.split("\\.", 2);
        String currentField = pathParts[0];
        String remainingPath = pathParts.length > 1 ? pathParts[1] : null;

        if (node.has(currentField)) {
            JsonNode targetNode = node.get(currentField);
            if (remainingPath == null) {
                // 如果路径结束，提取值
                if (targetNode.isValueNode()) {
                    values.add(targetNode.asText());
                }
            } else {
                // 继续递归遍历
                extractFieldValue(targetNode, remainingPath, values);
            }
        }
    }
}
