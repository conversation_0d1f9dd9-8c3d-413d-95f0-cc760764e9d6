package com.shenmo.wen.modules.user.pojo.resp;

import lombok.Data;

/**
 * 用户响应
 * <AUTHOR>
 */
@Data
public class WenUserResp {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * IP归属地
     */
    private String ipLocation;

    /**
     * 职业信息
     */
    private String job;

    /**
     * 用户等级
     */
    private Integer level;

    /**
     * 用户经验值
     */
    private Integer experience;

    /**
     * 通知接收类型
     * 0-全部，1-发布，2-修改，3-收藏，4-关闭
     */
    private Integer notificationReceiveType;

    /**
     * 创建时间
     */
    private Long ctTm;

    /**
     * 修改时间
     */
    private Long mdTm;
}
