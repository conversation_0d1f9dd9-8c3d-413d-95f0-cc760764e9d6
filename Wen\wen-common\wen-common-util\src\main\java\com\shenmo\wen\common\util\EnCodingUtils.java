package com.shenmo.wen.common.util;

import com.shenmo.wen.common.exception.BaseException;
import org.springframework.http.HttpStatus;

import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 加密帮助
 *
 * <AUTHOR>
 */
public abstract class EnCodingUtils {

    private static final char[] HEX_DIGITS = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D',
            'E', 'F' };

    /**
     * 加密
     * <p>
     * 允许异常, 并返回空字符字符串
     *
     * @param key  加密盐
     * @param text 加密内容
     * @return 加密后的内容
     * <AUTHOR>
     */
    public static String encodeAllowException(String key, String text) {
        try {
            return encode(key, text);
        } catch (Exception e) {
            return "";
        }

    }

    /**
     * 解密
     * <p>
     * 允许异常, 并返回空字符字符串
     *
     * @param key  解密盐
     * @param text 解密内容
     * @return 解密后的内容
     * <AUTHOR>
     */
    public static String decodeAllowException(String key, String text) {
        try {
            return decode(key, text);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 加密
     *
     * @param key  加密盐
     * @param text 加密内容
     * @return 加密后的内容
     * @throws Exception 加密失败异常
     * <AUTHOR>
     */
    public static String encode(String key, String text) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        final byte[] encryptData = cipher.doFinal(text.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptData);
    }

    /**
     * 解密
     *
     * @param key  解密盐
     * @param text 解密内容
     * @return 解密后的内容
     * @throws Exception 解密失败异常
     * <AUTHOR>
     */
    public static String decode(String key, String text) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        byte[] bOut = cipher.doFinal(Base64.getDecoder().decode(text));
        return new String(bOut);
    }

    /**
     * SHA-256加密
     *
     * @param string 被加密的内容
     * @return 加密后的内容
     * <AUTHOR>
     */
    public static String encryptSha256(String string) {
        byte[] btInput = string.getBytes(StandardCharsets.UTF_8);
        try {
            MessageDigest mdInst = MessageDigest.getInstance("SHA-256");
            mdInst.update(btInput);
            byte[] md = mdInst.digest();
            int j = md.length;
            char[] str = new char[j * 2];
            int k = 0;
            for (byte byte0 : md) {
                str[k++] = HEX_DIGITS[byte0 >>> 4 & 0xf];
                str[k++] = HEX_DIGITS[byte0 & 0xf];
            }
            return new String(str);
        } catch (NoSuchAlgorithmException e) {
            throw new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, "SHA-256 encrypt fail", e);
        }
    }

    /**
     * AES Sha1prng 加密
     *
     * @param content  被加密的内容
     * @param password 被加密的密钥
     * @return 加密后的内容
     * <AUTHOR>
     */
    public static String encryptAesSha1prng(String content, String password) {
        try {
            byte[] enCodeFormat = aesSha1prngSecretKeyEncoded(password);
            SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");
            // 创建密码器
            // Create a cipher
            Cipher cipher = Cipher.getInstance("AES");
            byte[] byteContent = content.getBytes(StandardCharsets.UTF_8);
            // 初始化
            // Initialize
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] result = cipher.doFinal(byteContent);
            // 加密
            // encryption
            return Base64.getEncoder().encodeToString(result);
        } catch (Exception e) {
            throw new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, "AES encrypt fail", e);
        }
    }

    /**
     * AES Sha1prng 解密
     *
     * @param str      被解密的内容
     * @param password 被解密的密钥
     * @return 解密后的内容
     * <AUTHOR>
     */
    public static String decryptAesSha1prng(String str, String password) {
        try {
            byte[] content = Base64.getMimeDecoder().decode(str);
            byte[] enCodeFormat = aesSha1prngSecretKeyEncoded(password);
            SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");
            // 创建密码器
            // Create a cipher
            Cipher cipher = Cipher.getInstance("AES");
            // 初始化
            // Initialize
            cipher.init(Cipher.DECRYPT_MODE, key);
            byte[] result = cipher.doFinal(content);
            return new String(result);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException | IllegalBlockSizeException
                | BadPaddingException e) {
            throw new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, "AES decrypt fail", e);
        }
    }

    /**
     * 获取AES Sha1prng 密钥编码
     *
     * @param password 被加密的密钥
     * @return 加密后的算法字节数组
     * @throws NoSuchAlgorithmException 找不到算法异常
     * <AUTHOR>
     */
    private static byte[] aesSha1prngSecretKeyEncoded(String password) throws NoSuchAlgorithmException {

        KeyGenerator key = KeyGenerator.getInstance("AES");
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(password.getBytes());
        key.init(128, secureRandom);
        SecretKey secretKey = key.generateKey();
        return secretKey.getEncoded();
    }

    /**
     * md5 加密
     *
     * @param str 被加密的内容
     * @return 加密后的内容
     * <AUTHOR>
     */
    public static String encryptMd5(String str) {
        String strDigest;
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] data = md5.digest(str.getBytes(StandardCharsets.UTF_8));
            strDigest = bytesToHexString(data);
        } catch (Exception ex) {
            throw new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, "MD5 encrypt fail", ex);
        }
        return strDigest;
    }

    /**
     * 字节转换成十六进制字符串
     *
     * @param src 内容字节数组
     * @return 十六进制字符串
     * <AUTHOR>
     */
    public static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder();
        if (src == null || src.length == 0) {
            return null;
        }
        for (byte b : src) {
            int v = b & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

    public static String calculateHash(InputStream inputStream) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] byteArray = new byte[1024];
            int bytesCount = 0;
            while ((bytesCount = inputStream.read(byteArray)) != -1) {
                digest.update(byteArray, 0, bytesCount);
            }
            inputStream.close();
            byte[] bytes = digest.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException | IOException e) {
            throw new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, "SHA-256 encrypt fail", e);
        }
    }
}
