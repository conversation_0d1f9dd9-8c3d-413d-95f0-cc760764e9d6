<template>
  <div class="user-info-group">
    <div class="online-notification-container">
      <NotificationBtnModal @locationComment="handleLocationComment" />
      <div class="online-info">{{ onlineCount }}<UserMultiple :size="20" /></div>
    </div>
    <UserAvatar />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'

import userApi from '@/api/user'
import NotificationBtnModal from '@/components/notification/NotificationBtnModal.vue'
import UserAvatar from '@/components/user/UserAvatar.vue'
import { UserMultiple } from '@/icons'

// 定义要触发的事件
const emit = defineEmits<{
  (e: 'locationComment', commentId: string): void
}>()
onMounted(() => {
  loadOnlineCount()
})
const route = useRoute()
watch(route, () => {
  loadOnlineCount()
})

const onlineCount = ref<number>(0)
const loadOnlineCount = () => {
  userApi.online().then((res) => {
    if (res?.data !== undefined && res?.data !== null) {
      // 后端返回的data字段直接是数字类型
      onlineCount.value = res.data || 0
    }
  })
}
// 处理定位评论事件
const handleLocationComment = (commentId: string) => {
  emit('locationComment', commentId)
}
</script>

<style lang="scss" scoped>
@use '@/styles/user/user-avatar';
</style>
