package com.shenmo.wen.app.core.user.controller;

import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeVerificationStartReq;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeVerificationSubmitReq;
import com.shenmo.wen.app.core.user.pojo.resp.WenVerificationTimeInfoResp;
import com.shenmo.wen.app.core.user.pojo.resp.WenUserPrivilegeVerificationResp;
import com.shenmo.wen.app.core.user.service.WenUserPrivilegeVerificationService;
import com.shenmo.wen.common.pojo.response.ResponseData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户特权验证控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/user-privilege-verifications")
@RequiredArgsConstructor
public class WenUserPrivilegeVerificationController {

    private final WenUserPrivilegeVerificationService verificationService;

    /**
     * 创建特权验证流程
     */
    @PostMapping
    public ResponseData<WenUserPrivilegeVerificationResp> startVerification(
            @Validated @RequestBody WenUserPrivilegeVerificationStartReq req) {
        return ResponseData.success(verificationService.startVerification(req));
    }

    /**
     * 查询验证流程状态
     */
    @GetMapping("/{id}/status")
    public ResponseData<WenUserPrivilegeVerificationResp> verificationStatus(
            @PathVariable("id") Long id) {
        return ResponseData.success(verificationService.verificationStatus(id));
    }

    /**
     * 提交验证内容（步骤二）
     */
    @PutMapping("/{id}/content")
    public ResponseData<Boolean> submitVerificationContent(
            @PathVariable("id") Long id,
            @Validated @RequestBody WenUserPrivilegeVerificationSubmitReq req) {
        return ResponseData.success(verificationService.submitVerificationContent(id, req));
    }

    /**
     * 触发步骤三（用户B访问验证页面时调用）
     */
    @PutMapping("/{id}/step-three")
    public ResponseData<Boolean> triggerStepThree(@PathVariable("id") Long id) {
        return ResponseData.success(verificationService.triggerStepThree(id));
    }

    /**
     * 启动验证计时器（用户点击邮件链接访问页面时调用）
     */
    @PostMapping("/{id}/timer")
    public ResponseData<WenVerificationTimeInfoResp> startVerificationTimer(@PathVariable("id") Long id) {
        return ResponseData.success(verificationService.startVerificationTimer(id));
    }

    /**
     * 获取验证剩余时间
     */
    @GetMapping("/{id}/remaining-time")
    public ResponseData<WenVerificationTimeInfoResp> remainingTime(@PathVariable("id") Long id) {
        return ResponseData.success(verificationService.remainingTime(id));
    }
}
