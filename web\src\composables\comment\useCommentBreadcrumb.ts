import { ref, computed, type Ref, type ComputedRef } from 'vue'

import type { Comment } from '@/types/comment/comment.types'

/**
 * 评论面包屑导航组合式函数返回值类型
 */
export interface UseCommentBreadcrumbReturn {
  /** 面包屑导航 */
  breadcrumb: Ref<Comment[]>
  /** 是否为最后一级面包屑 */
  isLastBreadcrumb: ComputedRef<boolean>
  /** 最后一级面包屑索引 */
  lastBreadcrumbIndex: ComputedRef<number>
  /** 最后一级面包屑评论 */
  lastBreadcrumbComment: ComputedRef<Comment>
  /** 是否显示回复列表按钮 */
  showReplyListBtn: Ref<boolean>
  /** 重置面包屑 */
  resetBreadcrumb: () => void
  /** 添加面包屑 */
  addBreadcrumb: (comment: Comment) => void
  /** 设置显示回复列表按钮状态 */
  setShowReplyListBtn: () => void
}

/**
 * 评论面包屑导航组合式函数
 * 提供面包屑导航的状态管理和操作方法
 */
export function useCommentBreadcrumb(): UseCommentBreadcrumbReturn {
  /**
   * 获取第一级面包屑（根级别）
   * @returns 根级别面包屑评论对象
   */
  const getFirstBreadcrumb = (): Comment => {
    return { id: '', publisher: '评论列表' } as Comment
  }

  const breadcrumb = ref<Comment[]>([getFirstBreadcrumb()])
  const isLastBreadcrumb = computed(() => breadcrumb.value.length === 3)
  const lastBreadcrumbIndex = computed(() => breadcrumb.value.length - 1)
  const lastBreadcrumbComment = computed(() => breadcrumb.value[breadcrumb.value.length - 1])

  /**
   * 重置面包屑导航到根级别
   */
  const resetBreadcrumb = (): void => {
    breadcrumb.value = [getFirstBreadcrumb()]
  }

  /**
   * 添加面包屑
   */
  const addBreadcrumb = (comment: Comment) => {
    breadcrumb.value.push(comment)
  }

  // 展示回复列表按钮状态
  const showReplyListBtn = ref(true)
  const setShowReplyListBtn = () => {
    showReplyListBtn.value = lastBreadcrumbIndex.value < 2
  }

  return {
    breadcrumb,
    isLastBreadcrumb,
    lastBreadcrumbIndex,
    lastBreadcrumbComment,
    showReplyListBtn,
    resetBreadcrumb,
    addBreadcrumb,
    setShowReplyListBtn,
  }
}
