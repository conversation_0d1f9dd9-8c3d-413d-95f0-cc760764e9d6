# Cursor Augment Code 自动化工具

这个Git仓库包含了所有与Cursor Augment Code自动化监控相关的文件。

## 版本控制

本项目使用Git进行版本控制，包含以下特性：
- 自动忽略Python缓存文件和临时文件
- 保留模板图片文件但忽略其他截图
- 忽略用户特定的配置文件

## 目录结构

```
dever/
├── gui_automation.py              # GUI界面主程序
├── cursor_augment_automation.py   # 自动化核心逻辑
├── test_button_detection.py       # 按钮检测测试
├── test_new_window_feature.py     # 新窗口功能测试
├── test_installation.py           # 安装测试
├── config.json                    # 主配置文件
├── config_single.json             # 单次执行配置
├── tasks.txt                      # 任务内容文件
├── requirements.txt               # Python依赖
├── templates/                     # 按钮模板图片
│   ├── send_button_normal.png     # 正常状态按钮模板
│   └── send_button_pause.png      # 暂停状态按钮模板
├── *.bat                          # 各种批处理启动脚本
└── README.md                      # 本说明文件
```

## 快速开始

1. **安装依赖**：
   ```bash
   pip install -r requirements.txt
   ```

2. **启动GUI界面**：
   ```bash
   python gui_automation.py
   ```

3. **新窗口功能**：
   - 默认情况下，"开启新窗口"选项是**未勾选**的
   - 未勾选时：直接在当前对话框输入内容并发送
   - 勾选后：先按Ctrl+L创建新对话，然后发送内容

## 主要功能

- **图形化配置界面**：通过GUI设置所有参数
- **按钮状态检测**：自动识别发送按钮的状态
- **循环监控模式**：可设置定时检查间隔
- **新窗口控制**：可选择是否在新对话中发送内容
- **模板匹配**：使用图像模板匹配技术识别按钮状态

## 配置说明

主要配置项在`config.json`中：

- `enable_new_window`: 是否开启新窗口模式（默认false）
- `loop_mode`: 是否启用循环监控
- `check_interval_seconds`: 检查间隔时间
- `match_threshold`: 模板匹配阈值

## 注意事项

- 确保Cursor编辑器窗口可见
- 模板图片需要准确截取按钮状态
- 建议先运行"测试按钮检测"确认模板匹配正常

## 开发

查看 [DEVELOPMENT.md](DEVELOPMENT.md) 了解开发指南和架构说明。

## 更新日志

查看 [CHANGELOG.md](CHANGELOG.md) 了解版本更新历史。
