/*
 * Article 视图组件样式
 * 文章详情页面的布局样式定义，包括文章内容区域和评论区域的布局
 */

.article-layout {
  display: flex;
  height: 100vh;
  height: 100dvh;
  max-height: 100vh;
  max-height: 100dvh;
  width: 100vw;
  width: 100dvw;
  background-color: var(--gray-3);
  overflow: hidden;
  position: relative; /* 建立定位上下文 */

  :deep(.article-info-container) {
    flex: 0 0 65vw;
    flex: 0 0 65dvw;
    width: 65%;
    padding: 1.25rem;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100vh;
    height: 100dvh;

    .article-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      position: relative;
      margin: 1.25rem 0;

      .article-header-content-wrapper {
        margin-top: 3rem;

        .article-header-content {
          display: flex;
          flex-direction: column;
          text-align: center;

          .article-tag-container {
            display: flex;
            gap: 0.5rem;
            justify-content: center;

            .article-tag {
              margin-left: 0;
            }
          }
        }
      }
    }

    .time-clickable {
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        opacity: 0.8;
        text-decoration: underline;
      }
    }

    .action-buttons-container {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      position: absolute;
      top: -5%;
      right: 1%;

      .edit-button-container {
        margin-bottom: 0.5rem;
        display: flex;
        gap: 0.25rem;
      }

      .interaction-container {
        display: flex;
        font-size: 0.8rem;
        gap: 0.4rem;
        align-items: center;
      }

      .comment-count-container {
        margin-top: 0.5rem;
        margin-right: 0.25rem;
        font-size: 0.8rem;
        display: flex;
      }
    }

    .article-content {
      padding: 1rem 0 1rem 1rem;
      border-radius: 0.5rem;
      width: 90%;
      overflow-y: auto;
      background-color: var(--white-1);

      // 确保图片在文章内容中比例正确
      :deep(.image-wrapper),
      :deep(img) {
        max-width: 100%;
        height: auto;
        object-fit: contain;
      }
    }
  }
}

@media (width <= 55rem) {
  .article-layout {
    flex-direction: column;
    overflow-y: auto; /* 恢复外层滚动 */
    height: 100vh;
    height: 100dvh;
  }

  .article-layout .article-info-container {
    flex: 0 0 auto; /* 让文章区域根据内容自适应高度 */
    width: 100%;
    min-height: 40vh; /* 减少文章区域的最小高度 */
    height: auto; /* 让高度根据内容自适应 */
    max-height: none; /* 移除最大高度限制 */
    overflow: visible; /* 让文章内容自然展示 */

    .article-content {
      width: 95%;
      padding: 1rem 0 1rem 1rem;
      max-height: 60vh; /* 限制最大高度，超出时滚动 */
      overflow-y: auto; /* 文章内容的滚动条 */
      box-sizing: border-box;

      // 小屏幕上进一步优化图片显示
      :deep(.ProseMirror) {
        p > .image-wrapper,
        p > img {
          max-width: 100% !important;
          min-width: unset !important;
          width: auto !important;
          height: auto !important;
        }
      }
    }

    .article-header {
      margin: 0.75rem 0;

      h2 {
        font-size: 1.4rem;
        word-break: break-word;
      }
    }
  }

  .article-layout .comment-info-container {
    flex: 0 0 auto; /* 改回自适应 */
    width: 100%;
    height: auto; /* 自适应高度 */
    min-height: 70vh; /* 增加评论区域最小高度 */
    overflow: visible; /* 允许内容显示 */
  }
}
