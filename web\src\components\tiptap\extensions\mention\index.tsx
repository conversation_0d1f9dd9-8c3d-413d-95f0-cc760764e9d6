import Mention from '@tiptap/extension-mention'

import fileApi from '@/api/file'
import userApi from '@/api/user'
import type { ResponseData } from '@/types/api/response-data.types'

import { MentionView } from './MentionView'

import type { SuggestionProps, SuggestionKeyDownProps } from '@tiptap/suggestion'

// 定义 @mention 节点的属性类型
export interface MentionAttributes {
  id: string
  label: string
  avatar?: string
}

const mention = Mention.extend({
  name: 'mention',

  addAttributes() {
    return {
      ...this.parent?.(),
      avatar: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-avatar'),
        renderHTML: (attributes: MentionAttributes) => {
          return attributes.avatar ? { 'data-avatar': attributes.avatar } : {}
        },
      },
    }
  },

  renderHTML({ node, HTMLAttributes }) {
    const attrs = node.attrs as MentionAttributes
    const children: Array<[string, Record<string, string>, string]> = [
      ['span', { class: 'mention-name' }, `@${attrs.label}`],
    ]

    if (attrs.avatar) {
      const avatarUrl = fileApi.getResourceURL(attrs.avatar)
      children.push([
        'img',
        {
          class: 'mention-avatar',
          src: avatarUrl,
          alt: attrs.label,
          loading: 'lazy',
        },
        '',
      ])
    }

    return [
      'span',
      {
        ...HTMLAttributes,
        'data-type': 'mention',
        'data-id': attrs.id,
        'data-label': attrs.label,
        'data-avatar': attrs.avatar || '',
        class: 'mention',
        contenteditable: 'false',
      },
      ...children,
    ]
  },
})

const mentionExtension = mention.configure({
  suggestion: {
    items: async ({ query }) => {
      if (!query) return []
      const res: ResponseData<unknown[]> = await userApi.searchUser(query)
      return res.data || []
    },

    render: () => {
      let view: MentionView | undefined

      return {
        onStart: (props: SuggestionProps) => {
          view = MentionView.create({
            editor: props.editor,
            dictionary: {
              empty: '...',
            },
          })()
          view.onStart(props)
        },

        onUpdate: (props: SuggestionProps) => {
          view?.onUpdate(props)
        },

        onKeyDown: (props: SuggestionKeyDownProps) => {
          return view?.onKeyDown(props) ?? false
        },

        onExit: () => {
          view?.onExit()
        },
      }
    },
  },
})

export default mentionExtension
