import { useMessage } from 'naive-ui'

import commentApi from '@/api/comment'
import type { ArticleListRef } from '@/types/component/article-list-ref.types'
import type { CommentDanmakuRef } from '@/types/component/comment-danmaku-ref.types'
import type { SearchCondition } from '@/types/search/search-condition.types'
import logger from '@/utils/log/log'

import type { Ref } from 'vue'

/**
 * 首页搜索操作功能
 * 提供具体的搜索执行逻辑
 */
export function useHomeSearchOperations(
  isLoading: Ref<boolean>,
  isSearching: Ref<boolean>,
  searchCondition: Ref<SearchCondition>,
  hasSearchCondition: Ref<boolean>,
  currentRequestController: { value: AbortController | null },
) {
  const message = useMessage()

  /**
   * 搜索评论数据
   * 根据搜索条件获取匹配的评论列表
   * @param commentDanmakuRef 评论弹幕组件引用
   */
  const searchComments = async (commentDanmakuRef: CommentDanmakuRef | null): Promise<void> => {
    logger.debug('searchComments 被调用:', {
      hasSearchCondition: hasSearchCondition.value,
      searchCondition: searchCondition.value,
      commentDanmakuRef: !!commentDanmakuRef,
    })

    // 如果没有搜索条件，清空弹幕并设置循环为false
    if (!hasSearchCondition.value) {
      if (commentDanmakuRef) {
        commentDanmakuRef.clearDanmaku()
        commentDanmakuRef.danmakuLoop = false
      }
      logger.debug('没有搜索条件，清空弹幕')
      return
    }

    isSearching.value = true
    isLoading.value = true

    try {
      const res = await commentApi.search(
        searchCondition.value,
        currentRequestController.value?.signal,
      )

      logger.debug('搜索评论API响应:', res)

      if (commentDanmakuRef) {
        // 根据后端实现，res.data 直接就是评论数组
        const comments = Array.isArray(res.data) ? res.data : []
        logger.debug('准备添加评论到弹幕:', { count: comments.length, comments })
        commentDanmakuRef.addCommentList(comments)
      }
    } catch (error: unknown) {
      if (!((error as Error).name === 'CanceledError' || (error as Error).message === 'canceled')) {
        message.error('加载评论失败，请稍后重试')
      }
    } finally {
      isLoading.value = false
      isSearching.value = false
      currentRequestController.value = null
    }
  }

  /**
   * 搜索文章数据
   * 根据搜索条件获取匹配的文章列表
   * @param articleListRef 文章列表组件引用
   * @param loadMore 是否为加载更多操作
   */
  const searchArticles = async (
    articleListRef: ArticleListRef | null,
    loadMore: boolean = false,
  ): Promise<void> => {
    logger.debug('searchArticles called:', {
      loadMore,
      hasRef: !!articleListRef,
      isSearching: isSearching.value,
      isLoading: isLoading.value,
      searchCondition: searchCondition.value,
    })

    if (loadMore) return // 由ArticleList组件内部处理加载更多

    if (articleListRef) {
      isSearching.value = true
      isLoading.value = true

      try {
        // 强制重置列表并加载文章
        articleListRef.resetList()
        logger.debug('loadArticles completed')
      } catch (error: unknown) {
        logger.error('searchArticles error:', error as Error)
        if (
          !((error as Error).name === 'CanceledError' || (error as Error).message === 'canceled')
        ) {
          message.error('加载文章失败，请稍后重试')
        }
      } finally {
        isLoading.value = false
        isSearching.value = false
        currentRequestController.value = null
      }
    } else {
      logger.warn('articleListRef.value is null/undefined')
    }
  }

  return {
    searchComments,
    searchArticles,
  }
}
