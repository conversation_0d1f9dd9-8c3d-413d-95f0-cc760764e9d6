<template>
  <NSelect
    v-model:value="selectedUserIds"
    filterable
    clearable
    multiple
    remote
    value-field="id"
    label-field="username"
    :options="allUserOptions"
    :loading="userSearchLoading"
    :placeholder="placeholder"
    :render-label="renderUserLabel"
    @search="handleUserSearch"
  />
</template>

/** * 用户搜索选择组件 * * 功能说明： * - 支持远程搜索用户 * - 多选用户功能 * -
自定义用户选项渲染（显示头像和用户名） * - 支持已选用户回显 * - 提供重置搜索结果功能 * * 使用场景：
* - 文章分享用户选择 * - 用户权限分配 * - 群组成员选择 */
<script setup lang="ts">
import { NSelect, NAvatar } from 'naive-ui'
import { ref, computed, h } from 'vue'

import fileApi from '@/api/file'
import userApi from '@/api/user'
import type { SearchUserSelectEmits } from '@/types/component/search-user-select-emits.types'
import type { SearchUserSelectProps } from '@/types/component/search-user-select-props.types'
import type { SearchUser } from '@/types/user/search-user.types'
import logger from '@/utils/log/log'

// 定义组件 Props，使用明确的类型定义
const props = defineProps<SearchUserSelectProps>()

// 定义组件 Emits，使用明确的类型定义
const emit = defineEmits<SearchUserSelectEmits>()

// 用户搜索状态管理
const userSearchLoading = ref(false) // 搜索加载状态
const userOptions = ref<SearchUser[]>([]) // 搜索结果选项

/**
 * 合并搜索选项和已选用户
 * 确保已选用户能够正确回显，即使不在当前搜索结果中
 */
const allUserOptions = computed(() => {
  // 复制搜索结果作为基础选项
  const allOptions = [...userOptions.value]

  // 将已选用户添加到选项中，避免重复添加
  props.modelValue.forEach((user) => {
    if (!allOptions.some((option) => option.id === user.id)) {
      allOptions.push(user)
    }
  })

  return allOptions
})

/**
 * 自定义用户选项渲染函数
 * 使用 h 函数渲染包含头像和用户名的选项
 */
const renderUserLabel = (option: SearchUser) => {
  const avatar = fileApi.getResourceURL(option.avatar as string) || ''
  logger.debug('渲染用户标签: ', option)
  return h('div', { style: { display: 'flex', alignItems: 'center' } }, [
    h(NAvatar, {
      size: 'small',
      round: true,
      objectFit: 'cover',
      src: avatar,
      fallbackSrc: '/avatar/avatar.png',
      style: {
        marginRight: '8px',
        verticalAlign: 'middle',
      },
    }),
    h('span', option.username),
  ])
}

/**
 * 选中用户ID的计算属性
 * 将用户对象数组转换为ID数组，便于 NSelect 组件使用
 */
const selectedUserIds = computed({
  get: () => props.modelValue.map((user) => user.id),
  set: (newIds: string[]) => {
    // 保留仍被选中的用户对象
    const remainingUsers = props.modelValue.filter((user) => newIds.includes(user.id))

    // 从选项中找到新选择的用户对象
    const newUsers = allUserOptions.value.filter(
      (option) =>
        newIds.includes(option.id) && !remainingUsers.some((user) => user.id === option.id),
    )

    // 触发更新事件，传递完整的用户对象数组
    emit('update:modelValue', [...remainingUsers, ...newUsers])
  },
})

/**
 * 处理用户搜索输入
 * 根据查询关键词远程搜索用户
 */
const handleUserSearch = (query: string) => {
  // 空查询时清空搜索结果
  if (!query.trim()) {
    userOptions.value = []
    return
  }

  userSearchLoading.value = true
  userApi
    .searchUser(query)
    .then((res) => {
      if (res.data) {
        userOptions.value = res.data
      }
    })
    .finally(() => {
      userSearchLoading.value = false
    })
}

/**
 * 重置搜索结果
 * 清空当前搜索选项，通常在组件重置时调用
 */
const reset = () => {
  userOptions.value = []
}

defineExpose({
  reset,
})
</script>
