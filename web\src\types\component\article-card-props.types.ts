/**
 * 文章卡片组件Props类型定义
 * 定义文章卡片组件的属性接口
 */

import type { Article } from '@/types/article/article.types'

/**
 * 文章卡片组件的 Props 类型定义
 */
export interface ArticleCardProps {
  /** 文章数据对象 */
  article: Article
  /** 卡片在列表中的索引 */
  index: number
  /** 卡片背景颜色 */
  cardColor: string
  /** 是否正在拖拽状态 */
  isDragging?: boolean
  /** 被拖拽的文章对象 */
  draggedArticle?: Article | null
  /** 拖拽悬停的卡片ID */
  dragOverCardId?: string | null
  /** 拖拽悬停位置 */
  dragOverPosition?: string | null
  /** 是否为单行卡片布局 */
  isSingleCardRow?: boolean
  /** 拖拽样式对象 */
  dragStyle?: Record<string, unknown>
}
