package com.shenmo.wen.app.core.notification.exception;

import com.shenmo.wen.common.exception.BaseException;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import org.springframework.http.HttpStatus;

/**
 *
 * <AUTHOR>
 */
public class NotificationException extends BaseException {
    public NotificationException(ExceptionEnum exceptionEnum) {
        super(exceptionEnum);
    }

    public NotificationException(HttpStatus httpStatus, String message) {
        super(httpStatus, message);
    }

    public NotificationException(HttpStatus httpStatus, String description, Throwable throwable) {
        super(httpStatus, description, throwable);
    }

    public NotificationException(HttpStatus httpStatus, String description, String message) {
        super(httpStatus, description, message);
    }
}
