package com.shenmo.wen.app.core.user.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilege;

public interface WenUserPrivilegeMapper extends BaseMapper<WenUserPrivilege> {

    default WenUserPrivilege byUserIdAndTemplateId(long userId, Long templateId) {
        final LambdaQueryWrapper<WenUserPrivilege> wrapper = Wrappers.<WenUserPrivilege>lambdaQuery()
                .eq(WenUserPrivilege::getUserId, userId);
        if (templateId != null) {
            wrapper.eq(WenUserPrivilege::getTemplateId, templateId);
        } else {
            wrapper.isNull(WenUserPrivilege::getTemplateId);
        }
        return selectOne(wrapper);
    }

    default List<WenUserPrivilege> listByTemplateIds(List<Long> templateIds) {
        return selectList(Wrappers.<WenUserPrivilege>lambdaQuery().in(WenUserPrivilege::getTemplateId, templateIds));
    }

    /**
     * 查询用户的有效特权（未过期）
     *
     * @param userId 用户ID
     * @param currentTime 当前时间
     * @return 用户的有效特权列表
     */
    default List<WenUserPrivilege> listValidByUserId(Long userId, Long currentTime) {
        return selectList(Wrappers.<WenUserPrivilege>lambdaQuery()
                .eq(WenUserPrivilege::getUserId, userId)
                .gt(WenUserPrivilege::getExpireTime, currentTime));
    }
}
