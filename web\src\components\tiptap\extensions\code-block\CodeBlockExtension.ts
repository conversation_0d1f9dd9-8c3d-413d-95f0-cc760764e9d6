import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight'
import { Selection } from '@tiptap/pm/state'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import CodeBlockNodeView from './CodeBlockNodeView.vue'

import type { NodeViewProps } from '@tiptap/core'

// 导入组件和样式
import '@/styles/tiptap/extensions/code-block/code-block.scss'

/**
 * 自定义代码块扩展
 * 使用 Vue 组件渲染代码块，集成功能栏，消除延迟渲染问题
 */
const CodeBlockExtension = CodeBlockLowlight.extend({
  // 禁用代码块的选择功能
  selectable: false,

  // 不设为原子节点，允许正常的光标导航
  atom: false,

  // 禁用拖拽功能
  draggable: false,

  // 添加自定义配置
  addOptions() {
    return {
      ...this.parent?.(),
      // 禁用代码块的编辑功能
      HTMLAttributes: {
        class: 'code-block-readonly',
        'data-selectable': 'false',
      },
      // 禁用三次回车退出代码块的默认行为
      exitOnTripleEnter: false,
      // 禁用向下箭头退出代码块的默认行为
      exitOnArrowDown: false,
    }
  },

  // 使用 Vue 组件作为节点视图
  addNodeView() {
    return VueNodeViewRenderer(
      CodeBlockNodeView as typeof CodeBlockNodeView & { new (): NodeViewProps },
    )
  },

  addKeyboardShortcuts() {
    const parentShortcuts = this.parent?.() || {}

    return {
      ...parentShortcuts,
      // 重写 Enter 键行为，禁用默认的连续换行跳出
      Enter: ({ editor }) => {
        const { state } = editor
        const { selection } = state
        const { $from, empty } = selection

        // 确保我们在代码块内部
        if (!empty || $from.parent.type !== this.type) {
          return false
        }

        // 在代码块内部，只允许普通换行，不允许跳出
        return editor.commands.first(({ commands }) => [() => commands.newlineInCode()])
      },
      // 修改 Shift+Enter 快捷键，允许在代码块内任意位置跳出到上方
      'Shift-Enter': ({ editor }) => {
        const { state } = editor
        const { selection } = state
        const { $from, empty } = selection

        // 确保我们在代码块内部
        if (!empty || $from.parent.type !== this.type) {
          return false
        }

        // 获取代码块的位置
        const pos = $from.before()

        // 在代码块前插入一个段落
        return editor
          .chain()
          .insertContentAt(pos, { type: 'paragraph' })
          .command(({ tr }) => {
            // 将光标移动到新插入的段落
            const newPos = pos
            tr.setSelection(Selection.near(tr.doc.resolve(newPos)))
            return true
          })
          .run()
      },
      // 保持 Ctrl+Enter 快捷键，用于从代码块任意位置跳出到下方
      'Ctrl-Enter': ({ editor }) => {
        const { state } = editor
        const { selection } = state
        const { $from, empty } = selection

        // 确保我们在代码块内部
        if (!empty || $from.parent.type !== this.type) {
          return false
        }

        // 获取代码块的位置
        const pos = $from.after()

        // 在代码块后插入一个段落
        return editor
          .chain()
          .insertContentAt(pos, { type: 'paragraph' })
          .command(({ tr }) => {
            // 将光标移动到新插入的段落
            const newPos = pos + 1
            tr.setSelection(Selection.near(tr.doc.resolve(newPos)))
            return true
          })
          .run()
      },
    }
  },
})

export default CodeBlockExtension
