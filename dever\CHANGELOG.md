# 更新日志

## [1.0.0] - 2025-08-12

### 新增功能
- ✨ 初始化项目，实现Cursor Augment Code自动化监控
- ✨ 添加图形化配置界面 (GUI)
- ✨ 实现按钮状态检测和模板匹配
- ✨ 添加新窗口控制功能
  - 默认不开启新窗口，直接在当前对话框发送内容
  - 可选择开启新窗口模式，使用Ctrl+L创建新对话
- ✨ 支持循环监控模式
- ✨ 添加完整的测试套件

### 技术特性
- 🔧 基于Python + tkinter的GUI界面
- 🔧 使用OpenCV进行图像模板匹配
- 🔧 支持pyautogui自动化操作
- 🔧 JSON配置文件管理
- 🔧 完整的错误处理和日志记录

### 文件结构
- `gui_automation.py` - 主GUI界面
- `cursor_augment_automation.py` - 自动化核心逻辑
- `config.json` - 主配置文件
- `templates/` - 按钮模板图片
- `test_*.py` - 测试文件
- `*.bat` - 启动脚本

### 安装和使用
1. 运行 `install.bat` 安装依赖
2. 运行 `start_gui.bat` 启动GUI界面
3. 配置模板文件和参数
4. 选择是否开启新窗口模式
5. 点击"运行自动化"开始监控
