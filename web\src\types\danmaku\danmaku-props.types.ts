/**
 * 弹幕组件属性类型定义
 */

import type { CustomDanmu } from './custom-danmu.types'

/**
 * 弹幕组件属性接口
 * 定义弹幕组件接受的所有属性
 */
export interface DanmakuProps {
  /** 弹幕数据数组 */
  danmus: CustomDanmu[]
  /** 弹幕轨道数量 */
  channels: number
  /** 是否自动播放 */
  autoplay: boolean
  /** 是否循环播放 */
  loop: boolean
  /** 是否使用插槽 */
  useSlot: boolean
  /** 防抖延迟时间 */
  debounce: number
  /** 弹幕移动速度 */
  speeds: number
  /** 弹幕字体大小 */
  fontSize: number
  /** 弹幕顶部偏移量 */
  top: number
  /** 弹幕右侧偏移量 */
  right: number
  /** 是否暂停弹幕 */
  isSuspend: boolean
  /** 额外的CSS样式 */
  extraStyle: string
  /** 索引签名，允许动态属性访问 */
  [key: string]: unknown
}
