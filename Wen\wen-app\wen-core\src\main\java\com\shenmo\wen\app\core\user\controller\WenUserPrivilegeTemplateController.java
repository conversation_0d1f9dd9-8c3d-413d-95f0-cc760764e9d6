package com.shenmo.wen.app.core.user.controller;

import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilegeTemplate;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeTemplateSaveReq;
import com.shenmo.wen.app.core.user.service.WenUserPrivilegeTemplateService;
import com.shenmo.wen.common.pojo.response.ResponseData;

import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户特权模板控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user-privilege-templates")
@RequiredArgsConstructor
public class WenUserPrivilegeTemplateController {

    private final WenUserPrivilegeTemplateService service;

    @GetMapping
    public ResponseData<List<WenUserPrivilegeTemplate>> search(@RequestParam("name") String name) {
        return ResponseData.success(service.search(name));
    }

    @PostMapping
    public ResponseData<Boolean> save(@Validated @RequestBody WenUserPrivilegeTemplateSaveReq req) {
        return ResponseData.success(service.save(req));
    }
}
