/* 代码语法高亮样式 - 主题适配版本 */

/* 基础语法高亮样式 - 使用 CSS 变量适配主题 */
.hljs-comment,
.hljs-quote {
  color: var(--code-comment);
  font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag {
  color: var(--code-keyword);
}

.hljs-subst {
  color: var(--code-text);
}

.hljs-number,
.hljs-literal,
.hljs-variable,
.hljs-template-variable {
  color: var(--code-number);
}

.hljs-string,
.hljs-doctag {
  color: var(--code-string);
}

.hljs-title,
.hljs-section,
.hljs-selector-id {
  color: var(--code-function);
  font-weight: 600;
}

.hljs-type,
.hljs-class .hljs-title {
  color: var(--code-function);
  font-weight: 600;
}

.hljs-tag,
.hljs-name,
.hljs-attribute {
  color: var(--code-tag);
}

.hljs-regexp,
.hljs-link {
  color: var(--code-string);
}

.hljs-symbol,
.hljs-bullet {
  color: var(--code-number);
}

.hljs-built_in,
.hljs-builtin-name {
  color: var(--code-builtin);
}

.hljs-meta {
  color: var(--code-meta);
}

.hljs-deletion {
  color: var(--code-deletion-color);
  background: var(--code-deletion-bg);
}

.hljs-addition {
  color: var(--code-addition-color);
  background: var(--code-addition-bg);
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: 600;
}

/* 函数名称 */
.function_ {
  color: var(--code-function);
}

/* 操作符 */
.operator {
  color: var(--code-keyword);
}

/* 属性名 */
.property {
  color: var(--code-variable);
}

/* 特定语言的语法高亮增强 */

/* JavaScript/TypeScript */
.hljs-attr {
  color: var(--code-attribute);
}

.hljs-params {
  color: var(--code-variable);
}

/* CSS */
.hljs-selector-class,
.hljs-selector-pseudo {
  color: var(--code-function);
}

.hljs-selector-attr {
  color: var(--code-attribute);
}

/* HTML */
.hljs-tag .hljs-name {
  color: var(--code-tag);
}

.hljs-tag .hljs-attr {
  color: var(--code-attribute);
}

/* Python */
.hljs-decorator {
  color: var(--code-function);
}

/* Java */
.hljs-annotation {
  color: var(--code-meta);
}

/* Markdown */
.hljs-code {
  color: var(--code-string);
}

.hljs-formula {
  color: var(--code-function);
}

/* 确保代码文本的基础颜色 */
.ProseMirror pre code {
  color: var(--code-text);
}

/* 针对不同主题的特殊处理 */
[data-theme='light'] {
  .hljs-variable {
    color: var(--code-variable);
  }

  .hljs-title.function_ {
    color: var(--code-function);
  }
}

[data-theme='dark'] {
  .hljs-variable {
    color: var(--code-variable);
  }

  .hljs-title.function_ {
    color: var(--code-function);
  }

  /* 暗色主题下的特殊调整 */
  .hljs-punctuation {
    color: var(--code-text);
  }
}
