/*
 * DanmakuRenderer 组件样式
 * 弹幕渲染器的样式定义，包括主容器、富文本元素和图片样式
 */

/* 主容器样式 */
.danmaku-renderer {
  font-size: inherit;
  line-height: inherit;
  color: var(--black);
  word-break: break-word;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: inline-block;
  vertical-align: bottom;
}

/* @提及样式 - 弹幕中保持与编辑器一致的mention样式 */
.danmaku-renderer .mention {
  /* 保持与编辑器一致的基础样式，只调整弹幕特有的属性 */
  font-weight: bold; /* 弹幕中文字更粗 */
  vertical-align: bottom; /* 弹幕中垂直对齐 */
  margin-bottom: 0.15rem;

  /* 弹幕中使用半透明背景，保持视觉一致性 */
  background: rgba(200, 200, 200, 30%);

  &:hover {
    background: rgba(180, 180, 180, 40%);
  }
}

/* @提及头像样式 - 弹幕中保持一致的头像样式 */
.danmaku-renderer .mention-avatar {
  /* 继承全局头像样式，保持一致性 */
  overflow: hidden;
  aspect-ratio: 1 / 1;
}

/* 图片占位符样式 */
.danmaku-renderer .image-placeholder {
  color: var(--gray);
  background-color: rgba(0, 0, 0, 5%);
  padding: 0 4px;
  border-radius: 3px;
}

/* 代码样式 */
.danmaku-renderer code {
  background-color: #f6f2ff;
  border-radius: 0.4rem;
  color: #181818;
  font-size: 0.85em;
  padding: 0.25em 0.3em;
}

/* 链接样式 */
.danmaku-renderer a {
  color: #56a9ff;
  text-decoration: none;
}

.danmaku-renderer a:hover {
  text-decoration: underline;
}

/* 弹幕图片样式 */
.danmaku-renderer .danmaku-image {
  display: inline-block;
  vertical-align: bottom;
  margin: 0 3px;
  border-radius: 3px;
  object-fit: contain;
  line-height: 1;
  max-height: 3rem;
  cursor: pointer;
}

/* 添加富文本基础样式 */
.danmaku-renderer strong {
  font-weight: bold;
}

.danmaku-renderer em {
  font-style: italic;
}

.danmaku-renderer u {
  text-decoration: underline;
}

.danmaku-renderer s {
  text-decoration: line-through;
}
