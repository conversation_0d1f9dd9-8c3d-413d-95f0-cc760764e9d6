package com.shenmo.wen.app.core.favorite.controller;

import com.shenmo.wen.app.core.favorite.pojo.req.WenFavoriteReq;
import com.shenmo.wen.app.core.favorite.pojo.resp.WenFavoriteResp;
import com.shenmo.wen.app.core.favorite.service.WenFavoriteService;
import com.shenmo.wen.common.pojo.response.ResponseData;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/favorites")
@RequiredArgsConstructor
public class WenFavoriteController {
    private final WenFavoriteService service;

    @PostMapping
    public ResponseData<WenFavoriteResp> toggleFavorite(@RequestBody WenFavoriteReq req) {
        return ResponseData.success(service.toggleFavorite(req));
    }
}
