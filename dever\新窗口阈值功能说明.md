# 新窗口阈值功能说明

## 功能概述

新增了新窗口阈值功能，允许用户设置一个发送次数阈值，只有当前窗口发送超过指定次数后才会开启新窗口。同时提供了两个独立的任务文件，分别用于当前窗口和新窗口。

## 新增功能

### 1. 新窗口阈值设置
- 在GUI界面中新增了"新窗口阈值"数字输入框
- 可以设置当前窗口发送多少次后才开启新窗口
- 默认阈值为3次

### 2. 任务文件管理
- **当前窗口任务文件**: 用于在当前对话框中发送的内容
- **新窗口任务文件**: 用于在新对话窗口中发送的内容
- 每个窗口可以有不同的任务内容和配置

### 3. 发送计数功能
- 自动记录当前窗口的发送次数
- 发送计数会自动保存到配置文件中
- 支持跨会话保持计数状态

### 4. 进程选择功能
- 自动检测所有打开的Cursor窗口
- 支持在多个Cursor进程中选择目标窗口
- 提供下拉框界面选择和刷新功能
- 配置会自动保存选择的窗口索引

### 5. 新对话创建优化
- 创建新对话前先点击Augment Code输入区域
- 确保焦点在正确位置再执行Ctrl+L
- 避免在错误区域（如右侧编辑器）创建对话
- 保证新对话始终在Augment Code面板中创建

## 使用方法

### 1. GUI配置
1. 运行GUI界面：`python gui_automation.py`
2. 在"配置设置"区域找到以下选项：
   - **当前窗口任务文件**: 设置当前窗口使用的任务文件路径
   - **新窗口任务文件**: 设置新窗口使用的任务文件路径
   - **新窗口阈值**: 设置发送多少次后开启新窗口
   - **Cursor窗口**: 选择要控制的Cursor进程（支持多窗口选择）
3. 勾选"开启新窗口 (Ctrl+L)"选项（默认已勾选）
4. 点击"刷新"按钮更新可用的Cursor窗口列表
5. 从下拉框中选择要控制的Cursor窗口
6. 点击"保存配置"

### 2. 任务文件准备
- 创建或编辑 `tasks.txt` 作为当前窗口任务文件
- 创建或编辑 `tasks_new_window.txt` 作为新窗口任务文件
- 两个文件可以包含不同的任务内容

### 3. 运行自动化
1. 配置完成后点击"运行自动化"
2. 系统会根据发送计数自动选择使用哪个任务文件
3. 达到阈值后会自动创建新窗口

## 工作流程

### 按钮状态判断逻辑

```
暂停按钮 (pause):
├── 发送计数 < 阈值:
│   ├── 在当前窗口发送 tasks.txt
│   └── 发送计数 +1
└── 发送计数 ≥ 阈值:
    ├── 创建新窗口 (Ctrl+L)
    ├── 发送 tasks_new_window.txt
    └── 发送计数重置为 0

正常按钮 (normal) + 开启新窗口:
├── 直接创建新窗口 (Ctrl+L)
└── 发送 tasks_new_window.txt
```

## 配置文件说明

新增的配置项：
```json
{
  "automation": {
    "current_window_tasks_file": "tasks.txt",
    "new_window_tasks_file": "tasks_new_window.txt",
    "new_window_threshold": 3,
    "send_count": 0
  }
}
```

- `current_window_tasks_file`: 当前窗口任务文件路径
- `new_window_tasks_file`: 新窗口任务文件路径
- `new_window_threshold`: 新窗口阈值
- `send_count`: 当前发送计数

## 示例场景

假设设置阈值为3，勾选开启新窗口：

### 暂停按钮场景
1. **第1次检测**: 暂停按钮，计数0 → 发送 `tasks.txt`，计数变为1
2. **第2次检测**: 暂停按钮，计数1 → 发送 `tasks.txt`，计数变为2
3. **第3次检测**: 暂停按钮，计数2 → 发送 `tasks.txt`，计数变为3
4. **第4次检测**: 暂停按钮，计数3 → 创建新窗口，发送 `tasks_new_window.txt`，计数重置为0
5. **第5次检测**: 暂停按钮，计数0 → 发送 `tasks.txt`，计数变为1

### 正常按钮场景
- **任何时候**: 正常按钮 → 直接创建新窗口

## 测试功能

运行测试脚本验证功能：
```bash
python test_new_window_threshold.py
python demo_new_features.py
```

## 注意事项

1. 确保两个任务文件都存在且有内容
2. 新窗口阈值必须大于0
3. 发送计数会持久化保存，重启程序后会继续累计
4. 如需重置计数，可以手动修改配置文件中的 `send_count` 为0
