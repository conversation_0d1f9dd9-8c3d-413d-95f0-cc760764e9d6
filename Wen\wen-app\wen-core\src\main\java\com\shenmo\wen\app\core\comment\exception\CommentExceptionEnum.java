package com.shenmo.wen.app.core.comment.exception;

import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnumOption;
import com.shenmo.wen.common.exception.enumeration.ExceptionType;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 */
@ExceptionType(type = CommentException.class, module = CommentExceptionEnum.MODULE)
public enum CommentExceptionEnum implements ExceptionEnum {

    /**
     * 当前用户未拥有文章的评论等级权限
     */
    COMMENT_LEVEL_PERMISSION(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "还没到能评论的等级~")),
    COMMENT_NOT_EXISTS(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "评论消失了...")),
    ;

    public static final String MODULE = "006";

    /**
     * 异常枚举选项
     */
    private final ExceptionEnumOption exceptionEnumOption;

    CommentExceptionEnum(ExceptionEnumOption exceptionEnumOption) {

        this.exceptionEnumOption = exceptionEnumOption;
    }

    @Override
    public ExceptionEnumOption getOption() {

        return exceptionEnumOption;
    }
}
