<template>
  <div class="background-animation" :class="{ 'dark-theme': isDarkTheme }" :style="backgroundStyle">
    <!-- 静态元素容器 -->
    <div class="static-elements">
      <!-- 浅色主题云朵 -->
      <div v-if="!isDarkTheme && enableClouds" class="clouds-container">
        <div
          v-for="i in Math.ceil(cloudCount)"
          :key="`cloud-${i}`"
          class="cloud"
          :style="getCloudStyle(i)"
        ></div>
      </div>

      <!-- 暗色主题星星 -->
      <div v-if="isDarkTheme && enableStars" class="stars-container">
        <div
          v-for="i in particleCount"
          :key="`star-${i}`"
          class="star"
          :style="getStarStyle(i)"
        ></div>
      </div>
    </div>

    <!-- 动态元素容器 -->
    <div class="dynamic-elements">
      <!-- 浅色主题蒲公英 -->
      <div v-if="!isDarkTheme && enableDandelions" class="dandelions-container">
        <div
          v-for="i in particleCount"
          :key="`dandelion-${i}`"
          class="dandelion-seed"
          :style="getDandelionSeedStyle(i)"
        >
          <!-- 移除多根蒲公英绒毛循环，只保留一个主茎 -->
          <div class="main-stem"></div>
        </div>
      </div>

      <!-- 暗色主题萤火虫 -->
      <div v-if="isDarkTheme && enableFireflies" class="fireflies-container">
        <div
          v-for="i in particleCount"
          :key="`firefly-${i}`"
          class="firefly"
          :style="getFireflyStyle(i)"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue'

// REFACTOR: 使用抽取出的composables
import {
  useBackgroundStyles,
  useCloudStyles,
  useStarStyles,
  useDandelionStyles,
  useFireflyStyles,
} from '@/composables/background'

// 定义可配置的属性，使组件更灵活
const props = defineProps({
  particleCount: {
    type: Number,
    default: 30,
    description: '动态元素的数量（蒲公英/萤火虫）',
  },
  cloudCount: {
    type: Number,
    default: 9,
    description: '云朵数量',
  },
  enableClouds: {
    type: Boolean,
    default: true,
    description: '是否显示云朵',
  },
  enableDandelions: {
    type: Boolean,
    default: true,
    description: '是否显示蒲公英（浅色模式）',
  },
  enableStars: {
    type: Boolean,
    default: true,
    description: '是否显示星星（暗色模式）',
  },
  enableFireflies: {
    type: Boolean,
    default: true,
    description: '是否显示萤火虫（暗色模式）',
  },
  customLightGradient: {
    type: String,
    default: '',
    description: '自定义浅色主题背景渐变',
  },
  customDarkGradient: {
    type: String,
    default: '',
    description: '自定义暗色主题背景渐变',
  },
  zIndex: {
    type: Number,
    default: 0,
    description: '背景层级（z-index）',
  },
})

// 导出自定义事件
const emit = defineEmits(['theme-change'])

// REFACTOR: 使用背景样式composable
const { isDarkTheme, backgroundStyle } = useBackgroundStyles({
  customLightGradient: props.customLightGradient,
  customDarkGradient: props.customDarkGradient,
  zIndex: props.zIndex,
})

// REFACTOR: 使用云朵样式composable
const { getCloudStyle, generateCloudPseudoElementsCSS } = useCloudStyles()

// REFACTOR: 使用星星样式composable
const { getStarStyle } = useStarStyles()

// REFACTOR: 使用蒲公英样式composable
const { getDandelionSeedStyle } = useDandelionStyles()

// REFACTOR: 使用萤火虫样式composable
const { getFireflyStyle } = useFireflyStyles()

// 当主题变化时触发事件
watch(isDarkTheme, (newTheme) => {
  emit('theme-change', newTheme ? 'dark' : 'light')
})

// 当组件挂载完成后，动态生成云朵伪元素的CSS
onMounted(() => {
  // REFACTOR: 使用抽取出的generateCloudPseudoElementsCSS方法
  generateCloudPseudoElementsCSS()
})

// 导出方法和属性以便其他组件可以访问
defineExpose({
  isDarkTheme,
})
</script>

<style lang="scss" scoped>
@use '@/styles/background/background-animation';
</style>
