<template>
  <BubbleMenu :tippy-options="tippyOptions" :editor="editor as Editor" :should-show="shouldShow">
    <div class="editor-bubble-menu">
      <!-- 图片选择菜单 -->
      <template v-if="selectBubbleMenu.image">
        <!-- 图片相关操作 -->
      </template>

      <!-- Bilibili 选择菜单 -->
      <template v-else-if="selectBubbleMenu.bilibili">
        <!-- Bilibili 相关操作 -->
      </template>

      <!-- 默认文本编辑菜单 -->
      <template v-else>
        <!-- 文本格式按钮 -->
        <ToolbarButtonGroup
          :buttons="bubbleMenuButtons.textFormat"
          :editor="editor"
          :extensions-set="extensionsSet"
          :show-modal="showModal"
        />

        <!-- 引用按钮 -->
        <ToolbarButtonGroup
          :buttons="[bubbleMenuButtons.other[0]]"
          :editor="editor"
          :extensions-set="extensionsSet"
          :show-modal="showModal"
        />

        <!-- 链接按钮 -->
        <TiptapBtn
          :icon="LinkOutlined"
          :show="extensionsSet.has('link')"
          :trigger="
            () =>
              showModal(
                '设置链接',
                () => {
                  // 不在这里直接设置链接，而是在TipTapEditor.vue的handlePositiveClick中处理
                  // 这里只需要扩展链接标记范围
                  editor?.chain().focus().extendMarkRange('link').run()
                },
                true,
              )
          "
          :is-active="editor?.isActive('link')"
          tooltip="链接"
        />

        <!-- 颜色选择器 -->
        <ColorPicker
          :show="extensionsSet.has('color')"
          type="bubble-menu"
          :editor="editor"
          colorType="color"
          tooltip="文字颜色"
        />
        <ColorPicker
          :show="extensionsSet.has('backgroundColor')"
          type="bubble-menu"
          :editor="editor"
          colorType="backgroundColor"
          tooltip="背景色"
        />

        <!-- 对齐按钮 -->
        <ToolbarButtonGroup
          :buttons="bubbleMenuButtons.align"
          :editor="editor"
          :extensions-set="extensionsSet"
          :show-modal="showModal"
        />
      </template>
    </div>
  </BubbleMenu>
</template>

<script lang="ts" setup>
import { BubbleMenu, Editor } from '@tiptap/vue-3'

import ColorPicker from '@/components/tiptap/extensions/color/ColorPicker.vue'
import ToolbarButtonGroup from '@/components/tiptap/toolbar/components/ToolbarButtonGroup.vue'
import { allButtonConfigs } from '@/components/tiptap/toolbar/configs/toolbarButtons'
import TiptapBtn from '@/components/tiptap/toolbar/TiptapBtn.vue'
import { useBubbleMenu } from '@/composables/tiptap/useBubbleMenu'
import { LinkOutlined } from '@/icons'
import type { SelectBubbleMenuState } from '@/types/tiptap/select-bubble-menu-state.types'
import type { ShowModalPayload } from '@/types/tiptap/show-modal-payload.types'

// 气泡菜单专用的按钮配置（不包含某些不适合气泡菜单的按钮）
const bubbleMenuButtons = {
  textFormat: allButtonConfigs.textFormat,
  align: allButtonConfigs.align,
  other: [allButtonConfigs.other[0]], // 只包含引用按钮
}

// 组件 Props 接口
interface Props {
  editor: Editor
  extensionsSet: Set<string>
  selectBubbleMenu: SelectBubbleMenuState
}

defineProps<Props>()

const emit = defineEmits<{
  'show-modal': [payload: ShowModalPayload]
}>()

// 使用气泡菜单组合式函数
const { tippyOptions, shouldShow } = useBubbleMenu()

const showModal = (title: string, trigger: () => void, onlyInputValue = false) => {
  emit('show-modal', { title, trigger, onlyInputValue })
}
</script>
