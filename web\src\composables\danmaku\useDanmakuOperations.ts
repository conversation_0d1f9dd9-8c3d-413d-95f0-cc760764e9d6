import type { CustomDanmu } from '@/types/danmaku/custom-danmu.types'
import type { DanmakuEventType } from '@/types/danmaku/danmaku-events.types'
import type { DanmakuFullConfig } from '@/types/danmaku/danmaku-full-config.types'
import type { DanmakuInsertData, DanmakuAddData } from '@/types/danmaku/danmaku-insert-data.types'
import type { DanmakuOperationsComposableOptions } from '@/types/danmaku/danmaku-operations-options.types'
import type { UseDanmakuOperationsReturn } from '@/types/danmaku/danmaku-operations-return.types'
import type { DanmakuProps } from '@/types/danmaku/danmaku-props.types'
import { useDanmakuListManagement } from '@/utils/danmaku/operations/danmakuListManagement'
import { useDanmakuPlayControl } from '@/utils/danmaku/operations/danmakuPlayControl'
import { useDanmakuSlotComponent } from '@/utils/danmaku/operations/danmakuSlotComponent'
import logger from '@/utils/log/log'

import type { Ref } from 'vue'

/**
 * 弹幕操作管理组合式函数
 * 提供弹幕的绘制、插入、播放控制等核心功能
 * @param options 弹幕操作配置选项
 */
export function useDanmakuOperations(
  options: DanmakuOperationsComposableOptions,
): UseDanmakuOperationsReturn {
  const {
    props,
    emit,
    slots,
    danmakuConfig,
    danmuStyle,
    danmuList,
    container,
    dmContainer,
    containerWidth,
    containerHeight,
    calcChannels,
    danmuHeight,
    index,
    hidden,
    paused,
    getChannelIndex,
    danChannel,
    timer: timerRef,
  } = options

  // 计时器
  const timer = timerRef

  // 使用模块化组件
  const { getSlotComponent } = useDanmakuSlotComponent()
  const { add, push } = useDanmakuListManagement({ danmuList, index })

  /**
   * 绘制弹幕
   */
  function draw() {
    if (!paused.value && danmuList.value.length) {
      if (index.value > danmuList.value.length - 1) {
        const screenDanmus = dmContainer.value.children.length

        if (danmakuConfig.loop) {
          if (screenDanmus < index.value) {
            // 一轮弹幕插入完毕
            emit('list-end')
            index.value = 0
          }
          insert()
        }
      } else {
        insert()
      }
    }
  }

  /**
   * 插入弹幕
   */
  function insert(data?: DanmakuInsertData) {
    try {
      // 如果传入的是弹幕数据对象
      if (data && data.content) {
        let content = data.content
        // 如果content是字符串，尝试解析为JSON对象
        if (typeof content === 'string') {
          try {
            content = JSON.parse(content)
          } catch (e) {
            // 解析失败，保持原始字符串
            logger.warn('弹幕JSON解析失败，使用原始文本:', e as Error)
          }
        }

        // 将处理后的内容添加到弹幕列表
        return add({
          id: data.commentId || Date.now().toString(),
          content: content,
        } as DanmakuAddData)
      }

      // 如果没有传入数据或传入的是简单弹幕，使用原始逻辑
      const _index = danmakuConfig.loop ? index.value % danmuList.value.length : index.value
      const _danmu = data || danmuList.value[_index]
      let el = document.createElement(`div`)

      if (danmakuConfig.useSlot) {
        el = getSlotComponent(_danmu as CustomDanmu, _index, slots).$el
      } else {
        el.innerHTML = _danmu as string
        el.setAttribute('style', (props.extraStyle as string) || '')
        el.style.fontSize = `${danmuStyle.fontSize}px`
        el.style.lineHeight = `3rem`
      }
      el.classList.add('dm')
      dmContainer.value.appendChild(el)
      el.style.opacity = '0'

      const offsetHeight = el.offsetHeight
      let offsetWidth = el.offsetWidth

      // 检测是否包含图片，为图片弹幕添加宽度缓冲
      const hasImages = el.querySelector('img') !== null
      if (hasImages) {
        // 为包含图片的弹幕添加额外宽度缓冲，防止图片加载后宽度增加导致提前消失
        offsetWidth += 150 // 添加150px缓冲
      }

      if (!danmuHeight.value) {
        danmuHeight.value = 48 // 强制使用 3rem 高度
      }
      // 如果没有设置轨道数，则在获取到所有高度后计算出最大轨道数
      if (!danmakuConfig.channels) {
        calcChannels.value = Math.floor(
          containerHeight.value / ((danmuStyle.height as number) + (danmuStyle.top as number)),
        )
      }
      let channelIndex = getChannelIndex(el, dmContainer.value)
      if (channelIndex >= 0) {
        const height = danmuStyle.height as number
        const computedChannelIndex = () => {
          const top = channelIndex * (height + (danmuStyle.top as number)) + offsetHeight
          if (top >= containerHeight.value) {
            channelIndex--
            computedChannelIndex()
          }
        }
        computedChannelIndex()
        logger.debug('danmaku height top: ', height, String(danmuStyle.top))
        el.classList.add('move')
        el.dataset.index = `${_index}`
        el.dataset.channel = channelIndex.toString()
        el.style.opacity = '1'
        const top = channelIndex * (height + (danmuStyle.top as number)) + 'px'
        el.style.top = top
        el.style.left = `${containerWidth.value}px`

        // 使用延迟获取更准确的宽度，特别是对于包含图片的弹幕
        const setAnimationDuration = () => {
          const currentWidth = hasImages
            ? Math.max(el.offsetWidth + 100, offsetWidth)
            : el.offsetWidth
          // 保持弹幕在屏幕内的视觉速度一致
          // 弹幕需要移动的总距离 = 容器宽度 + 弹幕宽度
          const totalDistance = containerWidth.value + currentWidth
          const duration = totalDistance / (danmuStyle.speeds as number)
          el.style.animationDuration = `${duration}s`
          el.style.setProperty('--dm-scroll-width', `-${totalDistance}px`)
        }

        if (hasImages) {
          // 对于包含图片的弹幕，延迟设置动画时间以获得更准确的宽度
          requestAnimationFrame(() => {
            requestAnimationFrame(setAnimationDuration)
          })
        } else {
          setAnimationDuration()
        }
        el.addEventListener('animationend', () => {
          if (Number(el.dataset.index) === danmuList.value.length - 1 && !danmakuConfig.loop) {
            emit('play-end', el.dataset.index)
          }
          if (dmContainer.value) {
            dmContainer.value.removeChild(el)
          }
        })
        index.value++

        // 为包含图片的弹幕监听图片加载完成事件，动态调整动画时间
        if (hasImages) {
          const images = el.querySelectorAll('img')
          let loadedCount = 0
          const totalImages = images.length

          const updateAnimationOnImageLoad = () => {
            loadedCount++
            if (loadedCount === totalImages) {
              // 所有图片加载完成，重新计算动画时间
              const finalWidth = el.offsetWidth + 50 // 添加小量缓冲
              const totalDistance = containerWidth.value + finalWidth
              const newDuration = totalDistance / (danmuStyle.speeds as number)
              el.style.animationDuration = `${newDuration}s`
              el.style.setProperty('--dm-scroll-width', `-${totalDistance}px`)
            }
          }

          images.forEach((img) => {
            if (img.complete) {
              updateAnimationOnImageLoad()
            } else {
              img.addEventListener('load', updateAnimationOnImageLoad, { once: true })
              img.addEventListener('error', updateAnimationOnImageLoad, { once: true })
            }
          })
        }
      } else {
        dmContainer.value.removeChild(el)
      }
    } catch (error) {
      logger.error('添加弹幕时发生错误:', error as Error)
    }
  }

  // 使用播放控制模块
  const playControlOptions: {
    props: DanmakuProps
    emit: (event: DanmakuEventType, ...args: any[]) => void
    danmakuConfig: DanmakuFullConfig
    container: Ref<HTMLDivElement>
    dmContainer: Ref<HTMLDivElement>
    containerWidth: Ref<number>
    containerHeight: Ref<number>
    danmuHeight: Ref<number>
    paused: Ref<boolean>
    hidden: Ref<boolean>
    danChannel: any
    timer: Ref<any>
    draw: () => void
  } = {
    props: props as DanmakuProps,
    emit,
    danmakuConfig: danmakuConfig as DanmakuFullConfig,
    container,
    dmContainer,
    containerWidth,
    containerHeight,
    danmuHeight,
    paused,
    hidden,
    danChannel,
    timer,
    draw,
  }
  const { clearTimer, play, pause, stop, show, hide, reset, getPlayState } =
    useDanmakuPlayControl(playControlOptions)

  return {
    draw,
    insert,
    add,
    push,
    clearTimer,
    play,
    pause,
    stop,
    show,
    hide,
    reset,
    getPlayState,
  }
}
