/**
 * 云朵样式生成逻辑
 * 抽取自BackgroundAnimation组件
 */
export function useCloudStyles() {
  // 云朵样式生成
  const getCloudStyle = (index: number) => {
    // 5种不同形状的云朵，提供更多变化
    const cloudType = index % 5

    // 根据类型设置不同的尺寸和形状特性
    let width, height, blur, opacity, pseudoElements

    if (cloudType === 0) {
      // 蓬松积云
      width = Math.floor(Math.random() * 150) + 350 // 350-500px宽
      height = Math.floor(Math.random() * 100) + 200 // 200-300px高
      blur = 15 // 增加模糊值
      opacity = 0.65 // 降低整体不透明度，使其更朦胧
      pseudoElements = [
        { top: '25%', left: '-10%', width: '60%', height: '60%', borderRadius: '70% 60% 65% 75%' },
        { top: '10%', left: '30%', width: '70%', height: '70%', borderRadius: '65% 75% 60% 70%' },
        { top: '35%', left: '75%', width: '45%', height: '45%', borderRadius: '65% 55% 70% 60%' },
        { top: '60%', left: '25%', width: '55%', height: '55%', borderRadius: '70% 65% 75% 60%' },
        { top: '45%', left: '52%', width: '48%', height: '48%', borderRadius: '60% 75% 65% 70%' },
      ]
    } else if (cloudType === 1) {
      // 长层云
      width = Math.floor(Math.random() * 200) + 400 // 400-600px
      height = Math.floor(Math.random() * 80) + 140 // 140-220px
      blur = 18
      opacity = 0.6
      pseudoElements = [
        { top: '30%', left: '5%', width: '50%', height: '55%', borderRadius: '80% 70% 75% 65%' },
        { top: '20%', left: '40%', width: '60%', height: '70%', borderRadius: '75% 80% 65% 70%' },
        { top: '35%', left: '60%', width: '40%', height: '60%', borderRadius: '70% 65% 80% 75%' },
        { top: '25%', left: '80%', width: '35%', height: '65%', borderRadius: '65% 75% 70% 80%' },
      ]
    } else if (cloudType === 2) {
      // 小团层积云
      width = Math.floor(Math.random() * 120) + 280 // 280-400px
      height = Math.floor(Math.random() * 90) + 160 // 160-250px
      blur = 14
      opacity = 0.7
      pseudoElements = [
        { top: '20%', left: '10%', width: '55%', height: '55%', borderRadius: '65% 70% 60% 75%' },
        { top: '15%', left: '45%', width: '65%', height: '65%', borderRadius: '75% 65% 70% 60%' },
        { top: '50%', left: '25%', width: '50%', height: '50%', borderRadius: '60% 75% 65% 70%' },
      ]
    } else if (cloudType === 3) {
      // 绵羊状云朵
      width = Math.floor(Math.random() * 140) + 300 // 300-440px
      height = Math.floor(Math.random() * 120) + 180 // 180-300px
      blur = 16
      opacity = 0.63
      pseudoElements = [
        { top: '10%', left: '5%', width: '40%', height: '40%', borderRadius: '75% 65% 70% 60%' },
        { top: '5%', left: '35%', width: '45%', height: '45%', borderRadius: '70% 60% 75% 65%' },
        { top: '15%', left: '70%', width: '35%', height: '35%', borderRadius: '65% 70% 60% 75%' },
        { top: '50%', left: '10%', width: '38%', height: '38%', borderRadius: '70% 75% 65% 60%' },
        { top: '45%', left: '40%', width: '42%', height: '42%', borderRadius: '65% 60% 75% 70%' },
        { top: '40%', left: '75%', width: '30%', height: '30%', borderRadius: '75% 65% 60% 70%' },
      ]
    } else {
      // 卷云
      width = Math.floor(Math.random() * 180) + 320 // 320-500px
      height = Math.floor(Math.random() * 70) + 130 // 130-200px
      blur = 13
      opacity = 0.66
      pseudoElements = [
        { top: '25%', left: '0%', width: '45%', height: '45%', borderRadius: '65% 70% 75% 60%' },
        { top: '20%', left: '35%', width: '50%', height: '50%', borderRadius: '75% 65% 60% 70%' },
        { top: '30%', left: '65%', width: '40%', height: '40%', borderRadius: '70% 60% 75% 65%' },
      ]
    }

    // 随机位置生成策略 - 根据云朵类型分配不同高度范围

    // 计算高度范围 - 让不同类型的云朵分布在不同高度，增加深度感
    // 较大的云朵放在较低位置，小云朵放在较高位置
    let minTop, maxTop
    if (cloudType === 0) {
      // 蓬松积云 - 中低位置
      minTop = 40
      maxTop = 65
    } else if (cloudType === 1) {
      // 长层云 - 高位置
      minTop = 10
      maxTop = 30
    } else if (cloudType === 2) {
      // 小团层积云 - 中位置
      minTop = 25
      maxTop = 50
    } else if (cloudType === 3) {
      // 绵羊状云朵 - 中低位置
      minTop = 35
      maxTop = 60
    } else {
      // 卷云 - 最高位置
      minTop = 5
      maxTop = 25
    }

    // 计算云朵的最大宽度占屏幕百分比
    const widthPercent = (width / window.innerWidth) * 100

    // 水平位置 - 避免云朵超出屏幕
    const left = Math.random() * (100 - widthPercent)

    // 垂直位置 - 根据上面定义的范围随机生成
    const top = minTop + Math.random() * (maxTop - minTop)

    // 添加少量随机旋转 - 根据云朵类型给不同的旋转角度
    let rotate
    if (cloudType === 0 || cloudType === 3) {
      // 积云和绵羊云可以有更多旋转
      rotate = Math.random() * 8 - 4 // -4到4度
    } else if (cloudType === 1) {
      // 长层云旋转很小
      rotate = Math.random() * 2 - 1 // -1到1度
    } else {
      // 其他类型适中旋转
      rotate = Math.random() * 4 - 2 // -2到2度
    }

    // 注入伪元素数据
    const pseudoElementsJSON = JSON.stringify(pseudoElements)

    // 为不同类型云朵添加不同的z-index，创造层次感
    // 卷云(最高)和长层云(较高)在最上层
    // 蓬松积云和绵羊状云朵在中间层
    // 小团层积云在最下层
    let zIndexValue
    if (cloudType === 4 || cloudType === 1) {
      // 卷云和长层云
      zIndexValue = 6
    } else if (cloudType === 0 || cloudType === 3) {
      // 积云和绵羊云
      zIndexValue = 5
    } else {
      // 小团层积云
      zIndexValue = 4
    }

    return {
      width: `${width}px`,
      height: `${height}px`,
      left: `${left}%`,
      top: `${top}%`,
      opacity: opacity,
      filter: `blur(${blur}px)`, // 直接使用更大的模糊值
      transform: `rotate(${rotate}deg)`,
      zIndex: zIndexValue,
      '--cloud-type': cloudType,
      '--pseudo-elements': pseudoElementsJSON,
      /* 确保没有边框或边界 */
      border: 'none',
      outline: 'none',
      boxShadow: 'none',
      backgroundColor: 'transparent',
    }
  }

  // 生成云朵伪元素的CSS
  const generateCloudPseudoElementsCSS = () => {
    // 创建额外的style标签来处理动态伪元素样式
    const styleEl = document.createElement('style')

    // 添加逻辑，为每个云朵注入动态伪元素样式
    document.querySelectorAll('.cloud').forEach((cloud, index) => {
      const cloudEl = cloud as HTMLElement
      const pseudoElStr = cloudEl.style.getPropertyValue('--pseudo-elements')

      if (pseudoElStr) {
        try {
          const pseudoElements = JSON.parse(pseudoElStr) as Array<{
            top: string
            left: string
            width: string
            height: string
            borderRadius: string
          }>

          // 为每个伪元素创建一个独特的类名
          const className = `cloud-${index}`
          cloudEl.classList.add(className)

          // 生成CSS规则
          let css = ''
          pseudoElements.forEach((el, i) => {
            // 为每个伪元素添加随机的透明度和模糊度
            const opacity = (Math.random() * 0.2 + 0.4).toFixed(2) // 降低透明度
            const blurAmount = Math.floor(Math.random() * 10) + 12 // 12-22px - 增加模糊
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const spread = Math.floor(Math.random() * 15) + 20 // 20-35px - 减小光晕

            if (i === 0) {
              // 使用::before伪元素 - 增强渐变透明过渡
              css += `.${className}::before { 
                width: ${el.width}; 
                height: ${el.height}; 
                top: ${el.top}; 
                left: ${el.left}; 
                border-radius: ${el.borderRadius};
                background: radial-gradient(
                  circle at center,
                  rgba(255, 255, 255, 0.8) 0%,
                  rgba(255, 255, 255, ${opacity}) 40%,
                  rgba(255, 255, 255, 0.15) 75%,
                  rgba(255, 255, 255, 0) 100%
                );
                filter: blur(${blurAmount}px);
                box-shadow: none;
                opacity: ${opacity};
                border: none;
                outline: none;
              }\n`
            } else if (i === 1) {
              // 使用::after伪元素 - 增强渐变透明过渡
              css += `.${className}::after { 
                width: ${el.width}; 
                height: ${el.height}; 
                top: ${el.top}; 
                left: ${el.left}; 
                border-radius: ${el.borderRadius};
                background: radial-gradient(
                  circle at center,
                  rgba(255, 255, 255, 0.75) 0%,
                  rgba(255, 255, 255, ${opacity}) 35%,
                  rgba(255, 255, 255, 0.1) 70%,
                  rgba(255, 255, 255, 0) 100%
                );
                filter: blur(${blurAmount}px);
                box-shadow: none;
                opacity: ${opacity};
                border: none;
                outline: none;
              }\n`
            } else {
              // 为其他伪元素创建新的CSS规则，使用更多随机性
              const randomOpacity = (Math.random() * 0.2 + 0.35).toFixed(2) // 降低透明度
              const randomBlur = Math.floor(Math.random() * 8) + 10 // 10-18px - 增加模糊

              css += `.${className}::before { 
                content: ''; 
                position: absolute;
                width: ${el.width}; 
                height: ${el.height}; 
                top: ${el.top}; 
                left: ${el.left};
                border-radius: ${el.borderRadius};
                background: radial-gradient(
                  circle at center,
                  rgba(255, 255, 255, 0.7) 0%,
                  rgba(255, 255, 255, ${randomOpacity}) 30%,
                  rgba(255, 255, 255, 0.08) 65%,
                  rgba(255, 255, 255, 0) 100%
                );
                filter: blur(${randomBlur}px);
                box-shadow: none;
                opacity: ${randomOpacity};
                z-index: ${i};
                border: none;
                outline: none;
              }\n`
            }
          })

          styleEl.textContent += css
        } catch (e) {
          console.error('解析云朵伪元素数据失败:', e)
        }
      }
    })

    // 将样式添加到文档中
    document.head.appendChild(styleEl)

    return styleEl
  }

  return {
    getCloudStyle,
    generateCloudPseudoElementsCSS,
  }
}
