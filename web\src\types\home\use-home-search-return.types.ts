import type { ArticleListRef } from '@/types/component/article-list-ref.types'
import type { CommentDanmakuRef } from '@/types/component/comment-danmaku-ref.types'
import type { SearchCondition } from '@/types/search/search-condition.types'

import type { SearchHistoryItem } from './search-history-item.types'
import type { Ref, ComputedRef } from 'vue'

/**
 * 首页搜索组合式函数返回值类型
 */
export interface UseHomeSearchReturn {
  /** 是否正在加载 */
  isLoading: Ref<boolean>
  /** 是否正在搜索 */
  isSearching: Ref<boolean>
  /** 搜索条件 */
  searchCondition: Ref<SearchCondition>
  /** 是否有搜索条件 */
  hasSearchCondition: ComputedRef<boolean>
  /** 搜索历史记录 */
  searchHistory: Ref<SearchHistoryItem[]>
  /** 获取搜索占位符 */
  getSearchPlaceholder: (isCardVisible: boolean) => string
  /** 加载搜索条件 */
  loadSearchCondition: () => void
  /** 保存搜索条件 */
  saveSearchCondition: () => void
  /** 执行搜索 */
  search: (
    isCardVisible: boolean,
    articleListRef: ArticleListRef | null,
    commentDanmakuRef: CommentDanmakuRef | null,
    loadMore?: boolean,
  ) => void
  /** 处理标签选择 */
  handleTagSelected: (
    tagName: string,
    isCardVisible: boolean,
    articleListRef: ArticleListRef | null,
    commentDanmakuRef: CommentDanmakuRef | null,
  ) => void
  /** 清理函数 */
  cleanup: () => void
}
