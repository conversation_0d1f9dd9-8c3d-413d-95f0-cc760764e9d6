import { TextHeader120Filled, TextHeader220Filled, TextHeader320Filled } from '@/icons'

import type { ToolbarButtonConfig } from './types'

/**
 * 标题按钮配置
 */
export const headingButtons: ToolbarButtonConfig[] = [
  {
    icon: TextHeader120Filled,
    extensionName: 'heading',
    trigger: (editor) => editor?.chain().focus().toggleHeading({ level: 1 }).run(),
    isActive: (editor) => editor?.isActive('heading', { level: 1 }),
    tooltip: '标题1',
  },
  {
    icon: TextHeader220Filled,
    extensionName: 'heading',
    trigger: (editor) => editor?.chain().focus().toggleHeading({ level: 2 }).run(),
    isActive: (editor) => editor?.isActive('heading', { level: 2 }),
    tooltip: '标题2',
  },
  {
    icon: TextHeader320Filled,
    extensionName: 'heading',
    trigger: (editor) => editor?.chain().focus().toggleHeading({ level: 3 }).run(),
    isActive: (editor) => editor?.isActive('heading', { level: 3 }),
    tooltip: '标题3',
  },
]
