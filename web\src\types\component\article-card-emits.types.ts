/**
 * 文章卡片组件Emits类型定义
 * 定义文章卡片组件的事件发射接口
 */

import type { Article } from '@/types/article/article.types'
import type { TiptapEditorRef } from '@/types/component/tiptap-editor-ref.types'

/**
 * 文章卡片组件的 Emits 类型定义
 */
export interface ArticleCardEmits {
  /** 切换文章可见范围 */
  (event: 'toggleScope', article: Article): void
  /** 开始长按操作 */
  (
    event: 'startLongPress',
    mouseEvent: MouseEvent | TouchEvent,
    article: Article,
    element: HTMLElement,
  ): void
  /** 取消长按操作 */
  (event: 'cancelLongPress'): void
  /** 下载文章 */
  (event: 'download', articleId: string): void
  /** 设置编辑器引用 */
  (event: 'setEditor', articleId: string, editor: TiptapEditorRef): void
}
