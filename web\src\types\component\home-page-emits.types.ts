import type { SearchCondition } from '@/types/search/search-condition.types'

/**
 * 首页组件的 Emits 类型定义
 */
export interface HomePageEmits {
  /** 搜索条件变化 */
  (event: 'search-condition-change', condition: SearchCondition): void
  /** 视图模式切换 */
  (event: 'view-mode-change', isCardVisible: boolean): void
  /** 文章创建成功 */
  (event: 'article-create-success'): void
  /** 特权创建成功 */
  (event: 'privilege-create-success'): void
}
