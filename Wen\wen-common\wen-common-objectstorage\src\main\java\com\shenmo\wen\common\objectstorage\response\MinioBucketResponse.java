package com.shenmo.wen.common.objectstorage.response;

import io.minio.messages.Bucket;
import org.springframework.lang.NonNull;

/**
 * minio桶响应
 *
 * <AUTHOR>
 */
public class MinioBucketResponse extends BucketResponse<Bucket> {

    /**
     * 构造方法
     *
     * @param origin 源对象
     * <AUTHOR>
     */
    public MinioBucketResponse(Bucket origin) {
        super(origin);
    }

    /**
     * 获取桶名称
     *
     * @return 桶名称
     * <AUTHOR>
     */
    @NonNull
    @Override
    public String getName() {

        return origin.name();
    }
}
