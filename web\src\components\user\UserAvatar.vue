<template>
  <div class="avatar-container">
    <NPopover trigger="click" placement="bottom">
      <div class="user-info">
        <div class="info-row">
          <strong>手机号：</strong><span>{{ loginUser.phone }}</span>
        </div>
        <div class="info-row">
          <strong>用户名：</strong><span>{{ loginUser.username }}</span>
        </div>
        <div class="info-row">
          <strong>职业：</strong><span>{{ loginUser.job }}</span>
        </div>
        <div class="actions-row">
          <ThemeToggle />
          <NButton type="error" size="tiny" @click="logout">退出登录</NButton>
        </div>
      </div>
      <template #trigger>
        <LongPress @long-press="handleAvatarFileClick">
          <NAvatar :size="56" :src="loginUser.avatar" object-fit="cover" class="cursor-pointer" />
        </LongPress>
      </template>
    </NPopover>
    <input
      type="file"
      ref="avatarFileInputRef"
      @change="handleAvatarFileChange"
      class="display-none"
    />
  </div>
</template>

<script lang="ts" setup>
import { NPopover, NButton, NAvatar } from 'naive-ui'
import { ref, onMounted } from 'vue'

import authApi from '@/api/auth'
import fileApi from '@/api/file'
import userApi from '@/api/user'
import LongPress from '@/components/interaction/LongPress.vue'
import ThemeToggle from '@/components/theme/ThemeToggle.vue'
import { HOME_CARD, HOME_SEARCH_CONDITION } from '@/constants/home/<USER>'
import router from '@/router'
import {
  createLoginUser,
  type LoginUserCreationParams,
} from '@/types/user/login-user-creation.types'
import { type LoginUser } from '@/types/user/login-user.types'
import localStorage from '@/utils/storage/local-storage'

const loginUser = ref<LoginUser>({} as LoginUser)
const avatarFileInputRef = ref<HTMLInputElement | null>(null)

onMounted(() => {
  // 获取最新的用户信息
  userApi.info().then((res) => {
    if (res.data) {
      // 将UserInfoResponse转换为LoginUser格式
      const userCreationParams: LoginUserCreationParams = {
        id: res.data.id,
        username: res.data.username,
        phone: res.data.phone,
        email: res.data.email,
        avatar: getResourceURL(res.data.avatar || ''),
        ipLocation: res.data.ipLocation,
        job: res.data.job,
        level: res.data.level,
        notificationReceiveType: res.data.notificationReceiveType,
        createdAt: res.data.ctTm,
      }
      const userInfo = createLoginUser(userCreationParams)
      loginUser.value = userInfo
      // 更新本地存储的用户信息
      localStorage.setLoginUser(userInfo)
    }
  })
})

// 头像点击事件，触发上传
const handleAvatarFileClick = () => {
  avatarFileInputRef.value?.click()
}
const getResourceURL = (uri: string): string => {
  return fileApi.getResourceURL(uri)
}
// 处理头像文件上传
const handleAvatarFileChange = async (e: Event): Promise<void> => {
  const target = e.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    const file = target.files[0]
    userApi.changeAvatar(file).then((res) => {
      if (res.data) {
        loginUser.value.avatar = getResourceURL(res.data)
        userApi.info().then((res) => {
          if (res.data) {
            // 将UserInfoResponse转换为LoginUser格式
            const userCreationParams: LoginUserCreationParams = {
              id: res.data.id,
              username: res.data.username,
              phone: res.data.phone,
              email: res.data.email,
              avatar: getResourceURL(res.data.avatar || ''),
              ipLocation: res.data.ipLocation,
              job: res.data.job,
              level: res.data.level,
              notificationReceiveType: res.data.notificationReceiveType,
              createdAt: res.data.ctTm,
            }
            const loginUserData = createLoginUser(userCreationParams)
            localStorage.setLoginUser(loginUserData)
          }
        })
      }
    })
  }
}
// 退出登录的函数
const logout = () => {
  authApi.logout().then(() => {
    router.push('/login')
    localStorage.removeLoginUser()
    // 清除搜索条件
    localStorage.remove(HOME_SEARCH_CONDITION)
    // 清除卡片显示状态，确保下次登录默认显示文章
    localStorage.remove(HOME_CARD)
  })
}
</script>

<style lang="scss" scoped>
@use '@/styles/user/user-avatar';
</style>
