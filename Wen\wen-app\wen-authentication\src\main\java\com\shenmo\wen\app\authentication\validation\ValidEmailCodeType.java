package com.shenmo.wen.app.authentication.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * 邮箱验证码类型验证注解
 * 
 * <AUTHOR>
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = EmailCodeTypeValidator.class)
@Documented
public @interface ValidEmailCodeType {
    
    String message() default "无效的验证码类型，支持的类型：login、register、forgot";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}
