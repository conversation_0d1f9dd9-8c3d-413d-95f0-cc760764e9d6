package com.shenmo.wen.app.core.user.pojo.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 用户特权模板保存请求
 * 
 * <AUTHOR>
 */
@Data
public class WenUserPrivilegeTemplateSaveReq {
    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String name;

    /**
     * 模板图标
     */
    @NotBlank(message = "模板图标不能为空")
    private String icon;

    /**
     * 模板链接
     */
    @NotBlank(message = "模板链接不能为空")
    private String link;

    /**
     * 模板面额
     */
    @NotNull(message = "模板面额不能为空")
    private Integer denomination;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 二维码URL
     */
    private String qrCodeUrl;
}
