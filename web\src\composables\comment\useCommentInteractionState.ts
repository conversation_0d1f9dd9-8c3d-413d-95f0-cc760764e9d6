import { ref, type Ref } from 'vue'

import type { EditorWithFormatPainter } from '@/types/tiptap/editor-with-format-painter.types'

import type { JSONContent } from '@tiptap/vue-3'

/**
 * 评论交互状态管理组合式函数返回值类型
 */
export interface UseCommentInteractionStateReturn {
  /** 快捷回复加载状态 */
  quickReplyLoading: Ref<Map<string, boolean>>
  /** 发送评论加载状态 */
  sendCommentLoading: Ref<boolean>
  /** 评论输入框可见性 */
  commentInputVisible: Ref<string>
  /** 评论回复内容 */
  commentReply: Ref<JSONContent | undefined>
  /** 快捷回复编辑器映射 */
  quickReplyTiptapEditorMap: Ref<Map<string, EditorWithFormatPainter>>
  /** 更新编辑器引用 */
  updateEditor: (commentId: string, editor: EditorWithFormatPainter) => void
}

/**
 * 评论交互状态管理组合式函数
 * 提供评论交互相关的状态管理功能
 */
export function useCommentInteractionState(): UseCommentInteractionStateReturn {
  // 评论交互状态
  const quickReplyLoading = ref<Map<string, boolean>>(new Map())
  const sendCommentLoading = ref(false)
  const commentInputVisible = ref('-1')
  const commentReply = ref<JSONContent | undefined>(undefined)
  const quickReplyTiptapEditorMap = ref<Map<string, EditorWithFormatPainter>>(new Map())

  // 更新编辑器引用
  const updateEditor = (commentId: string, editor: EditorWithFormatPainter) => {
    // 使用类型断言来解决复杂的类型不匹配问题
    ;(quickReplyTiptapEditorMap.value as unknown as Map<string, EditorWithFormatPainter>).set(
      commentId,
      editor as unknown as EditorWithFormatPainter,
    )
  }

  return {
    quickReplyLoading,
    sendCommentLoading,
    commentInputVisible,
    commentReply,
    quickReplyTiptapEditorMap: quickReplyTiptapEditorMap as unknown as Ref<
      Map<string, EditorWithFormatPainter>
    >,
    updateEditor,
  }
}
