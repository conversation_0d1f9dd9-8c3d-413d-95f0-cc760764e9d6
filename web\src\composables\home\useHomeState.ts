import { ref, nextTick, type Ref } from 'vue'

import { HOME_CARD } from '@/constants/home/<USER>'
import type { ArticleListRef } from '@/types/component/article-list-ref.types'
import type { ArticleModalExpose } from '@/types/component/article-modal-expose.types'
import type { CommentDanmakuRef } from '@/types/component/comment-danmaku-ref.types'
import webSocket from '@/utils/network/web-socket'
import localStorage from '@/utils/storage/local-storage'

/**
 * 首页状态组合式函数返回值类型
 */
interface UseHomeStateReturn {
  /** 卡片显示状态 */
  isCardVisible: Ref<boolean>
  /** 在线人数 */
  onlineCount: Ref<number>
  /** 文章弹框引用 */
  articleModalRef: Ref<ArticleModalExpose | null>
  /** 文章列表引用 */
  articleListRef: Ref<ArticleListRef | null>
  /** 评论弹幕引用 */
  commentDanmakuRef: Ref<CommentDanmakuRef | null>
  /** 初始化状态 */
  initializeState: () => void
  /** 切换卡片显示状态 */
  toggleCardVisibility: (
    resetArticleList: () => void,
    search: () => void | Promise<void>,
    handleDanmakuSubscription: (
      commentDanmakuRef: Ref<CommentDanmakuRef | null>,
      subscribe: boolean,
    ) => void,
  ) => void
  /** 重置文章列表 */
  resetArticleList: () => void
  /** 打开创建文章弹框 */
  openCreateArticleDialog: () => void
  /** 创建窗口大小调整回调 */
  createResizeCallback: (
    handleDanmakuResize: (
      commentDanmakuRef: Ref<CommentDanmakuRef | null>,
      isCardVisible: boolean,
    ) => void,
  ) => () => void
  /** 清理函数 */
  cleanup: (resizeCallback: () => void) => void
}

/**
 * 首页状态管理组合式函数
 * 提供首页的状态管理、组件引用和事件处理功能
 */
export function useHomeState(): UseHomeStateReturn {
  // 显示状态控制
  const isCardVisible = ref(true)

  // 在线人数
  const onlineCount = ref<number>(0)

  // 组件引用
  const articleModalRef = ref<ArticleModalExpose | null>(null)
  const articleListRef = ref<ArticleListRef | null>(null)
  const commentDanmakuRef = ref<CommentDanmakuRef | null>(null)

  // 初始化状态
  const initializeState = (): void => {
    // 连接WebSocket
    webSocket.connect()

    // 从localStorage读取展示类型
    const savedCardVisibility = localStorage.get(HOME_CARD)
    if (savedCardVisibility !== null && savedCardVisibility !== undefined) {
      isCardVisible.value = Boolean(savedCardVisibility)
    }
  }

  // 切换卡片显示状态
  const toggleCardVisibility = (
    resetArticleList: () => void,
    search: () => void | Promise<void>,
    handleDanmakuSubscription: (
      commentDanmakuRef: Ref<CommentDanmakuRef | null>,
      subscribe: boolean,
    ) => void,
  ): void => {
    // 保存状态到localStorage
    localStorage.set(HOME_CARD, Boolean(isCardVisible.value))

    if (isCardVisible.value) {
      // 从评论视图切换到文章视图
      handleDanmakuSubscription(commentDanmakuRef, false)

      // 重置文章列表并重新加载数据
      resetArticleList()
      nextTick(async () => {
        // 等待文章列表组件挂载完成
        const isReady = await waitForRef(() => articleListRef.value)
        if (isReady) {
          search()
        }
      })
    } else {
      // 从文章视图切换到评论视图
      handleDanmakuSubscription(commentDanmakuRef, true)
      nextTick(async () => {
        // 等待评论弹幕组件挂载完成
        const isReady = await waitForRef(() => commentDanmakuRef.value)
        if (isReady) {
          search()
        }
      })
    }
  }

  // 等待组件引用准备就绪
  const waitForRef = async (
    refGetter: () => unknown,
    maxWaitTime: number = 1000,
  ): Promise<boolean> => {
    const startTime = Date.now()
    while (!refGetter() && Date.now() - startTime < maxWaitTime) {
      await new Promise((resolve) => setTimeout(resolve, 10))
    }
    return !!refGetter()
  }

  // 重置文章列表
  const resetArticleList = (): void => {
    if (articleListRef.value) {
      articleListRef.value.resetList()
    }
  }

  // 打开创建文章弹框
  const openCreateArticleDialog = (): void => {
    articleModalRef.value?.openCreateArticleDialog()
  }

  // 窗口大小调整回调
  const createResizeCallback = (
    handleDanmakuResize: (
      commentDanmakuRef: Ref<CommentDanmakuRef | null>,
      isCardVisible: boolean,
    ) => void,
  ): (() => void) => {
    return (): void => {
      handleDanmakuResize(commentDanmakuRef, isCardVisible.value)
    }
  }

  // 清理函数
  const cleanup = (resizeCallback: () => void): void => {
    window.removeEventListener('resize', resizeCallback)
  }

  return {
    // 状态
    isCardVisible,
    onlineCount,

    // 组件引用
    articleModalRef,
    articleListRef,
    commentDanmakuRef,

    // 方法
    initializeState,
    toggleCardVisibility,
    resetArticleList,
    openCreateArticleDialog,
    createResizeCallback,
    cleanup,
  }
}
