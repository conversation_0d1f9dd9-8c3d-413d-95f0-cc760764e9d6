/*
 * Home 视图组件样式
 * 首页布局样式定义，包括顶部控制区域、标签栏和内容区域
 */

.home-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  height: 100dvh;
  width: 100vw;
  width: 100dvw;

  .home-layout-top {
    padding: 1.25rem;
    background: linear-gradient(to bottom, var(--creamy-white-3), var(--creamy-white-2));
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 7.5rem;
    flex-wrap: wrap;

    .left-controls-container {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      position: absolute;
      left: 1.25rem;
      top: 7rem;

      .control-item {
        display: flex;
        align-items: center;
        color: var(--black);
        font-size: 0.9rem;

        .control-label {
          margin-right: 0;
        }
      }
    }

    .middle-controls-container {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: auto;
      padding-left: 4.5%;
      max-width: 100%;
    }
  }

  .tag-bar-wrapper {
    min-height: 2.5rem;
    padding-bottom: 0.25rem;
    background-color: var(--creamy-white-2);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .home-layout-content {
    height: calc(100vh - 12.5rem);
    height: calc(100dvh - 12.5rem);
    background-color: var(--creamy-white-1);
    position: relative;
    overflow: hidden;
    width: 100%;
    display: flex;
    flex-direction: column;
    padding-top: 0.25rem;
  }
}

/* 弹幕控制按钮的过渡动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
