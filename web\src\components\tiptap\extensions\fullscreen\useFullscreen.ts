import { ref } from 'vue'

import logger from '@/utils/log/log'

/**
 * 编辑器全屏模式逻辑封装
 * 处理全屏模式的切换、样式及事件处理
 */
export const useFullscreen = (editor: { value?: { commands: { focus: () => void } } }) => {
  // 全屏状态
  const isFullscreen = ref(false)
  // 工具栏全屏状态（用于同步工具栏与编辑器的全屏状态）
  const toolbarFullscreenState = ref<boolean | undefined>(undefined)
  // 事件处理器
  let resizeHandler: (() => void) | null = null
  let visualViewportHandler: ((e: Event) => void) | null = null
  let resizeEventHandler: ((e: UIEvent) => void) | null = null

  /**
   * 处理全屏模式切换
   * @param fullscreen 是否进入全屏模式
   */
  const handleToggleFullscreen = (fullscreen: boolean) => {
    isFullscreen.value = fullscreen
    // 更新工具栏状态
    toolbarFullscreenState.value = fullscreen

    // 添加或移除body的overflow样式以防止滚动
    if (isFullscreen.value) {
      document.body.style.overflow = 'hidden'

      // 检测是否为移动设备
      const isMobile = window.innerWidth <= 768

      // 在移动设备上，调整页面视口设置，避免虚拟键盘引起的问题
      if (isMobile) {
        // 清除之前可能存在的监听器
        if (resizeEventHandler) {
          window.removeEventListener('resize', resizeEventHandler)
        }

        if (visualViewportHandler) {
          const visualViewportObj = window.visualViewport
          if (visualViewportObj) {
            visualViewportObj.removeEventListener('resize', visualViewportHandler)
          }
        }

        // 创建调整函数
        resizeHandler = () => {
          const fullscreenElement = document.querySelector('.tiptap-fullscreen')
          const editorContent = fullscreenElement?.querySelector('.editor-content')

          if (fullscreenElement && editorContent) {
            // 只在全屏模式下处理
            if (isFullscreen.value) {
              // 处理虚拟键盘弹出导致窗口高度变化的情况
              const viewportHeight = window.innerHeight
              // 设置最大高度（留出工具栏和字数统计空间）
              ;(editorContent as HTMLElement).style.maxHeight = `${viewportHeight - 8 * 16}px`
            }
          }
        }

        // 创建window resize事件处理器
        resizeEventHandler = (_e: UIEvent) => {
          if (resizeHandler) resizeHandler()
        }

        // 使用VisualViewport API处理iOS设备上的虚拟键盘
        const visualViewportObj = window.visualViewport
        if (visualViewportObj) {
          visualViewportHandler = (_event: Event) => {
            const fullscreenElement = document.querySelector('.tiptap-fullscreen')
            if (fullscreenElement && isFullscreen.value) {
              // 键盘弹出时，调整编辑器位置，避免被键盘遮挡
              const offsetY = visualViewportObj.offsetTop
              ;(fullscreenElement as HTMLElement).style.transform = offsetY
                ? `translateY(-${offsetY}px)`
                : ''

              // 调整内容区域大小
              const editorContent = fullscreenElement.querySelector('.editor-content')
              if (editorContent) {
                const newHeight = visualViewportObj.height - 8 * 16 // 减去工具栏和计数器高度
                ;(editorContent as HTMLElement).style.maxHeight = `${newHeight}px`
              }
            }
          }

          visualViewportObj.addEventListener('resize', visualViewportHandler)
        }

        // 初始调整
        if (resizeHandler) {
          try {
            resizeHandler()
          } catch (e) {
            // 忽略可能的错误
            logger.error('Error initializing resize handler', e as Error)
          }
        }

        // 添加窗口大小变化监听
        if (resizeEventHandler) {
          window.addEventListener('resize', resizeEventHandler)
        }
      }

      // 确保全屏模式下保持当前主题
      const isDarkTheme = document.documentElement.classList.contains('dark-theme')
      setTimeout(() => {
        const fullscreenElement = document.querySelector('.tiptap-fullscreen')
        if (fullscreenElement) {
          if (isDarkTheme) {
            fullscreenElement.classList.add('dark-theme')
          }

          // 获取编辑器包装器的原始背景色并应用到全屏元素
          const editorWrapper =
            document.querySelector('.tiptap-editor-wrapper:not(.tiptap-fullscreen)') ||
            document.querySelector('.article-modal-content')

          if (editorWrapper) {
            const computedStyle: CSSStyleDeclaration = window.getComputedStyle(editorWrapper)
            // 确保有默认背景色
            const backgroundColor =
              computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)' &&
              computedStyle.backgroundColor !== 'transparent'
                ? computedStyle.backgroundColor
                : isDarkTheme
                  ? 'var(--dark-gray)'
                  : 'white'

            ;(fullscreenElement as HTMLElement).style.backgroundColor = backgroundColor

            // 同时应用到编辑器内容区和工具栏
            const editorContent = fullscreenElement.querySelector('.editor-content')
            const editorContentFullscreen = fullscreenElement.querySelector(
              '.editor-content-fullscreen',
            )

            if (editorContent) {
              ;(editorContent as HTMLElement).style.backgroundColor = backgroundColor
            }
            if (editorContentFullscreen) {
              ;(editorContentFullscreen as HTMLElement).style.backgroundColor = backgroundColor
            }

            const editorToolbar = fullscreenElement.querySelector('.editor-toolbar')
            if (editorToolbar) {
              ;(editorToolbar as HTMLElement).style.backgroundColor = backgroundColor
            }

            // 同时应用到ProseMirror元素
            const proseMirror = fullscreenElement.querySelector('.ProseMirror')
            if (proseMirror) {
              ;(proseMirror as HTMLElement).style.backgroundColor = backgroundColor
            }
          } else {
            // 如果找不到原始编辑器，使用默认颜色
            const defaultBgColor = isDarkTheme ? 'var(--dark-gray)' : 'white'
            ;(fullscreenElement as HTMLElement).style.backgroundColor = defaultBgColor

            const editorContent = fullscreenElement.querySelector('.editor-content')
            const editorContentFullscreen = fullscreenElement.querySelector(
              '.editor-content-fullscreen',
            )

            if (editorContent) {
              ;(editorContent as HTMLElement).style.backgroundColor = defaultBgColor
            }
            if (editorContentFullscreen) {
              ;(editorContentFullscreen as HTMLElement).style.backgroundColor = defaultBgColor
            }

            const editorToolbar = fullscreenElement.querySelector('.editor-toolbar')
            if (editorToolbar) {
              ;(editorToolbar as HTMLElement).style.backgroundColor = defaultBgColor
            }

            const proseMirror = fullscreenElement.querySelector('.ProseMirror')
            if (proseMirror) {
              ;(proseMirror as HTMLElement).style.backgroundColor = defaultBgColor
            }
          }
        }
      }, 0)
    } else {
      document.body.style.overflow = ''
      // 清除事件监听器
      if (resizeEventHandler) {
        window.removeEventListener('resize', resizeEventHandler)
        resizeEventHandler = null
      }

      const visualViewportObj = window.visualViewport
      if (visualViewportHandler && visualViewportObj) {
        visualViewportObj.removeEventListener('resize', visualViewportHandler)
        visualViewportHandler = null
      }

      resizeHandler = null

      // 重置任何可能的transform
      const fullscreenElement = document.querySelector('.tiptap-fullscreen')
      if (fullscreenElement) {
        ;(fullscreenElement as HTMLElement).style.transform = ''
      }
    }

    // 让编辑器聚焦
    requestAnimationFrame(() => {
      editor.value?.commands.focus()
    })
  }

  /**
   * 关闭全屏并同步工具栏状态
   */
  const handleCloseFullscreen = () => {
    handleToggleFullscreen(false)
    // 更新工具栏的全屏状态
    toolbarFullscreenState.value = false
  }

  /**
   * 清理函数，在组件卸载时调用
   */
  const cleanupFullscreen = () => {
    document.body.style.overflow = ''

    // 清除事件监听器
    if (resizeEventHandler) {
      window.removeEventListener('resize', resizeEventHandler)
      resizeEventHandler = null
    }

    const visualViewportObj = window.visualViewport
    if (visualViewportHandler && visualViewportObj) {
      visualViewportObj.removeEventListener('resize', visualViewportHandler)
      visualViewportHandler = null
    }

    resizeHandler = null
  }

  return {
    isFullscreen,
    toolbarFullscreenState,
    handleToggleFullscreen,
    handleCloseFullscreen,
    cleanupFullscreen,
  }
}
