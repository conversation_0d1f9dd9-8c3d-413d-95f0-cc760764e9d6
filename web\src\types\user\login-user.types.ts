import type { UserConfig } from './user-config.types'

/**
 * 登录用户信息类型定义
 * 包含用户的基本信息和配置
 */
export interface LoginUser {
  /** 用户唯一标识符 */
  id: string
  /** 用户名 */
  username: string
  /** 手机号码 */
  phone: string
  /** 邮箱地址 */
  email: string
  /** 用户头像URL */
  avatar: string
  /** IP地理位置 */
  ipLocation: string
  /** 职业信息 */
  job: string
  /** 用户等级 */
  level: number
  /** 通知接收类型 */
  notificationReceiveType: number
  /** 用户配置信息 */
  config: UserConfig
}
