package com.shenmo.wen.app.core.comment.service;

import java.util.List;

import com.shenmo.wen.app.core.comment.pojo.domain.WenCommonLocation;
import com.shenmo.wen.app.core.comment.pojo.req.WenCommentLoadReq;
import com.shenmo.wen.app.core.comment.pojo.req.WenCommentSaveReq;
import com.shenmo.wen.app.core.comment.pojo.req.WenCommentSearchReq;
import com.shenmo.wen.app.core.comment.pojo.resp.WenCommentResp;

/**
 *
 * <AUTHOR>
 */
public interface WenCommentService {
    Long save(WenCommentSaveReq req);

    List<WenCommentResp> load(WenCommentLoadReq req);

    WenCommentResp loadById(Long id);

    List<WenCommentResp> parents(Long id);

    List<WenCommentResp> search(WenCommentSearchReq req);

    WenCommonLocation location(Long id);
}
