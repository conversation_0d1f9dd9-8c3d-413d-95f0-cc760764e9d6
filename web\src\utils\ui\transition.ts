export const transition = {
  beforeEnter: (el: Element) => {
    if (el instanceof HTMLElement) {
      el.style.opacity = '0'
      // 减少位移，提高性能
      el.style.transform = 'translateY(2px)'
    }
  },

  enter: (el: Element, done: () => void) => {
    if (el instanceof HTMLElement) {
      // 触发重排
      void el.offsetHeight

      // 使用更短的过渡时间提高性能
      el.style.transition = 'opacity 0.2s ease, transform 0.2s ease'
      el.style.opacity = '1'
      el.style.transform = 'translateY(0)'

      // 与过渡时间匹配的回调延迟
      setTimeout(done, 200)
    } else {
      done()
    }
  },

  leave: (el: Element, done: () => void) => {
    if (el instanceof HTMLElement) {
      // 使用更短的过渡时间提高性能
      el.style.transition = 'opacity 0.2s ease, transform 0.2s ease'
      el.style.opacity = '0'
      // 减少位移，提高性能
      el.style.transform = 'translateY(-2px)'

      // 与过渡时间匹配的回调延迟
      setTimeout(done, 200)
    } else {
      done()
    }
  },
}

export default transition
