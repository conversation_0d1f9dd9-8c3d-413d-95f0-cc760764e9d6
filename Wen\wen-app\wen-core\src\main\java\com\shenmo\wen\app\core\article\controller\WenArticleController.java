package com.shenmo.wen.app.core.article.controller;

import com.shenmo.wen.app.core.article.pojo.domain.WenHotTag;
import com.shenmo.wen.app.core.article.pojo.req.WenArticleSaveReq;
import com.shenmo.wen.app.core.article.pojo.req.WenArticleSearchReq;
import com.shenmo.wen.app.core.article.pojo.req.WenArticleUpdateReq;
import com.shenmo.wen.app.core.article.pojo.resp.WenArticleResp;
import com.shenmo.wen.app.core.article.service.WenArticleService;
import com.shenmo.wen.common.pojo.response.ResponseData;

import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/articles")
@RequiredArgsConstructor
public class WenArticleController {

    private final WenArticleService service;

    @PostMapping
    public ResponseData<Long> save(@Validated @RequestBody WenArticleSaveReq req) {
        return ResponseData.success(service.save(req));
    }

    @PutMapping("/{id}")
    public ResponseData<Void> edit(@PathVariable("id") Long id, @Validated @RequestBody WenArticleUpdateReq req) {
        service.edit(id, req);
        return ResponseData.success();
    }

    @GetMapping
    public ResponseData<List<WenArticleResp>> search(WenArticleSearchReq req) {
        return ResponseData.success(service.search(req));
    }

    @GetMapping("/{id}")
    public ResponseData<WenArticleResp> getById(@PathVariable("id") Long id) {
        return ResponseData.success(service.detail(id));
    }

    @GetMapping("/{id}/title")
    public ResponseData<String> title(@PathVariable("id") Long id) {
        return ResponseData.success(service.title(id));
    }

    @GetMapping("/{id}/file")
    public void download(@PathVariable("id") Long id) throws IOException {
        service.download(id);
    }

    /**
     * 切换文章发布范围
     *
     * @param id 文章ID
     * @return 操作结果
     */
    @PatchMapping("/{id}/published-scope")
    public ResponseData<Void> togglePublishedScope(@PathVariable("id") Long id) {
        service.togglePublishedScope(id);
        return ResponseData.success();
    }

    /**
     * 获取热门标签
     * 
     * @param limit 返回的标签数量，默认为10
     * @return 热门标签及其使用次数
     */
    @GetMapping("/hot-tags")
    public ResponseData<List<WenHotTag>> hotTags(@RequestParam(value = "limit", defaultValue = "10") int limit) {
        return ResponseData.success(service.hotTags(limit));
    }

    /**
     * 删除文章
     *
     * @param id 文章ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public ResponseData<Void> delete(@PathVariable("id") Long id) {
        service.delete(id);
        return ResponseData.success();
    }
}
