/**
 * 编辑器事件优化器
 * 提供编辑器事件处理的防抖、节流等性能优化功能
 * 专门用于 Tiptap 编辑器的事件处理优化
 *
 * 注意：防抖和节流功能已统一使用 @/utils/frequency-limit.ts
 * 这里保留的函数主要用于向后兼容和特殊的编辑器事件处理需求
 */

import frequencyLimit from '@/utils/performance/frequency-limit'

/**
 * 创建防抖的编辑器事件处理函数
 * 用于优化编辑器中频繁触发的事件，如输入、滚动等
 * @param handler 原始事件处理函数
 * @param delay 防抖延迟（毫秒）
 * @param key 可选的唯一标识，如果不提供则使用随机生成的key
 * @returns 防抖后的事件处理函数
 */
export function createDebouncedEditorHandler<T extends Event>(
  handler: (event: T) => void,
  delay = 100,
  key?: string,
): (event: T) => void {
  const debounceKey = key || `editor-debounce-${Math.random().toString(36).substr(2, 9)}`

  return (event: T) => {
    frequencyLimit.debounce(debounceKey, () => handler(event), delay)
  }
}

/**
 * 创建节流的编辑器事件处理函数
 * 用于限制编辑器事件的触发频率，如图片调整大小、滚动等
 * @param handler 原始事件处理函数
 * @param delay 节流延迟（毫秒）
 * @param key 可选的唯一标识，如果不提供则使用随机生成的key
 * @returns 节流后的事件处理函数
 */
export function createThrottledEditorHandler<T extends Event>(
  handler: (event: T) => void,
  delay = 100,
  key?: string,
): (event: T) => void {
  const throttleKey = key || `editor-throttle-${Math.random().toString(36).substr(2, 9)}`

  return (event: T) => {
    frequencyLimit.throttle(throttleKey, () => handler(event), delay)
  }
}

/**
 * 检查浏览器是否支持被动事件监听器
 * 用于编辑器中的触摸和滚轮事件优化
 */
export function detectPassiveEventSupport(): boolean {
  let supportsPassive = false
  try {
    const opts = Object.defineProperty({}, 'passive', {
      get() {
        supportsPassive = true
        return false
      },
    })
    window.addEventListener('testPassive', () => {}, opts)
    window.removeEventListener('testPassive', () => {}, opts)
  } catch {
    // 忽略错误
  }
  return supportsPassive
}

/**
 * 编辑器事件监听器配置接口
 */
export interface EditorEventListenerConfig {
  passive?: boolean
  once?: boolean
  capture?: boolean
}

/**
 * 获取编辑器推荐的事件监听器配置
 * 根据事件类型和编辑器需求返回最优的事件配置
 * @param eventType 事件类型
 * @param needsPreventDefault 是否需要阻止默认行为
 * @returns 配置对象
 */
export function getEditorEventConfig(
  eventType: string,
  needsPreventDefault = false,
): EditorEventListenerConfig {
  // 编辑器中常见的被动事件类型
  const EDITOR_PASSIVE_EVENTS = new Set([
    'wheel',
    'touchstart',
    'touchmove',
    'touchend',
    'touchcancel',
    'scroll',
  ])

  const isPassiveEvent = EDITOR_PASSIVE_EVENTS.has(eventType)
  const shouldBePassive = isPassiveEvent && !needsPreventDefault

  return {
    passive: shouldBePassive,
    capture: false,
  }
}

// 导出类型定义
export type EventListenerConfig = EditorEventListenerConfig
