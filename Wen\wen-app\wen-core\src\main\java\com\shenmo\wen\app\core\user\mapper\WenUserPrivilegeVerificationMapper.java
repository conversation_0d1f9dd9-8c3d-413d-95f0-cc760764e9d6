package com.shenmo.wen.app.core.user.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilegeVerification;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 用户特权验证流程Mapper接口
 * 
 * <AUTHOR>
 */
public interface WenUserPrivilegeVerificationMapper extends BaseMapper<WenUserPrivilegeVerification> {

    /**
     * 根据ID查询验证流程
     */
    default WenUserPrivilegeVerification byId(Long id) {
        return selectById(id);
    }

    /**
     * 检查用户今日是否已申请过该特权
     */
    default boolean existsTodayApplication(Long userId, Long privilegeId) {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        LocalDateTime endOfDay = LocalDate.now().atTime(LocalTime.MAX);
        
        long startTimestamp = startOfDay.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
        long endTimestamp = endOfDay.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
        
        LambdaQueryWrapper<WenUserPrivilegeVerification> wrapper = Wrappers.<WenUserPrivilegeVerification>lambdaQuery()
                .eq(WenUserPrivilegeVerification::getUserId, userId)
                .eq(WenUserPrivilegeVerification::getPrivilegeId, privilegeId)
                .ge(WenUserPrivilegeVerification::getCtTm, startTimestamp)
                .le(WenUserPrivilegeVerification::getCtTm, endTimestamp);
        
        return selectCount(wrapper) > 0;
    }

    /**
     * 查询过期的验证流程
     */
    default List<WenUserPrivilegeVerification> listExpired() {
        long currentTime = System.currentTimeMillis();
        LambdaQueryWrapper<WenUserPrivilegeVerification> wrapper = Wrappers.<WenUserPrivilegeVerification>lambdaQuery()
                .lt(WenUserPrivilegeVerification::getExpireTime, currentTime)
                .eq(WenUserPrivilegeVerification::getStatus, 0); // 进行中状态
        
        return selectList(wrapper);
    }

    /**
     * 根据用户ID和特权ID查询最新的验证流程
     */
    default WenUserPrivilegeVerification getLatestByUserAndPrivilege(Long userId, Long privilegeId) {
        LambdaQueryWrapper<WenUserPrivilegeVerification> wrapper = Wrappers.<WenUserPrivilegeVerification>lambdaQuery()
                .eq(WenUserPrivilegeVerification::getUserId, userId)
                .eq(WenUserPrivilegeVerification::getPrivilegeId, privilegeId)
                .orderByDesc(WenUserPrivilegeVerification::getCtTm)
                .last("LIMIT 1");
        
        return selectOne(wrapper);
    }
}
