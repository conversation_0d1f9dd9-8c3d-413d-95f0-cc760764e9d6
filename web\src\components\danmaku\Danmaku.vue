<template>
  <div ref="container" class="vue-danmaku">
    <div ref="dmContainer" :class="['danmus', { show: !hidden }, { paused: paused }]"></div>
    <slot />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, reactive, computed, useSlots, type PropType } from 'vue'

import { useDanmakuInsert } from '@/composables/danmaku/useDanmakuInsert'
import { usePlayControl } from '@/composables/danmaku/usePlayControl'
import { useResizeHandler } from '@/composables/danmaku/useResizeHandler'
import { useSuspendEvents } from '@/composables/danmaku/useSuspendEvents'
import type { DanChannel } from '@/types/danmaku/dan-channel.types'
import type { DanmakuItem } from '@/types/danmaku/danmaku-item.types'
import type { DanmakuProps } from '@/types/danmaku/danmaku-props.types'
import type { DanmuItem } from '@/types/danmaku/danmu-item.types'
import type { Danmu } from '@/types/danmaku/danmu.types'

// 定义props
const props = defineProps({
  /**
   * 弹幕列表数据
   */
  danmus: {
    type: Array as PropType<Danmu[]>,
    required: true,
    default: () => [],
  },
  /**
   * 轨道数量，0为最大轨道数量（撑满容器）
   */
  channels: {
    type: Number,
    default: 0,
  },
  /**
   * 是否自动播放
   */
  autoplay: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否循环播放
   */
  loop: {
    type: Boolean,
    default: false,
  },
  /**
   * 是否开启弹幕插槽，默认否
   */
  useSlot: {
    type: Boolean,
    default: false,
  },
  /**
   * 弹幕刷新频率(ms)
   */
  debounce: {
    type: Number,
    default: 100,
  },
  /**
   * 弹幕速度（像素/秒）
   */
  speeds: {
    type: Number,
    default: 200,
  },
  /**
   * 是否开启随机轨道注入弹幕
   */
  randomChannel: {
    type: Boolean,
    default: false,
  },
  /**
   * 弹幕字号（仅文本模式）
   */
  fontSize: {
    type: Number,
    default: 18,
  },
  /**
   * 弹幕垂直间距
   */
  top: {
    type: Number,
    default: 4,
  },
  /**
   * 弹幕水平间距
   */
  right: {
    type: Number,
    default: 0,
  },
  /**
   * 是否开启悬浮暂停
   */
  isSuspend: {
    type: Boolean,
    default: false,
  },
  /**
   * 弹幕额外样式
   */
  extraStyle: {
    type: String,
    default: '',
  },
})

// 定义emit
const emit = defineEmits(['list-end', 'play-end', 'dm-over', 'dm-out', 'update:danmus'])

// 容器
const container = ref<HTMLDivElement>(document.createElement('div'))
const dmContainer = ref<HTMLDivElement>(document.createElement('div'))
const containerWidth = ref(0)
const containerHeight = ref(0)
// 变量
const timer = ref(0)
const calcChannels = ref(0)
const danmuHeight = ref(48)
const index = ref<number>(0)
const hidden = ref(false)
const paused = ref(false)
const danChannel = reactive<DanChannel>({})

function useModelWrapper<T>(
  props: DanmakuProps,
  emit: (event: string, ...args: unknown[]) => void,
  name = 'modelValue',
  translater?: (value: unknown) => unknown,
) {
  return computed<T>({
    get: () => props[name] as T,
    set: (value: T) => {
      emit(`update:${name}`, translater ? translater(value) : value)
    },
  })
}

const danmuList = useModelWrapper<Danmu[]>(
  props as DanmakuProps,
  emit as (event: string, ...args: unknown[]) => void,
  'danmus',
)

const danmaku: DanmakuItem = reactive({
  channels: computed(() => props.channels || calcChannels.value),
  autoplay: computed(() => props.autoplay),
  loop: computed(() => props.loop),
  useSlot: computed(() => props.useSlot),
  debounce: computed(() => props.debounce),
  randomChannel: computed(() => props.randomChannel),
})

const danmu: DanmuItem = reactive({
  height: computed(() => danmuHeight.value),
  fontSize: computed(() => props.fontSize),
  speeds: computed(() => {
    const baseSpeed = props.speeds
    const screenWidth = containerWidth.value
    if (screenWidth < 320) {
      return baseSpeed * 0.25 // 窄屏，速度减少 4 倍
    } else if (screenWidth < 960) {
      return baseSpeed * 0.5 // 中等屏幕，速度减少 2 倍
    } else if (screenWidth >= 1920) {
      return baseSpeed // 宽屏，速度保持不变
    } else {
      // 介于 960px 和 1920px 之间，线性插值
      const scaleFactor = 0.5 + ((screenWidth - 960) / (1920 - 960)) * 0.5
      return baseSpeed * scaleFactor
    }
  }),
  top: computed(() => props.top),
  right: computed(() => props.right),
})

// 使用抽取的组合式函数
const { initCore, resize } = useResizeHandler(
  container,
  dmContainer,
  containerWidth,
  containerHeight,
  danmu,
)

const { draw, insert, add, push } = useDanmakuInsert(
  danmuList,
  index,
  containerWidth,
  containerHeight,
  dmContainer,
  danChannel,
  danmu,
  danmaku,
  calcChannels,
  danmuHeight,
  emit as (event: string, ...args: unknown[]) => void,
  props.extraStyle,
  useSlots().dm,
)

const { play, clear, stop, pause, show, hide, getPlayState } = usePlayControl(
  dmContainer,
  danmuList,
  index,
  paused,
  hidden,
  danChannel,
  timer,
  draw,
  danmaku.debounce,
)

const { initSuspendEvents } = useSuspendEvents(
  dmContainer,
  emit as (event: string, ...args: unknown[]) => void,
)

function init() {
  initCore()
  if (props.isSuspend) {
    initSuspendEvents()
  }
  if (danmaku.autoplay) {
    play()
  }
}

function reset() {
  danmuHeight.value = 0
  init()
}

onMounted(() => {
  init()
})

onBeforeUnmount(() => {
  clear()
})

// 导出属性和方法
defineExpose({
  // element
  container,
  dmContainer,

  // variable
  hidden,
  paused,
  danmuList,

  // function
  getPlayState,
  resize,
  play,
  pause,
  stop,
  show,
  hide,
  reset,
  add,
  push,
  insert,
})
</script>

<style lang="scss">
@use '@/styles/danmaku-renderer';
</style>
