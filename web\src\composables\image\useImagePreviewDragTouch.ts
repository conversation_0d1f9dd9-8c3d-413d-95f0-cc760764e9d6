import type { ImageClickCallback } from '@/types/image/image-click-callback.types'

import { getCenter, getDistance } from './useImagePreviewDragHelpers'

import type { UseImagePreviewDragStateReturn } from './useImagePreviewDragState'
import type { UseImagePreviewDragTransformReturn } from './useImagePreviewDragTransform'

/**
 * 图片预览拖拽触摸事件管理组合式函数返回值类型
 */
export interface UseImagePreviewDragTouchReturn {
  /** 图片触摸开始处理 */
  handleTouchStart: (e: TouchEvent) => void
  /** 模态框触摸开始处理 */
  handleModalTouchStart: (e: TouchEvent) => void
  /** 触摸移动处理 */
  handleTouchMove: (e: TouchEvent) => void
  /** 触摸结束处理 */
  handleTouchEnd: (e: TouchEvent) => void
  /** 添加触摸事件监听器 */
  addTouchEventListeners: (modalElement?: HTMLElement) => void
  /** 移除触摸事件监听器 */
  removeTouchEventListeners: (modalElement?: HTMLElement) => void
}

/**
 * 图片预览拖拽触摸事件管理组合式函数
 * 提供触摸拖拽和缩放功能
 */
export function useImagePreviewDragTouch(
  imageElement: HTMLImageElement,
  stateReturn: UseImagePreviewDragStateReturn,
  transformReturn: UseImagePreviewDragTransformReturn,
  onImageClick?: ImageClickCallback,
): UseImagePreviewDragTouchReturn {
  const { state, dragVars, scaleVars, options } = stateReturn
  const { updateTransform } = transformReturn
  const { minScale, maxScale } = options

  /**
   * 图片触摸开始处理（主要用于拖拽）
   */
  const handleTouchStart = (e: TouchEvent): void => {
    if (e.touches.length === 1) {
      // 单指拖拽
      e.preventDefault()
      state.isDragging = true
      dragVars.hasDragged = false // 重置拖拽标记
      const touch = e.touches[0]
      dragVars.dragStartX = touch.clientX
      dragVars.dragStartY = touch.clientY
      dragVars.startTranslateX = state.translateX
      dragVars.startTranslateY = state.translateY
    } else if (e.touches.length === 2) {
      // 双指缩放开始
      e.preventDefault()
      state.isDragging = false // 停止拖拽
      state.isZooming = true
      scaleVars.isScaling = true

      // 记录初始状态
      scaleVars.scaleStartDistance = getDistance(e.touches[0], e.touches[1])
      scaleVars.scaleStartScale = state.scale

      // 计算缩放中心点（两指中心）
      const center = getCenter(e.touches[0], e.touches[1])
      const rect = imageElement.getBoundingClientRect()

      // 缩放中心在图片坐标系中的位置
      const centerX = center.x - rect.left
      const centerY = center.y - rect.top
      scaleVars.scaleCenterX = (centerX - rect.width / 2 - state.translateX) / state.scale
      scaleVars.scaleCenterY = (centerY - rect.height / 2 - state.translateY) / state.scale

      imageElement.classList.remove('dragging')
      imageElement.classList.add('zooming')
    }
  }

  /**
   * 模态框触摸开始处理（主要用于缩放）
   */
  const handleModalTouchStart = (e: TouchEvent): void => {
    if (e.touches.length === 2) {
      // 双指缩放开始
      e.preventDefault()
      state.isDragging = false // 停止拖拽
      state.isZooming = true
      scaleVars.isScaling = true

      // 记录初始状态
      scaleVars.scaleStartDistance = getDistance(e.touches[0], e.touches[1])
      scaleVars.scaleStartScale = state.scale

      // 计算缩放中心点（两指中心）
      const center = getCenter(e.touches[0], e.touches[1])
      const rect = imageElement.getBoundingClientRect()

      // 缩放中心在图片坐标系中的位置
      const centerX = center.x - rect.left
      const centerY = center.y - rect.top
      scaleVars.scaleCenterX = (centerX - rect.width / 2 - state.translateX) / state.scale
      scaleVars.scaleCenterY = (centerY - rect.height / 2 - state.translateY) / state.scale

      imageElement.classList.remove('dragging')
      imageElement.classList.add('zooming')
    }
    // 单指触摸在模态框上不做处理，让图片的触摸事件处理
  }

  /**
   * 触摸移动处理
   */
  const handleTouchMove = (e: TouchEvent): void => {
    if (e.touches.length === 1 && state.isDragging && !state.isZooming) {
      // 单指拖拽
      e.preventDefault()
      const touch = e.touches[0]
      const deltaX = touch.clientX - dragVars.dragStartX
      const deltaY = touch.clientY - dragVars.dragStartY

      // 检查是否超过拖拽阈值
      if (
        !dragVars.hasDragged &&
        (Math.abs(deltaX) > dragVars.dragThreshold || Math.abs(deltaY) > dragVars.dragThreshold)
      ) {
        dragVars.hasDragged = true
        imageElement.classList.add('dragging')
      }

      if (dragVars.hasDragged) {
        state.translateX = dragVars.startTranslateX + deltaX
        state.translateY = dragVars.startTranslateY + deltaY
        updateTransform()
      }
    } else if (e.touches.length === 2 && state.isZooming && scaleVars.isScaling) {
      // 双指缩放进行中
      e.preventDefault()

      const currentDistance = getDistance(e.touches[0], e.touches[1])

      // 计算缩放比例（基于初始距离）
      const scaleRatio = currentDistance / scaleVars.scaleStartDistance
      let newScale = scaleVars.scaleStartScale * scaleRatio

      // 限制缩放范围
      newScale = Math.max(minScale, Math.min(maxScale, newScale))

      if (Math.abs(newScale - state.scale) > 0.001) {
        // 获取当前缩放中心在屏幕上的位置
        const rect = imageElement.getBoundingClientRect()
        const screenCenterX =
          rect.left + rect.width / 2 + scaleVars.scaleCenterX * state.scale + state.translateX
        const screenCenterY =
          rect.top + rect.height / 2 + scaleVars.scaleCenterY * state.scale + state.translateY

        // 更新缩放
        state.scale = newScale

        // 重新计算位置，保持缩放中心不变
        state.translateX =
          screenCenterX - rect.left - rect.width / 2 - scaleVars.scaleCenterX * state.scale
        state.translateY =
          screenCenterY - rect.top - rect.height / 2 - scaleVars.scaleCenterY * state.scale

        updateTransform()
      }
    }
  }

  /**
   * 触摸结束处理
   */
  const handleTouchEnd = (e: TouchEvent): void => {
    if (e.touches.length === 0) {
      const wasClick = state.isDragging && !dragVars.hasDragged // 如果是拖拽状态但没有真正拖拽，则认为是点击

      state.isDragging = false
      state.isZooming = false
      scaleVars.isScaling = false
      imageElement.classList.remove('dragging', 'zooming')

      // 如果是点击（没有拖拽），触发点击回调
      if (wasClick && onImageClick) {
        onImageClick()
      }
    } else if (e.touches.length === 1) {
      // 从双指变为单指，停止缩放
      state.isZooming = false
      scaleVars.isScaling = false
      imageElement.classList.remove('zooming')
    }
  }

  /**
   * 添加触摸事件监听器
   */
  const addTouchEventListeners = (modalElement?: HTMLElement) => {
    // 图片上的触摸事件（用于拖拽）
    imageElement.addEventListener('touchstart', handleTouchStart, { passive: false })

    // 模态框上的触摸事件（用于缩放，如果提供了模态框元素）
    if (modalElement) {
      modalElement.addEventListener('touchstart', handleModalTouchStart, { passive: false })
      modalElement.addEventListener('touchmove', handleTouchMove, { passive: false })
      modalElement.addEventListener('touchend', handleTouchEnd, { passive: true })
      modalElement.addEventListener('touchcancel', handleTouchEnd, { passive: true })
    } else {
      // 如果没有模态框元素，则在图片上监听所有触摸事件
      imageElement.addEventListener('touchmove', handleTouchMove, { passive: false })
      imageElement.addEventListener('touchend', handleTouchEnd, { passive: true })
      imageElement.addEventListener('touchcancel', handleTouchEnd, { passive: true })
    }
  }

  /**
   * 移除触摸事件监听器
   */
  const removeTouchEventListeners = (modalElement?: HTMLElement) => {
    // 图片触摸事件
    imageElement.removeEventListener('touchstart', handleTouchStart)

    // 模态框触摸事件
    if (modalElement) {
      modalElement.removeEventListener('touchstart', handleModalTouchStart)
      modalElement.removeEventListener('touchmove', handleTouchMove)
      modalElement.removeEventListener('touchend', handleTouchEnd)
      modalElement.removeEventListener('touchcancel', handleTouchEnd)
    } else {
      // 如果没有模态框元素，则从图片上移除触摸事件
      imageElement.removeEventListener('touchmove', handleTouchMove)
      imageElement.removeEventListener('touchend', handleTouchEnd)
      imageElement.removeEventListener('touchcancel', handleTouchEnd)
    }
  }

  return {
    handleTouchStart,
    handleModalTouchStart,
    handleTouchMove,
    handleTouchEnd,
    addTouchEventListeners,
    removeTouchEventListeners,
  }
}
