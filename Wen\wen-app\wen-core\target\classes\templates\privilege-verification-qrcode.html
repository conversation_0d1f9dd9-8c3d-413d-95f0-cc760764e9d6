<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>确认验证特权 - {subjectPrefix}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            line-height: 1.6;
            color: #1a202c;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        .logo {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }
        .title {
            font-size: 18px;
            margin: 0;
            font-weight: 400;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        .content {
            padding: 50px 40px;
            line-height: 1.7;
            color: #4a5568;
        }
        .greeting {
            font-size: 20px;
            margin-bottom: 24px;
            color: #2d3748;
            font-weight: 500;
        }
        .highlight {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
            color: #92400e;
            font-weight: 500;
        }
        .info-box {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #bae6fd;
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
            color: #0c4a6e;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 16px 32px;
            text-decoration: none;
            border-radius: 12px;
            margin: 24px 0;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);
        }
        .footer {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 32px 40px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
            font-size: 14px;
            color: #64748b;
        }
        .icon {
            font-size: 18px;
            margin-right: 8px;
        }
        @media (max-width: 640px) {
            body {
                padding: 10px;
            }
            .content {
                padding: 30px 20px;
            }
            .header {
                padding: 30px 20px;
            }
            .footer {
                padding: 24px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{subjectPrefix}</div>
            <h2 class="title">确认验证特权</h2>
        </div>

        <div class="content">
            <div class="greeting">您好！👋</div>

            <div class="highlight">
                <span class="icon">🔐</span>
                <strong>{emailContent}</strong>
            </div>

            <div class="info-box">
                <p><span class="icon">📋</span><strong>特权类型：</strong>{templateName}</p>
                <p><span class="icon">📱</span><strong>验证方式：</strong>二维码扫描验证</p>
            </div>

            <div class="info-box">
                <p><span class="icon">📋</span><strong>验证说明：</strong></p>
                <p>该用户正在申请获得特定权限，需要通过二维码扫描完成身份验证。请仔细核实申请人身份后再进行操作。</p>
            </div>

            <p>如果您确认此申请，请点击下方按钮查看二维码：</p>

            <div style="text-align: center;">
                <a href="{pageUrl}" class="button">📱 查看二维码</a>
            </div>

            <div class="info-box">
                <p><span class="icon">⚠️</span><strong>重要提醒：</strong></p>
                <ul style="margin: 8px 0 0 20px; padding: 0;">
                    <li>请务必确认申请人身份后再进行验证</li>
                    <li>验证链接具有时效性，请及时处理</li>
                    <li>扫描二维码前请确保环境安全</li>
                    <li>如对此申请有疑问，请联系系统管理员</li>
                    <li>未经授权的验证可能带来安全风险</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p><strong>{subjectPrefix} 安全团队</strong></p>
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>如有疑问，请联系系统管理员</p>
        </div>
    </div>
</body>
</html>
