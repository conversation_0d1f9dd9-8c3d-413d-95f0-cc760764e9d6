package com.shenmo.wen.common.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文章发布范围枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ArticlePublishedScopeEnum {

    PUBLIC(0, "公开"),
    PERSONAL(1, "个人")
    ;

    private final int code;
    private final String text;

    public static ArticlePublishedScopeEnum of(int code) {
        final ArticlePublishedScopeEnum[] values = values();
        for (ArticlePublishedScopeEnum e : values) {
            if (e.code == code) {
                return e;
            }
        }
        throw new EnumConstantNotPresentException(ArticlePublishedScopeEnum.class, String.valueOf(code));
    }
}