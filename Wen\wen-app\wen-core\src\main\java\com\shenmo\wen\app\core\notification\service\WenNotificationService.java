package com.shenmo.wen.app.core.notification.service;

import com.shenmo.wen.app.core.notification.pojo.req.WenNotificationLoadReq;
import com.shenmo.wen.app.core.notification.pojo.resp.WenNotificationResp;
import com.shenmo.wen.common.pojo.response.PageResult;

/**
 *
 * <AUTHOR>
 */
public interface WenNotificationService {
    PageResult<WenNotificationResp> load(WenNotificationLoadReq req);

    void read(Long id);

    void unread(Long id);

    /**
     * 标记所有通知为已读
     * 
     * @param userId 用户ID
     */
    void readAll();

    Long totalUnread();
}
