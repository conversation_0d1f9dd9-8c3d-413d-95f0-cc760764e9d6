import type { Article } from '@/types/article/article.types'

import { useArticleDragLongPress } from './useArticleDragLongPress'
import { useArticleDragMove } from './useArticleDragMove'
import { useArticleDragState } from './useArticleDragState'

interface DragOptions {
  onDragStart?: (article: Article) => void
  onDragEnd?: () => void
  onDelete?: (article: Article) => void
  onReorder?: (draggedId: string, targetId: string, position: 'before' | 'after') => void
}

export function useArticleDrag(options: DragOptions = {}) {
  // 初始化状态管理
  const {
    isDragging,
    draggedArticle,
    showTrashBin,
    isOverTrashBin,
    dragOverCardId,
    dragOverPosition,
    isSingleCardRow,
    dragPosition,
    dragStyle,
    isEnding,
    clonedElement,
    eventHandlers,
    forceReset,
    resetDragState,
  } = useArticleDragState()

  // 初始化长按检测
  const { isLongPressActive, startLongPress, cancelLongPress } = useArticleDragLongPress()

  // 初始化拖拽移动
  const { startDragging } = useArticleDragMove(
    isDragging,
    draggedArticle,
    showTrashBin,
    isOverTrashBin,
    dragOverCardId,
    dragOverPosition,
    isSingleCardRow,
    dragPosition,
    isEnding,
    clonedElement,
    eventHandlers,
    resetDragState,
    options,
  )

  // 包装长按开始函数
  const handleStartLongPress = (
    event: MouseEvent | TouchEvent,
    article: Article,
    avatarElement: HTMLElement,
  ) => {
    startLongPress(event, article, avatarElement, startDragging)
  }

  return {
    isDragging,
    draggedArticle,
    showTrashBin,
    isOverTrashBin,
    dragPosition,
    dragStyle,
    dragOverCardId,
    dragOverPosition,
    isSingleCardRow,
    isLongPressActive,
    startLongPress: handleStartLongPress,
    cancelLongPress,
    forceReset,
  }
}
