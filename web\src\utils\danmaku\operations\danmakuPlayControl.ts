import { useDanmakuEffects } from '@/composables/danmaku/useDanmakuEffects'
import type { DanmakuChannelMap } from '@/types/danmaku/danmaku-channel.types'
import type { DanmakuEventType } from '@/types/danmaku/danmaku-events.types'
import type { DanmakuFullConfig } from '@/types/danmaku/danmaku-full-config.types'
import type { DanmakuProps } from '@/types/danmaku/danmaku-props.types'

import type { Ref } from 'vue'

/**
 * 弹幕播放控制选项接口
 */
interface PlayControlOptions {
  /** 弹幕组件属性 */
  props: DanmakuProps
  /** 事件发射器 */
  emit: (event: DanmakuEventType, ...args: unknown[]) => void
  /** 弹幕配置 */
  danmakuConfig: DanmakuFullConfig
  /** 容器元素引用 */
  container: Ref<HTMLDivElement>
  /** 弹幕容器元素引用 */
  dmContainer: Ref<HTMLDivElement>
  /** 容器宽度 */
  containerWidth: Ref<number>
  /** 容器高度 */
  containerHeight: Ref<number>
  /** 弹幕高度 */
  danmuHeight: Ref<number>
  /** 是否暂停 */
  paused: Ref<boolean>
  /** 是否隐藏 */
  hidden: Ref<boolean>
  /** 弹幕通道映射 */
  danChannel: DanmakuChannelMap
  /** 定时器ID */
  timer: Ref<number>
  /** 绘制函数 */
  draw: () => void
}

/**
 * 弹幕播放控制操作
 */
export function useDanmakuPlayControl(options: PlayControlOptions) {
  const {
    props,
    emit,
    danmakuConfig,
    container,
    dmContainer,
    containerWidth,
    containerHeight,
    danmuHeight,
    paused,
    hidden,
    danChannel,
    timer,
    draw,
  } = options

  /**
   * 清除定时器
   */
  function clearTimer() {
    clearInterval(timer.value)
    timer.value = 0
  }

  /**
   * 开始播放弹幕
   */
  function play() {
    paused.value = false
    if (!timer.value) {
      timer.value = window.setInterval(() => draw(), danmakuConfig.debounce)
    }
  }

  /**
   * 暂停弹幕
   */
  function pause(): void {
    paused.value = true
  }

  /**
   * 停止弹幕
   */
  function stop() {
    Object.assign(danChannel, {})
    dmContainer.value.innerHTML = ''
    paused.value = true
    hidden.value = false
    clearTimer()
  }

  /**
   * 显示弹幕
   */
  function show(): void {
    hidden.value = false
  }

  /**
   * 隐藏弹幕
   */
  function hide(): void {
    hidden.value = true
  }

  /**
   * 重置弹幕
   */
  function reset() {
    danmuHeight.value = 0
    containerWidth.value = container.value.offsetWidth
    containerHeight.value = container.value.offsetHeight
    if (containerWidth.value === 0 || containerHeight.value === 0) {
      throw new Error('获取不到容器宽高')
    }

    if (props.isSuspend) {
      const { initSuspendEvents } = useDanmakuEffects(dmContainer, emit)
      initSuspendEvents()
    }

    if (danmakuConfig.autoplay) {
      play()
    }
  }

  /**
   * 获取播放状态
   */
  function getPlayState(): boolean {
    return !paused.value
  }

  return {
    clearTimer,
    play,
    pause,
    stop,
    show,
    hide,
    reset,
    getPlayState,
  }
}
