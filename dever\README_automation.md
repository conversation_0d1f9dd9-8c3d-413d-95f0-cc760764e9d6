# Cursor Augment Code 自动化脚本

这个脚本能够自动检测Cursor编辑器中的Augment Code窗口，根据发送按钮的状态执行相应的自动化操作。

## 功能特性

- 🔍 自动检测Cursor窗口中的Augment Code面板
- 🎯 精确识别发送按钮的状态（正常/暂停）
- 🚀 自动创建新对话（Ctrl+L）
- 📋 自动复制并发送tasks.txt中的内容
- 📝 详细的日志记录和错误处理

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用前准备

### 1. 创建按钮状态模板

在使用脚本之前，需要手动截取Augment Code面板中发送按钮的两种状态图片：

1. **正常状态按钮**: 截取发送按钮处于可发送状态的图片
   - 保存为: `templates/send_button_normal.png`

2. **暂停状态按钮**: 截取发送按钮处于暂停/停止状态的图片
   - 保存为: `templates/send_button_pause.png`

#### 截图步骤：
1. 打开Cursor编辑器，确保Augment Code面板可见
2. 使用截图工具（如Windows的截图工具）
3. 精确截取发送按钮区域（建议包含一些周围背景以提高识别准确性）
4. 将图片保存到对应路径

### 2. 准备任务文件

确保项目根目录下存在 `tasks.txt` 文件，包含需要发送的任务内容。

## 使用方法

### 方法一：图形界面（推荐）

```bash
# 运行GUI界面
python gui_automation.py
# 或者双击运行
run_gui.bat
```

GUI界面提供以下功能：
- 📁 可视化配置文件路径选择
- 🎛️ 直观的参数调整界面
- 🧪 一键按钮检测测试
- 📊 实时运行日志显示
- ⚙️ 配置保存和加载

### 方法二：命令行

```bash
python cursor_augment_automation.py
```

## 工作流程

1. **窗口检测**: 自动查找并激活Cursor窗口
2. **面板识别**: 截取Augment Code面板区域
3. **状态判断**: 使用模板匹配识别发送按钮状态
4. **条件执行**: 
   - 如果按钮不是暂停状态 → 执行自动化操作
   - 如果按钮是暂停状态 → 跳过操作
5. **自动化操作**:
   - 执行 Ctrl+L 创建新对话
   - 读取 tasks.txt 内容
   - 复制并发送内容

## 配置选项

可以在脚本中修改以下配置：

```python
class CursorAugmentAutomation:
    def __init__(self):
        # 任务文件路径
        self.tasks_file_path = "tasks.txt"
        
        # 按钮模板路径
        self.send_button_normal_template = "templates/send_button_normal.png"
        self.send_button_pause_template = "templates/send_button_pause.png"
```

## 注意事项

### 安全设置
- 脚本启用了pyautogui的FAILSAFE功能
- 将鼠标移动到屏幕左上角可紧急停止脚本

### 环境要求
- Windows 10/11 (推荐)
- Python 3.7+
- Cursor编辑器已安装并运行
- Augment Code插件已启用

### 使用建议
1. **首次使用**: 建议在测试环境中先运行，确保模板匹配准确
2. **模板更新**: 如果Cursor界面更新，可能需要重新截取按钮模板
3. **分辨率**: 确保截取模板时的屏幕分辨率与运行时一致
4. **窗口状态**: 确保Cursor窗口完全可见，不被其他窗口遮挡

## 故障排除

### 常见问题

1. **找不到Cursor窗口**
   - 确保Cursor正在运行
   - 检查窗口标题是否包含"Cursor"

2. **无法识别按钮状态**
   - 重新截取按钮模板图片
   - 确保模板图片清晰且包含足够的特征
   - 调整匹配阈值（threshold参数）

3. **自动化操作失败**
   - 检查Cursor窗口是否处于活动状态
   - 确保Augment Code面板可见
   - 验证快捷键是否正确

### 调试模式

可以修改日志级别来获取更详细的调试信息：

```python
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
```

## 扩展功能

脚本支持以下扩展：

1. **多窗口支持**: 可修改窗口检测逻辑支持多个Cursor实例
2. **自定义快捷键**: 可配置不同的快捷键组合
3. **批量任务**: 支持处理多个任务文件
4. **定时执行**: 结合系统任务计划程序实现定时自动化

## 许可证

MIT License - 可自由使用和修改
