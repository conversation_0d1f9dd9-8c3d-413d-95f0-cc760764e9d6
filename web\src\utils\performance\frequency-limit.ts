type DebounceMap = {
  [key: string]: ReturnType<typeof setTimeout>
}
type ThrottleMap = {
  [key: string]: number
}
const debounceMap: DebounceMap = {}
const throttleMap: ThrottleMap = {}
const frequencyLimit = {
  /**
   * 防抖函数 - 延迟执行函数，在指定时间内如果再次调用会重新计时
   *
   * 原理：在事件被触发 delay 毫秒后再执行回调，如果在这 delay 毫秒内事件又被触发，则重新计时
   *
   * 适用场景：
   * 1. 搜索框输入，用户输入完毕后才发送请求
   * 2. 窗口大小调整后重新布局
   * 3. 表单验证，用户输入完成后再进行验证
   * 4. 按钮提交事件，防止用户多次点击
   *
   * @param key - 唯一标识，用于区分不同的防抖操作
   * @param func - 需要执行的回调函数
   * @param delay - 延迟时间，单位毫秒，默认300ms
   */
  debounce: (key: string, func: () => void, delay: number = 300) => {
    if (debounceMap[key]) {
      clearTimeout(debounceMap[key])
    }
    debounceMap[key] = setTimeout(() => {
      func()
    }, delay)
  },

  /**
   * 节流函数 - 限制函数在一定时间内只执行一次
   *
   * 原理：规定一个时间周期，在这个周期内，只执行一次函数，若这个周期内多次触发，仅第一次生效
   *
   * 适用场景：
   * 1. 滚动事件处理，如懒加载、无限滚动
   * 2. 频繁点击按钮，如游戏中的攻击按钮
   * 3. 拖拽事件，mousemove 事件处理
   * 4. 高频监听事件，如 resize、scroll 等
   * 5. 实时数据处理，如图表数据更新
   *
   * @param key - 唯一标识，用于区分不同的节流操作
   * @param func - 需要执行的回调函数
   * @param delay - 时间周期，单位毫秒，默认1000ms
   */
  throttle: (key: string, func: () => void, delay: number = 1000) => {
    const now = Date.now()
    if (!throttleMap[key] || now - throttleMap[key] >= delay) {
      func()
      throttleMap[key] = now
    }
  },
}
export default frequencyLimit
