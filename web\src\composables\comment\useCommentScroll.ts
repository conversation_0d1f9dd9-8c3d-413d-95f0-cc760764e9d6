import { ref, nextTick, type Ref } from 'vue'
import { useRout<PERSON>, useRouter } from 'vue-router'

import commentApi from '@/api/comment'
import { useCommentStore } from '@/stores'
import type { CommentLocationResponse } from '@/types/comment/comment-location-response.types'
import type { Comment } from '@/types/comment/comment.types'

import type { UseCommentBreadcrumbReturn } from './useCommentBreadcrumb'

/**
 * 评论滚动和定位组合式函数返回值类型
 */
export interface UseCommentScrollReturn {
  /** 评论元素引用映射 */
  commentRefs: Ref<Map<string, HTMLElement>>
  /** 评论滚动触发器 */
  commentScrollTrigger: Ref<string>
  /** 闪烁评论ID */
  flashCommentId: Ref<string>
  /** 更新评论引用 */
  updateCommentRef: (commentId: string, el: HTMLElement) => void
  /** 定位评论 */
  locationComment: (commentId?: string) => void
  /** 滚动到指定评论 */
  scrollToComment: (commentId: string) => void
  /** 准备滚动到评论 */
  prepareScrollToComment: (comments: Comment[]) => void
  /** 重置滚动触发器 */
  resetScrollTrigger: () => void
  /** 获取评论ID */
  getCommentId: () => string
}

/**
 * 评论滚动和定位组合式函数
 * 提供评论的滚动定位、闪烁效果等功能
 */
export function useCommentScroll(
  commentLoading: Ref<boolean>,
  breadcrumbActions: UseCommentBreadcrumbReturn,
  resetCommentList: () => void,
  addCommentList: (list: Comment[]) => void,
  setFirstFixedComment: (comment: Comment) => Promise<void>,
): UseCommentScrollReturn {
  const commentRefs = ref<Map<string, HTMLElement>>(new Map())
  const commentScrollTrigger = ref<string>('0')
  const flashCommentId = ref<string>('')
  const commentStore = useCommentStore()
  const route = useRoute()
  const router = useRouter()

  /**
   * 重置滚动触发器
   */
  const resetScrollTrigger = () => {
    commentScrollTrigger.value = String(Date.now())
  }

  /**
   * 更新评论引用
   */
  const updateCommentRef = (commentId: string, el: HTMLElement) => {
    commentRefs.value.set(commentId, el)
  }

  /**
   * 获取评论ID
   */
  const getCommentId = (): string => {
    return commentStore.getId
  }

  /**
   * 闪烁评论
   */
  const flashComment = (commentId: string) => {
    flashCommentId.value = commentId
    setTimeout(() => {
      flashCommentId.value = ''
    }, 1000)
  }

  /**
   * 准备滚动到评论
   */
  const prepareScrollToComment = (comments: Comment[]) => {
    resetCommentList()
    addCommentList(comments)
  }

  /**
   * 滚动到指定评论
   */
  const scrollToComment = (commentId: string) => {
    nextTick(() => {
      const commentRef = commentRefs.value.get(commentId)
      if (commentRef) {
        commentRef.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        })
        setTimeout(() => {
          commentRef.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          })
          flashComment(commentId)
        }, 10)
      }
    })
  }

  /**
   * 定位评论
   */
  const locationComment = (commentId: string = getCommentId()) => {
    commentLoading.value = true
    if (commentId) {
      // 更新commentStore中的评论ID
      commentStore.setId(commentId)
      // 更新路由参数
      router.push({
        params: {
          ...route.params,
          commentId: commentId,
        },
      })
      commentApi
        .location(commentId)
        .then((res) => {
          const commentLocation = res?.data as CommentLocationResponse
          if (commentLocation) {
            const parents = commentLocation.parents
            const comments = commentLocation.comments
            prepareScrollToComment(comments as any)
            breadcrumbActions.resetBreadcrumb()
            const parentsLength = parents.length
            if (parentsLength > 0) {
              for (let i = parentsLength - 1; i >= 0; i--) {
                const comment = parents[i]
                breadcrumbActions.addBreadcrumb(comment as any)
              }
              const parentComment = parents[0]
              setFirstFixedComment(parentComment as any)
            }
            scrollToComment(commentId)
          }
        })
        .catch((error) => {
          // 处理评论加载错误
          if (error.response && error.response.status === 403) {
            // 权限错误，仅重置评论列表
            resetCommentList()
          } else {
            console.error('定位评论失败:', error)
          }
        })
        .finally(() => {
          commentLoading.value = false
        })
    }
  }

  return {
    commentRefs,
    commentScrollTrigger,
    flashCommentId,
    updateCommentRef,
    locationComment,
    scrollToComment,
    prepareScrollToComment,
    resetScrollTrigger,
    getCommentId,
  }
}
