{"extends": ["stylelint-config-standard-scss", "stylelint-config-recommended-vue"], "rules": {"selector-class-pattern": null, "selector-id-pattern": null, "no-descending-specificity": null, "selector-pseudo-element-no-unknown": [true, {"ignorePseudoElements": ["v-deep"]}], "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["deep"]}], "color-function-notation": "legacy", "alpha-value-notation": null, "at-rule-no-unknown": [true, {"ignoreAtRules": ["use", "forward", "include", "mixin", "function", "return", "if", "else", "each", "for", "while", "extend", "error", "warn", "debug"]}], "keyframes-name-pattern": "^([a-z][a-z0-9]*)(-[a-z0-9]+)*$"}}