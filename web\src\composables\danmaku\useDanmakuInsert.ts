import { h, createApp, type Ref, type Slot } from 'vue'

import type { DanmakuChannelMap } from '@/types/danmaku/danmaku-channel.types'
import type { DanmakuItem } from '@/types/danmaku/danmaku-item.types'
import type { DanmuItem } from '@/types/danmaku/danmu-item.types'
import type { Danmu } from '@/types/danmaku/danmu.types'
import logger from '@/utils/log/log'

/**
 * 弹幕插入组合式函数返回值类型
 */
interface UseDanmakuInsertReturn {
  /** 绘制弹幕 */
  draw: () => void
  /** 插入弹幕 */
  insert: (data?: Record<string, unknown>) => void
  /** 添加弹幕到当前位置 */
  add: (danmu: Danmu) => number
  /** 添加弹幕到末尾 */
  push: (danmu: Danmu) => number
  /** 获取轨道索引 */
  getChannelIndex: (el: HTMLDivElement) => number
  /** 获取弹幕右侧距离 */
  getDanRight: (el: HTMLDivElement) => number
  /** 获取插槽组件 */
  getSlotComponent: (danmu: unknown, index: number) => { $el: HTMLElement }
}

/**
 * 弹幕插入管理组合式函数
 * 提供弹幕的绘制、插入、轨道分配等核心功能
 * @param danmuList 弹幕列表响应式引用
 * @param index 当前索引响应式引用
 * @param containerWidth 容器宽度响应式引用
 * @param containerHeight 容器高度响应式引用
 * @param dmContainer 弹幕容器元素响应式引用
 * @param danChannel 弹幕轨道映射
 * @param danmu 弹幕项配置
 * @param danmaku 弹幕配置
 * @param calcChannels 计算的轨道数响应式引用
 * @param danmuHeight 弹幕高度响应式引用
 * @param emit 事件发射器
 * @param extraStyle 额外样式字符串
 * @param dmSlot 弹幕插槽（可选）
 */
export function useDanmakuInsert(
  danmuList: Ref<Danmu[]>,
  index: Ref<number>,
  containerWidth: Ref<number>,
  containerHeight: Ref<number>,
  dmContainer: Ref<HTMLDivElement>,
  danChannel: DanmakuChannelMap,
  danmu: DanmuItem,
  danmaku: DanmakuItem,
  calcChannels: Ref<number>,
  danmuHeight: Ref<number>,
  emit: (event: string, ...args: unknown[]) => void,
  extraStyle: string,
  dmSlot?: Slot,
): UseDanmakuInsertReturn {
  /**
   * 绘制弹幕到屏幕上
   * 根据当前索引和弹幕列表状态决定是否插入新弹幕
   */
  function draw(): void {
    if (danmuList.value.length) {
      if (index.value > danmuList.value.length - 1) {
        const screenDanmus = dmContainer.value.children.length

        if (danmaku.loop) {
          if (screenDanmus < index.value) {
            // 一轮弹幕插入完毕
            emit('list-end')
            index.value = 0
          }
          insert()
        }
      } else {
        insert()
      }
    }
  }

  /**
   * 插入弹幕到容器中
   * 支持外部传入弹幕数据，也可以使用内部弹幕列表数据
   * @param data 外部定义的弹幕数据（可选）
   */
  function insert(data?: Record<string, unknown>): void {
    try {
      // 如果传入的是弹幕数据对象
      if (data && data.content) {
        let content = data.content
        // 如果content是字符串，尝试解析为JSON对象
        if (typeof content === 'string') {
          try {
            content = JSON.parse(content)
          } catch (e) {
            // 解析失败，保持原始字符串
            logger.warn('弹幕JSON解析失败，使用原始文本:', e as Error)
          }
        }

        // 将处理后的内容添加到弹幕列表
        add({
          id: (data.commentId as string) || Date.now().toString(),
          content: content,
        })
      }

      // 如果没有传入数据或传入的是简单弹幕，使用原始逻辑
      const _index = danmaku.loop ? index.value % danmuList.value.length : index.value
      const _danmu = data || danmuList.value[_index]
      let el = document.createElement(`div`)

      if (danmaku.useSlot && dmSlot) {
        el = getSlotComponent(_danmu, _index).$el
      } else {
        el.innerHTML = _danmu as string
        el.setAttribute('style', extraStyle)
        el.style.fontSize = `${danmu.fontSize}px`
        el.style.lineHeight = `3rem`
      }
      el.classList.add('dm')
      dmContainer.value.appendChild(el)
      el.style.opacity = '0'

      const offsetHeight = el.offsetHeight
      let offsetWidth = el.offsetWidth

      // 检测是否包含图片，为图片弹幕添加宽度缓冲
      const hasImages = el.querySelector('img') !== null
      if (hasImages) {
        // 为包含图片的弹幕添加额外宽度缓冲，防止图片加载后宽度增加导致提前消失
        offsetWidth += 150 // 添加150px缓冲
      }

      if (!danmuHeight.value) {
        danmuHeight.value = 48 // 强制使用 3rem 高度
      }
      // 如果没有设置轨道数，则在获取到所有高度后计算出最大轨道数
      if (!danmaku.channels) {
        calcChannels.value = Math.floor(containerHeight.value / (danmu.height + danmu.top))
      }
      let channelIndex = getChannelIndex(el)
      if (channelIndex >= 0) {
        const height = danmu.height
        const computedChannelIndex = () => {
          const top = channelIndex * (height + danmu.top) + offsetHeight
          if (top >= containerHeight.value) {
            channelIndex--
            computedChannelIndex()
          }
        }
        computedChannelIndex()
        logger.debug('danmaku height top: ', height, danmu.top)
        el.classList.add('move')
        el.dataset.index = `${_index}`
        el.dataset.channel = channelIndex.toString()
        el.style.opacity = '1'
        const top = channelIndex * (height + danmu.top) + 'px'
        el.style.top = top
        el.style.left = `${containerWidth.value}px`

        // 使用延迟获取更准确的宽度，特别是对于包含图片的弹幕
        const setAnimationDuration = () => {
          const currentWidth = hasImages
            ? Math.max(el.offsetWidth + 100, offsetWidth)
            : el.offsetWidth
          // 保持弹幕在屏幕内的视觉速度一致
          const totalDistance = containerWidth.value + currentWidth
          const duration = totalDistance / danmu.speeds
          el.style.animationDuration = `${duration}s`
          el.style.setProperty('--dm-scroll-width', `-${totalDistance}px`)
        }

        if (hasImages) {
          // 对于包含图片的弹幕，延迟设置动画时间以获得更准确的宽度
          requestAnimationFrame(() => {
            requestAnimationFrame(setAnimationDuration)
          })
        } else {
          setAnimationDuration()
        }
        el.addEventListener('animationend', () => {
          if (Number(el.dataset.index) === danmuList.value.length - 1 && !danmaku.loop) {
            emit('play-end', el.dataset.index)
          }
          if (dmContainer.value) {
            dmContainer.value.removeChild(el)
          }
        })
        index.value++

        // 为包含图片的弹幕监听图片加载完成事件，动态调整动画时间
        if (hasImages) {
          const images = el.querySelectorAll('img')
          let loadedCount = 0
          const totalImages = images.length

          const updateAnimationOnImageLoad = () => {
            loadedCount++
            if (loadedCount === totalImages) {
              // 所有图片加载完成，重新计算动画时间
              const finalWidth = el.offsetWidth + 50 // 添加小量缓冲
              const totalDistance = containerWidth.value + finalWidth
              const newDuration = totalDistance / danmu.speeds
              el.style.animationDuration = `${newDuration}s`
              el.style.setProperty('--dm-scroll-width', `-${totalDistance}px`)
            }
          }

          images.forEach((img) => {
            if (img.complete) {
              updateAnimationOnImageLoad()
            } else {
              img.addEventListener('load', updateAnimationOnImageLoad, { once: true })
              img.addEventListener('error', updateAnimationOnImageLoad, { once: true })
            }
          })
        }
      } else {
        dmContainer.value.removeChild(el)
      }
    } catch (error) {
      logger.error('添加弹幕时发生错误:', error as Error)
    }
  }

  function getSlotComponent(_danmu: unknown, _index: number) {
    const DmComponent = createApp({
      render() {
        return h('div', {}, [
          dmSlot &&
            dmSlot({
              danmu: _danmu,
              index: _index,
            }),
        ])
      },
    })

    const ele = DmComponent.mount(document.createElement('div'))
    return ele
  }

  function getChannelIndex(el: HTMLDivElement): number {
    let _channels = [...Array(danmaku.channels).keys()]

    if (danmaku.randomChannel) {
      _channels = _channels.sort(() => 0.5 - Math.random())
    }
    for (const i of _channels) {
      const items = danChannel[i] as HTMLElement[]

      if (items && items.length) {
        for (let j = 0; j < items.length; j++) {
          const danRight = getDanRight(items[j] as HTMLDivElement) - 10
          // 安全距离判断
          if (danRight <= (el.offsetWidth - items[j].offsetWidth) * 0.75 || danRight <= 0) {
            break
          }
          if (j === items.length - 1) {
            ;(danChannel[i] as HTMLElement[]).push(el)
            el.addEventListener('animationend', () => (danChannel[i] as HTMLElement[]).splice(0, 1))
            return i % danmaku.channels
          }
        }
      } else {
        danChannel[i] = [el] as HTMLElement[]
        el.addEventListener('animationend', () => (danChannel[i] as HTMLElement[]).splice(0, 1))
        return i % danmaku.channels
      }
    }
    return -1
  }

  /**
   * 获取弹幕右侧到屏幕右侧的距离
   */
  function getDanRight(el: HTMLDivElement) {
    const eleWidth = el.offsetWidth || parseInt(el.style.width)
    const eleRight =
      el.getBoundingClientRect().right || dmContainer.value.getBoundingClientRect().right + eleWidth
    return dmContainer.value.getBoundingClientRect().right - eleRight
  }

  /**
   * 添加弹幕（插入到当前播放的弹幕位置）
   */
  function add(danmu: Danmu): number {
    if (index.value === danmuList.value.length) {
      // 如果当前弹幕已经播放完了，那么仍然走 push
      danmuList.value.push(danmu)
      return danmuList.value.length - 1
    } else {
      const _index = index.value % danmuList.value.length
      danmuList.value.splice(_index, 0, danmu)
      return _index + 1
    }
  }

  /**
   * 添加弹幕（插入到弹幕末尾）
   */
  function push(danmu: Danmu): number {
    danmuList.value.push(danmu)
    return danmuList.value.length - 1
  }

  return {
    draw,
    insert,
    add,
    push,
    getChannelIndex,
    getDanRight,
    getSlotComponent,
  }
}
