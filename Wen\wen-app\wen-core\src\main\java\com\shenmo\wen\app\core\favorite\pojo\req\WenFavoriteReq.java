package com.shenmo.wen.app.core.favorite.pojo.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 收藏请求
 * <AUTHOR>
 */
@Data
public class WenFavoriteReq {

    /**
     * 目标类型，用整数来表示被收藏对象的类型，0代表评论，1代表文章，可为空（按照业务规则来确定其取值情况）。
     */
    @NotNull(message = "收藏目标不可为空")
    private Integer targetType;

    /**
     * 目标ID，依据目标类型的不同，对应文章的ID或者评论的ID，用于精准关联到具体的收藏目标，可为空（结合业务逻辑判断其合法性）。
     */
    @NotNull(message = "收藏目标ID不可为空")
    private Long targetId;
}
