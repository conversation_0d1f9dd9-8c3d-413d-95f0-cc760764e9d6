package com.shenmo.wen.common.messagesynchronizer.redis;

import com.shenmo.wen.common.messagesynchronizer.DefaultMessageData;
import com.shenmo.wen.common.messagesynchronizer.MessageData;
import lombok.Getter;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Deque;
import java.util.Optional;
import java.util.concurrent.ConcurrentLinkedDeque;

/**
 * 抽象的redis消息监听器
 *
 * <AUTHOR>
 */
@Getter
public abstract class AbstractRedisMessageListener<T extends MessageData<?>> implements MessageListener {

    /**
     * 消息通知key双向队列
     */
    private static final Deque<String> MESSAGE_ID_DEQUE = new ConcurrentLinkedDeque<>();

    /**
     * 监听通道
     */
    protected final String channel;

    /**
     * redis操作模板
     */
    protected final RedisTemplate<String, T> redisTemplate;

    /**
     * 构造方法
     *
     * @param redisTemplate redis操作模板
     * @param channel       监听通道
     * <AUTHOR>
     */
    public AbstractRedisMessageListener(@NonNull RedisTemplate<String, T> redisTemplate,
            String channel) {

        this.redisTemplate = redisTemplate;
        this.channel = channel;
    }

    /**
     * 获取消息通知key队列
     *
     * @return 消息通知key队列
     * <AUTHOR>
     */
    public static Deque<String> getMessageIdDeque() {

        return MESSAGE_ID_DEQUE;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void onMessage(@NonNull Message message, @Nullable byte[] pattern) {

        final RedisTemplate<String, T> redisTemplate = getRedisTemplate();
        final RedisSerializer<MessageData<Object>> valueSerializer = (RedisSerializer<MessageData<Object>>) redisTemplate
                .getValueSerializer();
        final MessageData<Object> messageData = Optional.ofNullable(valueSerializer.deserialize(message.getBody()))
                .orElse(DefaultMessageData.empty());
        if (messageData.isEmpty()) {
            return;
        }
        // 本地队列过滤
        final String messageDataId = messageData.getId();
        if (MESSAGE_ID_DEQUE.contains(messageDataId)) {

            MESSAGE_ID_DEQUE.remove(messageDataId);
        } else {

            MESSAGE_ID_DEQUE.addLast(messageDataId);
            onMessage((T) messageData);
        }
    }

    /**
     * 处理redis订阅的消息数据
     *
     * @param messageData 订阅的消息数据
     * <AUTHOR>
     */
    protected void onMessage(@NonNull T messageData) {

        MESSAGE_ID_DEQUE.remove(messageData.getId());
    }
}
