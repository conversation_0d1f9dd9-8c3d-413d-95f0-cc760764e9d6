/**
 * 对话框选项类型定义
 */

/**
 * 对话框基础选项
 */
export interface DialogOptions {
  /** 对话框标题 */
  title?: string
  /** 对话框内容 */
  content?: string
  /** 确认按钮文本 */
  positiveText?: string
  /** 取消按钮文本 */
  negativeText?: string
  /** 是否自动聚焦 */
  autoFocus?: boolean
  /** 是否可以通过ESC键关闭 */
  closeOnEsc?: boolean
  /** 是否可以通过点击遮罩关闭 */
  maskClosable?: boolean
  /** 确认回调 */
  onPositiveClick?: () => void | Promise<void>
  /** 取消回调 */
  onNegativeClick?: () => void | Promise<void>
  /** 关闭回调 */
  onClose?: () => void
}
