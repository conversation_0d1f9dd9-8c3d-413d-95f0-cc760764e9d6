/*
 * NotificationBtnModal 组件样式
 * 通知按钮模态框的样式定义，包括暗色模式适配
 */

.notification-container {
  text-align: end;
}

/* 适配暗色模式的通知样式 */
:deep(.dark-notification-content) {
  color: var(--black);
}

:deep(.dark-notification-avatar) {
  border: 1px solid var(--gray-3);
}

:deep(.dark-notification-button) {
  color: var(--blue);
}

/*
 * NotificationButton 组件样式
 * 通知按钮组件的样式定义，包括基础布局
 */

.notification-btn {
  position: relative;
  display: inline-block;
}

/*
 * NotificationList 组件样式
 * 通知列表组件的样式定义，包括表格分页、弹窗内容和工具类
 */

.notification-table :deep(.n-pagination) {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-wrap: nowrap;
}

.notification-table :deep(.n-pagination-prefix) {
  white-space: nowrap;
  margin-right: 0.5rem;
}

.notification-pagination {
  display: flex;
  flex-wrap: wrap;
  margin-top: 1rem;
  justify-content: center;
  row-gap: 0.5rem;
}

.notification-popover-content {
  max-width: 500px;
}

.cursor-pointer {
  cursor: pointer;
}

.flex-column-end {
  display: flex;
  align-items: center;
}

/*
 * NotificationReceiveTypeSelector 组件样式
 * 通知接收类型选择器组件的样式定义
 */

.notification-popselect {
  margin-left: 8px;
}
