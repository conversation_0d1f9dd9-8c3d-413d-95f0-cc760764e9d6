package com.shenmo.wen.common.messagesynchronizer;

import com.shenmo.wen.common.messagesynchronizer.redis.MessageSynchronizerAdvice;
import com.shenmo.wen.common.util.spring.SpringRedisUtils;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * 对象存储 配置
 *
 * <AUTHOR>
 */
@ConditionalOnProperty(name = "spring.cloud.message-publish-listener", havingValue = "true", matchIfMissing = true)
@AutoConfiguration
public class WenSynchronizerAutoConfiguration {


    @Bean
    @ConditionalOnMissingBean
    public MessageSynchronizerListener messageSynchronizerListener(RedisTemplate<String, MessageData<MessageSynchronizerMethod>> messageSynchronizerTemplate) {

        return new MessageSynchronizerListener(messageSynchronizerTemplate);
    }

    @Bean
    @ConditionalOnMissingBean
    public MessageSynchronizerAdvice messageSynchronizerAdvice(RedisConnectionFactory factory,
                                                               MessageSynchronizerListener messageSynchronizerListener) {

        return new MessageSynchronizerAdvice(factory, SpringRedisUtils.createRedisTemplate(factory), messageSynchronizerListener);
    }
}
