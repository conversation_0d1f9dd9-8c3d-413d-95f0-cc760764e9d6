import { ref, type Ref } from 'vue'

import commentApi from '@/api/comment'
import { ARTICLE_COMMENTS } from '@/constants/article/frequency-key.constants'
import type { ResponseData } from '@/types/api/response-data.types'
import type { Comment } from '@/types/comment/comment.types'
import DateTime from '@/utils/date/date-time'
import frequencyLimit from '@/utils/performance/frequency-limit'
import tiptap from '@/utils/tiptap/tiptap'

import type { UseCommentBreadcrumbReturn } from './useCommentBreadcrumb'

/**
 * 评论加载组合式函数返回值类型
 */
export interface UseCommentLoaderReturn {
  /** 评论列表数据 */
  commentList: Ref<Comment[]>
  /** 评论加载状态 */
  commentLoading: Ref<boolean>
  /** 是否没有更多评论 */
  commentNoMore: Ref<boolean>
  /** 是否有评论权限 */
  hasCommentPermission: Ref<boolean>
  /** 排序类型 */
  sortType: Ref<string>
  /** 重置评论列表 */
  resetCommentList: () => void
  /** 加载当前评论列表 */
  loadCurrentCommentList: (reset?: boolean) => void
  /** 加载评论列表 */
  loadCommentList: (comment: Comment) => void
  /** 添加评论到列表 */
  addCommentList: (list: Comment[]) => void
  /** 设置首位固定评论 */
  setFirstFixedComment: (comment: Comment, newest?: boolean) => Promise<void>
  /** 初始化评论权限 */
  initCommentPermission: () => void
}

/**
 * 评论加载组合式函数
 * 提供评论数据的加载、状态管理等功能
 */
export function useCommentLoader(
  getArticleId: () => string,
  getDynamicLoadSize?: () => number,
  onLoadComplete?: () => void,
  breadcrumbActions?: UseCommentBreadcrumbReturn,
): UseCommentLoaderReturn {
  // 评论列表相关状态
  const commentList = ref<Comment[]>([])
  const commentLoading = ref(false)
  const commentNoMore = ref(false)
  const hasCommentPermission = ref(false)
  const sortType = ref('0') // 排序类型：0-按点赞数排序（默认），1-按时间排序，2-按回复数排序

  /**
   * 初始化评论权限
   * 优化：不再单独发送权限检查请求，权限检查集成到 loadCurrentCommentList 中
   */
  const initCommentPermission = (): void => {
    // 权限检查现在集成到 loadCurrentCommentList 中
    // 这里只是设置默认状态，实际权限检查在首次加载时进行
    hasCommentPermission.value = true // 默认假设有权限，加载时会验证
  }

  /**
   * 重置评论列表
   */
  const resetCommentList = () => {
    commentList.value = []
    commentLoading.value = false
    commentNoMore.value = false
  }

  /**
   * 添加评论到列表
   */
  const addCommentList = (list: Comment[]) => {
    if (!list || list.length === 0) return

    list
      .map(({ ...original }) => ({
        ...original,
        publishedAt: DateTime.getRelativeTime(original.publishedAt),
        exactPublishedAt: DateTime.toTimeString(original.publishedAt),
        fixed: false,
      }))
      .forEach((e: Comment) => {
        e.contentObj = tiptap.toJsonObject(e.content)
        e.quickCommentReply = undefined
        commentList.value.push(e)
      })
  }

  /**
   * 更新评论列表首位固定评论
   */
  const updateCommentListByFirstFixedComment = (comment: Comment) => {
    commentList.value.unshift({
      ...comment,
      publishedAt: DateTime.getRelativeTime(comment.publishedAt),
      exactPublishedAt: DateTime.toTimeString(comment.publishedAt),
      fixed: true,
      contentObj: tiptap.toJsonObject(comment.content),
    })
  }

  /**
   * 设置首位固定评论
   */
  const setFirstFixedComment = async (comment: Comment, newest: boolean = false) => {
    if (
      breadcrumbActions &&
      breadcrumbActions.lastBreadcrumbIndex.value > 0 &&
      (commentList.value.length == 0 || !commentList.value[0].fixed)
    ) {
      if (newest) {
        const res = await commentApi.loadById(comment.id)
        if (res?.data) {
          updateCommentListByFirstFixedComment(res.data as any)
        }
      } else {
        updateCommentListByFirstFixedComment(comment)
      }
    }
  }

  /**
   * 加载当前评论列表
   */
  const loadCurrentCommentList = (reset: boolean = true) => {
    // 如果已经在加载中，避免重复加载
    if (commentLoading.value && !reset) {
      return
    }

    // 如果已经没有更多数据，且不是主动重置
    if (commentNoMore.value && !reset) {
      // 如果没有数据，则重置加载状态让用户可以重试
      if (commentList.value.length === 0) {
        commentNoMore.value = false
      } else {
        return
      }
    }

    if (reset) {
      resetCommentList()
    }

    if (breadcrumbActions) {
      loadCommentList(breadcrumbActions.lastBreadcrumbComment.value)
    }
  }

  /**
   * 加载评论列表
   */
  const loadCommentList = (comment: Comment) => {
    // 第一检查，避免重复加载或无数据可加载
    if (commentLoading.value || commentNoMore.value) {
      return
    }

    // 设置loading状态
    commentLoading.value = true
    // 使用动态加载量或默认值5
    const loadSize = getDynamicLoadSize ? getDynamicLoadSize() : 5

    // 使用节流函数控制频率
    frequencyLimit.throttle(
      ARTICLE_COMMENTS,
      () => {
        // 获取分页ID
        const id =
          commentList.value.length > 0 ? commentList.value[commentList.value.length - 1].id : ''

        // 在执行API调用前再次检查，确保状态一致性
        if (commentNoMore.value) {
          commentLoading.value = false
          return
        }

        Promise.allSettled([
          commentApi
            .load(
              `?articleId=${getArticleId()}&id=${id}&parentCommentId=${comment?.id}&loadSize=${loadSize}&sortType=${sortType.value}`,
            )
            .catch((error) => {
              // 处理评论加载错误
              if (error.response && error.response.status === 403) {
                // 权限错误：设置权限状态并重置加载状态
                hasCommentPermission.value = false
                commentLoading.value = false
                // 不添加提示消息
                throw error // 继续传播错误，以便Promise.allSettled能捕获
              }
              throw error
            }),
          setFirstFixedComment(comment, true),
        ])
          .then((settledRes: PromiseSettledResult<unknown>[]) => {
            // 只有当第一个Promise不是rejected状态时才处理数据
            if (settledRes[0].status === 'fulfilled' && settledRes[0]?.value) {
              const res = settledRes[0]?.value as ResponseData<Comment[]>
              const dataList = res.data
              // 处理空数据情况
              if (dataList?.length == 0) {
                commentNoMore.value = true
                return
              }
              // 处理最后一页数据
              if (dataList.length < loadSize) {
                commentNoMore.value = true
              }
              // 将数据添加到列表
              addCommentList(dataList)
              if (breadcrumbActions) {
                breadcrumbActions.setShowReplyListBtn()
              }
              // DOM更新完成，由主函数处理滚动触发器重置
            } else {
              // 处理响应但没有数据的情况
              commentNoMore.value = true
            }
          })
          .catch((error) => {
            console.error('加载评论失败:', error)
            // 出错时恢复状态，允许用户重试
            commentNoMore.value = false
          })
          .finally(() => {
            // 无论如何，加载完成后取消loading状态
            commentLoading.value = false
            // 调用加载完成回调
            if (onLoadComplete) {
              onLoadComplete()
            }
          })
      },
      300,
    )
  }

  return {
    commentList,
    commentLoading,
    commentNoMore,
    hasCommentPermission,
    sortType,
    resetCommentList,
    loadCurrentCommentList,
    loadCommentList,
    addCommentList,
    setFirstFixedComment,
    initCommentPermission,
  }
}
