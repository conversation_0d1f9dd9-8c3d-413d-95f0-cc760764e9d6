package com.shenmo.wen.common.util;


import com.google.common.base.CaseFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.*;

/**
 * 线程池工厂
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class ExecutorServiceUtils {

    /**
     * 默认核心线程数
     */
    public static final int DEFAULT_CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors() << 1;
    /**
     * 默认最大线程数
     */
    public static final int DEFAULT_MAX_POOL_SIZE = DEFAULT_CORE_POOL_SIZE << 1;
    /**
     * 默认队列大小
     */
    public static final int DEFAULT_QUEUE_SIZE = DEFAULT_MAX_POOL_SIZE << 4;

    /**
     * 无参构造
     *
     * <AUTHOR>
     */
    private ExecutorServiceUtils() {
    }

    /**
     * 以类的名称创建默认指定大小的线程池
     *
     * @param cls 使用的类
     * @return 异步执行器服务接口
     * <AUTHOR>
     */
    public static ExecutorService createThreadPool(Class<?> cls) {
        return createThreadPool(CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_HYPHEN, cls.getSimpleName()) + "-", DEFAULT_CORE_POOL_SIZE, DEFAULT_MAX_POOL_SIZE, new ArrayBlockingQueue<>(DEFAULT_QUEUE_SIZE));
    }

    /**
     * 创建默认指定大小的线程池
     *
     * @param poolName 线程池的名称
     * @return 异步执行器服务接口
     * <AUTHOR>
     */
    public static ExecutorService createThreadPool(String poolName) {
        return createThreadPool(poolName, DEFAULT_CORE_POOL_SIZE, DEFAULT_MAX_POOL_SIZE, new ArrayBlockingQueue<>(DEFAULT_QUEUE_SIZE));
    }


    /**
     * 创建默认指定线程工厂与执行策略的线程池
     * <p>
     * <p>
     * {@link CustomizableThreadFactory}
     * {@link ThreadPoolExecutor.CallerRunsPolicy#rejectedExecution(Runnable, ThreadPoolExecutor)}
     *
     * @param cls       使用的类
     * @param workQueue 工作队列
     * @return 异步执行器服务接口
     * <AUTHOR>
     */
    public static ExecutorService createThreadPool(Class<?> cls, BlockingQueue<Runnable> workQueue) {

        return createThreadPool(cls, DEFAULT_CORE_POOL_SIZE, DEFAULT_MAX_POOL_SIZE, workQueue);
    }

    /**
     * 创建默认指定线程工厂与执行策略的线程池
     * <p>
     * <p>
     * {@link CustomizableThreadFactory}
     * {@link ThreadPoolExecutor.CallerRunsPolicy#rejectedExecution(Runnable, ThreadPoolExecutor)}
     *
     * @param cls          使用的类
     * @param corePoolSize 核心线程数
     * @param maxPoolSize  最大线程数
     * @param workQueue    工作队列
     * @return 异步执行器服务接口
     * <AUTHOR>
     */
    public static ExecutorService createThreadPool(Class<?> cls, int corePoolSize, int maxPoolSize, BlockingQueue<Runnable> workQueue) {

        return createThreadPool(CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_HYPHEN, cls.getSimpleName()) + "-", corePoolSize, maxPoolSize, workQueue);
    }

    /**
     * 创建默认指定线程工厂与执行策略的线程池
     * <p>
     * <p>
     * {@link CustomizableThreadFactory}
     * {@link ThreadPoolExecutor.CallerRunsPolicy#rejectedExecution(Runnable, ThreadPoolExecutor)}
     *
     * @param poolName     线程池的名称
     * @param corePoolSize 核心线程数
     * @param maxPoolSize  最大线程数
     * @param workQueue    工作队列
     * @return 异步执行器服务接口
     * <AUTHOR>
     */
    public static ExecutorService createThreadPool(String poolName, int corePoolSize, int maxPoolSize, BlockingQueue<Runnable> workQueue) {

        ExecutorService executorService = new ThreadPoolExecutor(corePoolSize,
                maxPoolSize, 30,
                TimeUnit.SECONDS, workQueue,
                new CustomizableThreadFactory(poolName), new ThreadPoolExecutor.CallerRunsPolicy()) {

            @Override
            public void shutdown() {
                super.shutdown();
                log.info("{} executor is shutdown!", poolName);
            }
        };
        log.info("{} executor is create!", poolName);
        return executorService;
    }
}
