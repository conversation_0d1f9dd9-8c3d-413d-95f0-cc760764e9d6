import fileApi from '@/api/file'
import { setupModalEvents } from '@/composables/image/useImageModalEvents'
import { useImagePreviewDrag } from '@/composables/image/useImagePreviewDrag'
import { THUMBNAIL } from '@/constants/image/filepath.constants'

// 配置常量
const IMAGE_PREVIEW_CONFIG = {
  MIN_LOADING_TIME: 500,
  LOADING_OPACITY: 0.5,
  FULL_OPACITY: 1,
  DRAG_CONFIG: {
    minScale: 0.5,
    maxScale: 3,
    scaleStep: 0.1,
  },
} as const

// 类型定义
interface ImageData {
  isOriginal: boolean
  thumbnailSrc: string
  isExternal: boolean
}

/**
 * 创建模态框元素
 */
function createModalElements(): { modal: HTMLDivElement; previewImg: HTMLImageElement } {
  const modal = document.createElement('div')
  modal.classList.add('modal-overlay')

  const previewImg = document.createElement('img')
  previewImg.alt = '图片预览'
  modal.appendChild(previewImg)
  document.body.appendChild(modal)

  // 触发显示动画
  modal.classList.add('modal-overlay-active')

  return { modal, previewImg }
}

/**
 * 获取图片数据信息
 */
function getImageData(img: HTMLImageElement, originalSrc: string): ImageData {
  const isOriginal = img.dataset.isOriginal === 'true'
  const thumbnailSrc = img.dataset.thumbnailSrc || originalSrc
  const isExternal = originalSrc.startsWith('http://') || originalSrc.startsWith('https://')

  return { isOriginal, thumbnailSrc, isExternal }
}

/**
 * 处理缩略图预览和原图加载
 */
function handleThumbnailPreview(
  previewImg: HTMLImageElement,
  modal: HTMLDivElement,
  originalSrc: string,
  img: HTMLImageElement,
): void {
  // 先显示缩略图
  previewImg.src = img.src
  previewImg.style.opacity = String(IMAGE_PREVIEW_CONFIG.LOADING_OPACITY)

  // 添加加载动画
  const loadingSpinner = document.createElement('div')
  loadingSpinner.classList.add('loading-spinner')
  modal.appendChild(loadingSpinner)

  // 加载原图
  const originalImg = new Image()
  const animationStartTime = Date.now()

  originalImg.onload = () => {
    const loadTime = Date.now() - animationStartTime

    const setOriginal = () => {
      loadingSpinner.style.display = 'none'
      previewImg.src = originalImg.src
      previewImg.style.opacity = String(IMAGE_PREVIEW_CONFIG.FULL_OPACITY)
      previewImg.dataset.originalFullUrl = originalImg.src
    }

    if (loadTime < IMAGE_PREVIEW_CONFIG.MIN_LOADING_TIME) {
      setTimeout(setOriginal, IMAGE_PREVIEW_CONFIG.MIN_LOADING_TIME - loadTime)
    } else {
      setOriginal()
    }
  }

  originalImg.onerror = () => {
    loadingSpinner.style.display = 'none'
    previewImg.style.opacity = String(IMAGE_PREVIEW_CONFIG.FULL_OPACITY)
    console.warn('原图加载失败，使用缩略图')
  }

  originalImg.src = fileApi.getResourceURL(originalSrc.replace(THUMBNAIL, ''))
}

/**
 * 设置模态框关闭逻辑
 */
function setupModalClose(
  modal: HTMLDivElement,
  previewImg: HTMLImageElement,
  img: HTMLImageElement,
  originalSrc: string,
  imageData: ImageData,
  emit: (e: 'image-preview-open' | 'image-preview-close') => void,
  cleanupModalEvents: () => void,
  dragHandler: { cleanup: () => void },
): () => void {
  return () => {
    modal.classList.remove('modal-overlay-active')
    modal.addEventListener(
      'transitionend',
      () => {
        if (!modal.classList.contains('modal-overlay-active')) {
          // 更新原图缓存
          if (
            !imageData.isExternal &&
            previewImg.dataset.originalFullUrl &&
            imageData.thumbnailSrc.includes(THUMBNAIL) &&
            !imageData.isOriginal
          ) {
            img.src = previewImg.dataset.originalFullUrl
            img.dataset.isOriginal = 'true'
            img.dataset.originalSrc = originalSrc.replace(THUMBNAIL, '')
          }

          document.body.removeChild(modal)
          cleanupModalEvents()
          dragHandler.cleanup()
          emit('image-preview-close')
        }
      },
      { once: true },
    )
  }
}

/**
 * 打开弹幕图片预览模态框
 * 实现图片点击放大查看功能，支持原图加载
 * 与编辑器图片行为保持一致：默认缩略图，预览展示原图，预览后缓存原图
 *
 * @param img 预览的图片元素
 * @param originalSrc 原始图片地址
 * @param emit 事件发射函数
 * @param event 触发事件对象
 */
export function openDanmuImageViewer(
  img: HTMLImageElement,
  originalSrc: string,
  emit: (e: 'image-preview-open' | 'image-preview-close') => void,
  event?: Event,
): void {
  // 阻止事件冒泡
  event?.preventDefault()
  event?.stopPropagation()

  // 发送暂停弹幕事件
  emit('image-preview-open')

  // 创建模态框元素
  const { modal, previewImg } = createModalElements()

  // 获取图片数据
  const imageData = getImageData(img, originalSrc)

  // 处理图片预览
  if (
    !imageData.isExternal &&
    imageData.thumbnailSrc.includes(THUMBNAIL) &&
    !imageData.isOriginal
  ) {
    handleThumbnailPreview(previewImg, modal, originalSrc, img)
  } else {
    // 已经是原图，直接显示
    previewImg.src = img.src
    previewImg.style.opacity = String(IMAGE_PREVIEW_CONFIG.FULL_OPACITY)
  }

  // 添加拖拽和缩放功能
  const dragHandler = useImagePreviewDrag(
    previewImg,
    IMAGE_PREVIEW_CONFIG.DRAG_CONFIG,
    () => closeModal(),
    modal,
  )

  // 设置模态框通用事件处理
  const cleanupModalEvents = setupModalEvents(
    modal,
    () => closeModal(),
    dragHandler.handleWheelZoom,
  )

  // 设置关闭模态框函数
  const closeModal = setupModalClose(
    modal,
    previewImg,
    img,
    originalSrc,
    imageData,
    emit,
    cleanupModalEvents,
    dragHandler,
  )

  // 初始化拖拽功能
  dragHandler.initialize()
}
