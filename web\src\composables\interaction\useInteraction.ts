import { ref, type Ref } from 'vue'

import interactionApi from '@/api/interaction'
import type { InteractionResponse } from '@/types/api/interaction-response.types'
import message from '@/utils/ui/message'

/**
 * 交互类型枚举
 */
export enum InteractionType {
  /** 点赞 */
  LIKE = 1,
  /** 踩 */
  DISLIKE = 2,
}

/**
 * 目标类型枚举
 */
export enum TargetType {
  /** 评论 */
  COMMENT = 0,
  /** 文章 */
  ARTICLE = 1,
}

/**
 * 交互状态类型
 */
interface InteractionState {
  /** 点赞数 */
  likeCount: number
  /** 踩数 */
  dislikeCount: number
  /** 是否已点赞 */
  isLike: boolean
  /** 是否已踩 */
  isDislike: boolean
}

/**
 * 交互组合式函数返回值类型
 */
interface UseInteractionReturn {
  /** 交互加载状态 */
  interactionLoading: Ref<boolean>
  /** 处理点赞操作 */
  handleLike: (targetId: string, targetType: TargetType, state: InteractionState) => Promise<void>
  /** 处理踩操作 */
  handleDislike: (
    targetId: string,
    targetType: TargetType,
    state: InteractionState,
  ) => Promise<void>
  /** 通用交互处理 */
  handleInteraction: (
    targetId: string,
    targetType: TargetType,
    actionType: InteractionType,
    state: InteractionState,
  ) => Promise<void>
}

/**
 * 交互管理组合式函数
 * 提供点赞、踩等交互功能的统一管理
 */
export function useInteraction(): UseInteractionReturn {
  // 交互加载状态
  const interactionLoading = ref(false)

  /**
   * 通用交互处理函数
   * @param targetId 目标ID
   * @param targetType 目标类型
   * @param actionType 操作类型
   * @param state 交互状态对象
   */
  const handleInteraction = async (
    targetId: string,
    targetType: TargetType,
    actionType: InteractionType,
    state: InteractionState,
  ): Promise<void> => {
    if (interactionLoading.value) {
      return
    }

    interactionLoading.value = true

    try {
      const reqParam = {
        targetType,
        targetId,
        actionType,
      }

      const res = await interactionApi.save({
        ...reqParam,
        type: reqParam.targetType,
      })

      const data = res?.data as InteractionResponse
      if (data) {
        // 更新交互数据
        state.likeCount = data.likeCount
        state.dislikeCount = data.dislikeCount

        const isLike = actionType === InteractionType.LIKE

        // 处理交互状态
        if (data.cancel) {
          // 取消操作
          if (isLike) {
            message.info('赞取消')
            state.isLike = false
          } else {
            message.info('踩取消')
            state.isDislike = false
          }
        } else {
          // 新增操作
          if (isLike) {
            message.success('赞 :)')
            state.isLike = true
            state.isDislike = false // 点赞时取消踩
          } else {
            message.warning('踩 :(')
            state.isDislike = true
            state.isLike = false // 踩时取消点赞
          }
        }
      }
    } catch (error) {
      console.error('交互操作失败:', error)
      message.error('操作失败，请稍后重试')
    } finally {
      interactionLoading.value = false
    }
  }

  /**
   * 处理点赞操作
   * @param targetId 目标ID
   * @param targetType 目标类型
   * @param state 交互状态对象
   */
  const handleLike = async (
    targetId: string,
    targetType: TargetType,
    state: InteractionState,
  ): Promise<void> => {
    await handleInteraction(targetId, targetType, InteractionType.LIKE, state)
  }

  /**
   * 处理踩操作
   * @param targetId 目标ID
   * @param targetType 目标类型
   * @param state 交互状态对象
   */
  const handleDislike = async (
    targetId: string,
    targetType: TargetType,
    state: InteractionState,
  ): Promise<void> => {
    await handleInteraction(targetId, targetType, InteractionType.DISLIKE, state)
  }

  return {
    interactionLoading,
    handleLike,
    handleDislike,
    handleInteraction,
  }
}
