package com.shenmo.wen.app.core.user.pojo.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 用户特权验证启动请求
 * 
 * <AUTHOR>
 */
@Data
public class WenUserPrivilegeVerificationStartReq {

    /**
     * 特权ID
     */
    @NotNull(message = "特权ID不能为空")
    private Long privilegeId;

    /**
     * 验证类型：0-短信验证，1-二维码验证
     */
    @NotNull(message = "验证类型不能为空")
    private Integer verificationType;

    /**
     * 二维码内容（当验证类型为二维码时必填）
     */
    private String qrcc;
}
