import scrollIntoView from 'smooth-scroll-into-view-if-needed'

import fileApi from '@/api/file'

import type { MentionUser, MentionViewOptions } from './types'
import type { Editor } from '@tiptap/core'
import type { SuggestionKeyDownProps, SuggestionProps } from '@tiptap/suggestion'

export class MentionView {
  private readonly editor: Editor
  private readonly options: MentionViewOptions
  private element: HTMLElement | undefined
  private index: number | undefined
  private nodes: HTMLElement[] | undefined
  private items: MentionUser[] | undefined
  private command: ((item: { id: string; label: string; avatar?: string }) => void) | undefined

  public static create(options: MentionViewOptions) {
    return () => new MentionView(options)
  }

  constructor(options: MentionViewOptions) {
    this.editor = options.editor
    this.options = options
  }

  public onStart(props: SuggestionProps) {
    // 重置状态
    this.index = 0
    this.nodes = []
    this.items = []
    this.command = props.command

    // 创建根元素
    this.element = document.createElement('div')
    this.element.classList.add('dropdown-menu')

    // 添加自定义类名
    for (const clazz of this.options.classes ?? []) {
      this.element.classList.add(clazz)
    }

    // 添加自定义属性
    for (const [key, val] of Object.entries(this.options.attributes ?? {})) {
      this.element.setAttribute(key, val)
    }

    // 添加到页面
    document.body.appendChild(this.element)

    this.onUpdate(props)
  }

  public onUpdate(props: SuggestionProps) {
    if (!this.element || this.index === undefined || !this.nodes || !this.items) {
      return
    }

    // 更新项目
    this.items = props.items
    this.command = props.command

    // 渲染菜单项
    this.render()

    // 更新位置
    if (props.clientRect) {
      this.updatePosition(props.clientRect)
    }
  }

  public onKeyDown(props: SuggestionKeyDownProps) {
    if (!this.element || this.index === undefined || !this.nodes || !this.items) {
      return false
    }

    if (props.event.key === 'Escape') {
      this.hide()
      return true
    }

    if (props.event.key === 'Enter') {
      const item = this.items[this.index]
      if (item && this.command) {
        this.command({
          id: item.id,
          label: item.username,
          avatar: item.avatar,
        })
      }
      return true
    }

    if (props.event.key === 'ArrowUp') {
      this.selectItem(this.index - 1 < 0 ? this.items.length - 1 : this.index - 1, true)
      return true
    }

    if (props.event.key === 'ArrowDown') {
      this.selectItem(this.index + 1 >= this.items.length ? 0 : this.index + 1, true)
      return true
    }

    return false
  }

  public onExit() {
    this.hide()
    this.cleanup()
  }

  private selectItem(index: number, scroll?: boolean) {
    if (!this.element || this.index === undefined || !this.nodes || !this.items) {
      return
    }

    // 确保索引有效
    this.index = Math.max(0, Math.min(index, this.items.length - 1))

    // 更新选中状态
    for (let i = 0; i < this.nodes.length; i++) {
      if (i === this.index) {
        this.nodes[i].classList.add('is-selected')
      } else {
        this.nodes[i].classList.remove('is-selected')
      }
    }

    // 滚动到选中项
    if (scroll && this.nodes[this.index]) {
      scrollIntoView(this.nodes[this.index], {
        behavior: 'smooth',
        block: 'nearest',
        inline: 'nearest',
        boundary: (parent) => parent !== this.element,
      })
    }
  }

  private render() {
    if (!this.element || this.index === undefined || !this.items) {
      return
    }

    // 清空内容
    while (this.element.firstChild) {
      this.element.removeChild(this.element.firstChild)
    }

    // 确保索引有效
    this.index = Math.max(0, Math.min(this.index, Math.max(0, this.items.length - 1)))

    if (this.items.length) {
      this.nodes = []

      for (let i = 0; i < this.items.length; i++) {
        const item = this.items[i]
        const button = document.createElement('button')
        button.setAttribute('type', 'button')

        // 头像
        if (item.avatar) {
          const avatar = document.createElement('img')
          avatar.classList.add('dropdown-avatar')
          avatar.src = fileApi.getResourceURL(item.avatar)
          avatar.alt = item.username
          avatar.loading = 'lazy'
          button.appendChild(avatar)
        }

        // 用户名
        const name = document.createElement('span')
        name.textContent = item.username
        button.appendChild(name)

        // 设置选中状态
        if (i === this.index) {
          button.classList.add('is-selected')
        }

        // 添加事件监听
        button.addEventListener('click', () => {
          if (this.command) {
            this.command({
              id: item.id,
              label: item.username,
              avatar: item.avatar,
            })
          }
        })

        this.nodes.push(button)
      }

      // 添加所有节点到DOM
      for (const node of this.nodes) {
        this.element.appendChild(node)
      }

      this.show()
    } else {
      // 显示空状态
      const empty = document.createElement('div')
      empty.classList.add('item')
      empty.textContent = this.options.dictionary?.empty ?? '...'
      this.element.appendChild(empty)

      this.show()
    }
  }

  private updatePosition(clientRect: () => DOMRect | null) {
    if (!this.element) return

    const rect = clientRect()
    if (!rect) return

    const viewportHeight = window.innerHeight
    const viewportWidth = window.innerWidth

    // 设置基本样式
    this.element.style.position = 'fixed'
    this.element.style.zIndex = '10000'

    // 获取菜单的实际尺寸（需要先设置位置才能获取）
    this.element.style.left = `${rect.left}px`
    this.element.style.top = `${rect.bottom + 8}px`
    this.element.style.visibility = 'hidden'
    this.element.style.display = 'block'

    const menuRect = this.element.getBoundingClientRect()
    const menuHeight = menuRect.height
    const menuWidth = menuRect.width

    // 恢复可见性
    this.element.style.visibility = 'visible'

    // 计算最佳位置
    let left = rect.left
    let top = rect.bottom + 8
    let showAbove = false

    // 检查底部空间是否足够
    const spaceBelow = viewportHeight - rect.bottom - 8
    const spaceAbove = rect.top - 8

    if (spaceBelow < menuHeight && spaceAbove > menuHeight) {
      // 底部空间不足，但上方空间足够，显示在上方
      top = rect.top - menuHeight - 8
      showAbove = true
    } else if (spaceBelow < menuHeight && spaceAbove < menuHeight) {
      // 上下空间都不足，选择空间更大的一侧
      if (spaceAbove > spaceBelow) {
        top = rect.top - menuHeight - 8
        showAbove = true
      }
      // 如果还是不够，让菜单可滚动
      if ((showAbove && spaceAbove < menuHeight) || (!showAbove && spaceBelow < menuHeight)) {
        this.element.style.maxHeight = `${Math.max(spaceAbove, spaceBelow) - 16}px`
        this.element.style.overflowY = 'auto'
      }
    }

    // 检查左右边界
    if (left + menuWidth > viewportWidth) {
      left = Math.max(8, viewportWidth - menuWidth - 8)
    }
    if (left < 8) {
      left = 8
    }

    // 应用最终位置
    this.element.style.left = `${left}px`
    this.element.style.top = `${top}px`

    // 添加位置指示类名，用于样式调整
    this.element.classList.toggle('dropdown-menu-above', showAbove)
    this.element.classList.toggle('dropdown-menu-below', !showAbove)
  }

  private show() {
    if (this.element) {
      this.element.style.display = 'block'
    }
  }

  private hide() {
    if (this.element) {
      this.element.style.display = 'none'
    }
  }

  private cleanup() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element)
    }
    this.element = undefined
    this.nodes = undefined
    this.items = undefined
    this.command = undefined
  }
}
