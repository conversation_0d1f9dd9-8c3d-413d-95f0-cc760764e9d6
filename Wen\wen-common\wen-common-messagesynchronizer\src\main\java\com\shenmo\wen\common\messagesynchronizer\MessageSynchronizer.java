package com.shenmo.wen.common.messagesynchronizer;

import java.lang.annotation.*;

/**
 * 消息同步器
 *
 * <AUTHOR>
 */
@Documented
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface MessageSynchronizer {

    /**
     * 消息id
     * <p>
     * 支持Spel表达式
     */
    String value();

    /**
     * 单实例锁
     * <p>
     * 该锁为单机锁, 避免消息发布时当前方法已经在执行过程中从而产生预料外的影响
     */
    boolean singleInstanceLock() default true;

    /**
     * 基于{@link #singleInstanceLock()}实现是否同步等待
     * <p>
     * 如果不等待, 那么当发现当前监听的方法已经在执行时, 直接放弃消息发布
     */
    boolean syncWait() default true;

}
