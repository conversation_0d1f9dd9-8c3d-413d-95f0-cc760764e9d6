import type { FavoriteResponse } from '@/types/api/favorite-response.types'
import type { RequestParams } from '@/types/api/request.types'
import { type ResponseData } from '@/types/api/response-data.types'
import api from '@/utils/api/api'

/** 收藏操作请求参数类型 */
interface FavoriteParams extends RequestParams {
  /** 目标ID（文章ID） */
  targetId: string
  /** 目标类型（1: 文章, 2: 评论） */
  targetType: number
  /** 收藏夹ID（可选，默认收藏到默认收藏夹） */
  folderId?: string
}

/** 收藏切换请求参数类型 */
interface FavoriteToggleParams extends RequestParams {
  /** 目标ID（文章ID） */
  targetId: string
  /** 目标类型（1: 文章, 2: 评论） */
  targetType: number
}

/**
 * 收藏相关API接口
 * 提供内容收藏和取消收藏功能
 */
const favoriteApi = {
  /** API基础路径 */
  URL: '/core/favorites',

  /**
   * 保存收藏记录
   * 将指定内容添加到收藏夹
   * @param params 收藏参数，包含目标ID、类型等
   * @returns 返回收藏操作结果
   */
  save: async (params: FavoriteParams): Promise<ResponseData<FavoriteResponse>> => {
    const response = await api.post<ResponseData<FavoriteResponse>>(favoriteApi.URL, params)
    return response.data
  },

  /**
   * 切换收藏状态
   * 切换用户对目标内容的收藏状态（收藏/取消收藏）
   * @param params 收藏切换参数，包含目标ID、类型等
   * @returns 返回切换后的收藏状态
   */
  toggle: async (params: FavoriteToggleParams): Promise<ResponseData<FavoriteResponse>> => {
    const response = await api.post<ResponseData<FavoriteResponse>>(favoriteApi.URL, params)
    return response.data
  },
}

export default favoriteApi
