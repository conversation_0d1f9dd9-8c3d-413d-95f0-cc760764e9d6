<template>
  <div class="theme-toggle-scene" :class="{ 'is-dark': isDarkTheme }" @click="handleThemeSwitch">
    <div class="sky">
      <div class="sun" :class="{ 'sun-set': isDarkTheme }">
        <Sun24Filled />
      </div>
      <div class="moon" :class="{ 'moon-rise': isDarkTheme }">
        <Moon24Filled />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

import { Sun24Filled, Moon24Filled } from '@/icons'
import { activeTheme, toggleTheme, ThemeType } from '@/utils/theme/theme'

// 主题相关
const isDarkTheme = computed(() => activeTheme.value === ThemeType.DARK)

// 处理主题切换
const handleThemeSwitch = () => {
  toggleTheme()
}
</script>

<style lang="scss" scoped>
@use '@/styles/theme/theme-toggle';
</style>
