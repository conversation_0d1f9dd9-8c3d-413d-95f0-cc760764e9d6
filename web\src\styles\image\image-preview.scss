/**
 * 图片预览模态框样式
 * 用于 Tiptap 编辑器和弹幕渲染器的图片预览功能
 */

/* 图片预览模态框容器 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 85%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  cursor: zoom-out;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;
  padding: 2rem;
  box-sizing: border-box;
  backdrop-filter: blur(3px);
  overflow: hidden;
}

/* 模态框激活状态 */
.modal-overlay-active {
  opacity: 1;
  visibility: visible;
}

/* 预览图片样式 */
.modal-overlay img {
  max-width: 95%;
  max-height: 90vh;
  object-fit: contain;
  border-radius: 0.25rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 25%);
  transform: scale(0.95);
  opacity: 0;
  transition:
    opacity 0.5s ease,
    transform 0.3s ease;
  cursor: grab;
  user-select: none;
  -webkit-user-drag: none;

  &:active {
    cursor: grabbing;
  }

  /* 拖拽时的视觉反馈 */
  &.dragging {
    cursor: grabbing;
    transition: none;
  }

  /* 缩放时保持清晰度和性能优化 */
  &.zooming {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    will-change: transform;
    backface-visibility: hidden;
    perspective: 1000px;
    transform-style: preserve-3d;
  }
}

/* 图片激活状态 */
.modal-overlay-active img {
  opacity: 1;
  transform: scale(1);
}

/* 加载动画 */
.loading-spinner {
  border: 0.2rem solid rgba(255, 255, 255, 20%);
  border-top: 0.2rem solid #f0f0f0;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -1.25rem;
  margin-left: -1.25rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 移动设备响应式样式 */
@media (width <= 768px) {
  .modal-overlay {
    padding: 1rem;
  }

  .modal-overlay img {
    max-width: 100%;
  }

  .loading-spinner {
    width: 2rem;
    height: 2rem;
    margin-top: -1rem;
    margin-left: -1rem;
  }
}
