<!--
  邮箱验证码输入组件

  功能说明：
  - 邮箱验证码输入框
  - 发送验证码按钮
  - 倒计时功能
  - 按钮状态管理
-->
<template>
  <div class="email-code-container">
    <NInput
      class="login-form-ipt email-code-input"
      v-model:value="value"
      placeholder="请输入验证码"
      maxlength="6"
      @keyup.enter="$emit('keyup.enter')"
    />
    <NButton
      class="send-code-btn"
      :disabled="emailCodeState.sendCodeDisabled || disabled"
      @click="$emit('send-code')"
      text
      :type="buttonType"
      size="small"
    >
      {{ emailCodeState.sendCodeText }}
    </NButton>
  </div>
</template>

<script lang="ts" setup>
import { NInput, NButton } from 'naive-ui'
import { computed } from 'vue'

import type { EmailCodeState } from '@/types/component/email-code-state.types'

// 定义组件 Props
interface EmailCodeInputProps {
  value: string
  emailCodeState: EmailCodeState
  disabled?: boolean
  buttonType?: 'info' | 'primary'
}

const props = withDefaults(defineProps<EmailCodeInputProps>(), {
  disabled: false,
  buttonType: 'info',
})

// 定义组件 Emits
interface EmailCodeInputEmits {
  'update:value': [value: string]
  'send-code': []
  'keyup.enter': []
}

const emit = defineEmits<EmailCodeInputEmits>()

// 双向绑定
const value = computed({
  get: () => props.value,
  set: (val) => emit('update:value', val),
})
</script>

<style lang="scss" scoped>
@use '@/styles/auth/email-code-input';
</style>
