// 全局基础样式
// 包含全局重置、溢出控制和基础元素样式

/* 全局溢出控制 */
html,
body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  overscroll-behavior: none; /* 防止页面反弹 */
}

/* 移动端允许外层滚动 */
@media (width <= 768px) {
  html,
  body {
    overflow: auto; /* 允许外层滚动，用于文章和评论区域切换 */
    height: auto;
  }
}

/* 阻止iOS设备上的弹性滚动 */
html {
  position: fixed;
  height: 100%;
  width: 100%;
  touch-action: manipulation;
}

/* 防止输入框自动缩放 */
input,
textarea,
select {
  font-size: 16px; /* 16px是iOS不会自动缩放的最小字体大小 */
  max-height: 100%; /* 确保不会导致页面错位 */
}

/* 确保所有容器在过渡期间不会出现滚动条 */
.article-container,
.comment-container {
  overscroll-behavior: none;
}

/* 防止评论相关动画导致页面滚动条 */
.comment-info-container,
.comment-list-container {
  overscroll-behavior: none;
  overflow-x: hidden; /* 防止水平滚动条 */
}

/* 在评论回复框动画期间防止页面滚动条 */
.comment-reply-enter-active,
.comment-reply-leave-active {
  overscroll-behavior: none;
}

/* 快速评论框激活时的溢出控制 */
body.comment-reply-active {
  overflow: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
}

/* 移动端快速评论框激活时的特殊处理 */
@media (width <= 768px) {
  body.comment-reply-active {
    overflow: auto; /* 移动端允许外层滚动 */
    position: static; /* 移除固定定位 */
    width: auto;
    height: auto;
  }
}

.comment-info-container.has-quick-reply {
  overflow: hidden;
  max-height: 100vh;
  max-height: 100dvh;
}

/* 移动端评论容器的特殊处理 */
@media (width <= 768px) {
  .comment-info-container.has-quick-reply {
    overflow: hidden; /* 保持隐藏，内部滚动 */
    max-height: 100%;
  }
}

/* 防止应用根容器出现滚动条 */
#app {
  overflow: hidden;
  max-height: 100vh;
  max-height: 100dvh;
  position: relative;
}

.article-layout,
.home-layout {
  overflow: hidden;
  max-height: 100vh;
  max-height: 100dvh;
  position: relative;
}

/* 移动端布局调整 */
@media (width <= 768px) {
  .article-layout {
    overflow-y: auto; /* 恢复外层滚动 */
    max-height: 100vh;
    max-height: 100dvh;
    height: 100vh;
    height: 100dvh;
  }
}

/* 防止 SVG 图标和相关元素被选中时出现蓝色背景 */
svg,
.icon,
[class*='icon'],
.scope-icon-wrapper,
.clickable {
  user-select: none;
}

/* 确保按钮和可点击元素显示正确的鼠标指针 */

/* 这些规则补充了重构过程中丢失的全局按钮样式 */
button,
[role='button'],
.n-button,
.cursor-pointer,
.clickable,
input[type='button'],
input[type='submit'],
input[type='reset'],
a[href],
label[for],
select,
summary,
[tabindex]:not([tabindex='-1']) {
  cursor: pointer;
}

/* 确保禁用状态的元素显示正确的鼠标指针 */
button:disabled,
[role='button']:disabled,
.n-button:disabled,
input:disabled,
select:disabled,
textarea:disabled,
.disabled {
  cursor: not-allowed;
}

/* 防止图标周围的文本在点击图标时被意外选中 */
.article-header,
.card-item .n-card-header,
.flex-between-center {
  /* 允许文本选择，但防止意外选择 */
  user-select: text;

  /* 但是对于图标元素，仍然禁用选择 */
  svg,
  .icon,
  [class*='icon'],
  .scope-icon-wrapper,
  .clickable {
    user-select: none;
  }
}
