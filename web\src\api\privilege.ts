import { type ResponseData } from '@/types/api/response-data.types'
import type { PrivilegeActivateRequest } from '@/types/privilege/privilege-activate-request.types'
import type { PrivilegeCodeReq } from '@/types/privilege/privilege-code-req.types'
import type { PrivilegeCodeRequest } from '@/types/privilege/privilege-code-request.types'
import type { PrivilegeResponse } from '@/types/privilege/privilege-response.types'
import type { PrivilegeSearchRequest } from '@/types/privilege/privilege-search-request.types'
import type { PrivilegeTemplateSaveReq } from '@/types/privilege/privilege-template-save-req.types'
import type { PrivilegeTemplateSaveRequest } from '@/types/privilege/privilege-template-save-request.types'
import type { PrivilegeTemplate } from '@/types/privilege/privilege-template.types'
import api from '@/utils/api/api'

/**
 * 用户特权相关API接口
 * 提供特权激活码生成、激活、搜索等功能
 */
const privilegeApi = {
  /** API基础路径 */
  URL: '/core/user-privileges',

  /**
   * 生成特权激活码
   * 根据指定参数生成特权激活码
   * @param request 激活码生成请求参数
   * @returns 返回生成的激活码
   */
  generateCode: async (request: PrivilegeCodeRequest): Promise<ResponseData<string>> => {
    const response = await api.post<ResponseData<string>>(privilegeApi.URL + '/code', request)
    return response.data
  },

  /**
   * 激活特权
   * 使用激活码激活用户特权
   * @param request 特权激活请求参数
   * @returns 返回激活结果
   */
  activate: async (request: PrivilegeActivateRequest): Promise<ResponseData<boolean>> => {
    const response = await api.post<ResponseData<boolean>>(
      privilegeApi.URL + '/activation',
      request,
    )
    return response.data
  },

  /**
   * 搜索用户特权
   * 根据指定条件搜索用户特权列表
   * @param params 搜索参数
   * @param signal 请求取消信号
   * @returns 返回匹配的特权列表
   */
  search: async (
    params?: PrivilegeSearchRequest,
    signal?: AbortSignal,
  ): Promise<ResponseData<PrivilegeResponse[]>> => {
    const response = await api.get<ResponseData<PrivilegeResponse[]>>(privilegeApi.URL, params, {
      signal,
    })
    return response.data
  },
}

/**
 * 用户特权模板相关API接口
 * 提供特权模板搜索、保存等功能
 */
const privilegeTemplateApi = {
  /** API基础路径 */
  URL: '/core/user-privilege-templates',

  /**
   * 搜索特权模板
   * 根据模板名称搜索匹配的模板列表
   * @param name 模板名称关键词
   * @returns 返回匹配的模板列表
   */
  search: async (name: string): Promise<ResponseData<PrivilegeTemplate[]>> => {
    const response = await api.get<ResponseData<PrivilegeTemplate[]>>(privilegeTemplateApi.URL, {
      name,
    })
    return response.data
  },

  /**
   * 保存特权模板
   * 创建或更新特权模板
   * @param request 模板保存请求参数
   * @returns 返回保存结果
   */
  save: async (request: PrivilegeTemplateSaveRequest): Promise<ResponseData<boolean>> => {
    const response = await api.post<ResponseData<boolean>>(privilegeTemplateApi.URL, request)
    return response.data
  },
}

// 扩展 privilegeApi 以支持新的接口
const extendedPrivilegeApi = {
  ...privilegeApi,

  /**
   * 生成特权码（新接口）
   * @param request 特权码生成请求参数
   * @returns 返回生成的激活码
   */
  generatePrivilegeCode: async (request: PrivilegeCodeReq): Promise<ResponseData<string>> => {
    const response = await api.post<ResponseData<string>>(
      '/api/user-privilege/generate-code',
      request,
    )
    return response.data
  },

  /**
   * 搜索用户（用于下拉选择）
   * @param keyword 搜索关键词
   * @returns 用户列表
   */
  searchUsers: async (
    keyword: string,
  ): Promise<ResponseData<Array<{ id: number; username: string; avatar?: string }>>> => {
    const response = await api.get<
      ResponseData<Array<{ id: number; username: string; avatar?: string }>>
    >('/api/user/search', { keyword })
    return response.data
  },

  /**
   * 搜索特权模板（用于下拉选择）
   * @param keyword 搜索关键词
   * @returns 模板列表
   */
  searchTemplates: async (
    keyword: string,
  ): Promise<ResponseData<Array<{ id: number; name: string }>>> => {
    const response = await api.get<ResponseData<Array<{ id: number; name: string }>>>(
      '/api/user-privilege-template/search',
      { keyword },
    )
    return response.data
  },
}

// 扩展 privilegeTemplateApi 以支持新的接口
const extendedPrivilegeTemplateApi = {
  ...privilegeTemplateApi,

  /**
   * 保存特权模板（新接口）
   * @param request 模板保存请求参数
   * @returns 返回保存结果
   */
  savePrivilegeTemplate: async (
    request: PrivilegeTemplateSaveReq,
  ): Promise<ResponseData<boolean>> => {
    const response = await api.post<ResponseData<boolean>>(
      '/api/user-privilege-template/save',
      request,
    )
    return response.data
  },
}

export {
  extendedPrivilegeApi as privilegeApi,
  extendedPrivilegeTemplateApi as privilegeTemplateApi,
}
export default extendedPrivilegeApi
