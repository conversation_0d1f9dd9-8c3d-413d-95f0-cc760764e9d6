import type { JsonValue } from '@/types/json/json-value-base.types'

/**
 * 简单TipTap编辑器组件引用类型定义
 * 定义简化版TipTap编辑器的引用接口
 */
export interface TiptapEditorRef {
  /** 编辑器实例 */
  editor: {
    /** 编辑器是否为空 */
    isEmpty: boolean
    /** 获取编辑器JSON内容 */
    getJSON: () => JsonValue
    /** 编辑器命令 */
    commands: {
      /** 清空编辑器内容 */
      clearContent: (emitUpdate?: boolean) => void
      /** 设置编辑器内容 */
      setContent: (content: JsonValue, emitUpdate?: boolean) => void
    }
    /** 编辑器存储 */
    storage: {
      /** Markdown解析器 */
      markdown?: {
        parser: {
          /** 解析Markdown内容 */
          parse: (content: string) => JsonValue
        }
      }
    }
  }
}
