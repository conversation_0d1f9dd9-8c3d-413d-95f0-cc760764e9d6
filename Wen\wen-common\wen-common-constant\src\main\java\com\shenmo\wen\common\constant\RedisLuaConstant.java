package com.shenmo.wen.common.constant;

/**
 *
 * <AUTHOR>
 */
public interface RedisLuaConstant {

    String SET_REMOVE_ADD = """
            if redis.call('SISMEMBER', KEYS[1], ARGV[1]) == 1 then
                redis.call('SREM', KEYS[1], ARGV[1])
                redis.call('SADD', KEYS[2], ARGV[1])
                return 1
            else
                return 0
            end""";

    String SET_REMOVE_ADD_ALL = """
            local messages = redis.call('SMEMBERS', KEYS[1])
            if #messages > 0 then
                redis.call('SREM', KEYS[1], unpack(messages))
                redis.call('SADD', KEYS[2], unpack(messages))
            end
            return #messages
            """;
}
