package com.shenmo.wen.app.authentication.service;

import com.shenmo.wen.app.authentication.pojo.req.SendEmailCodeReq;

/**
 * 邮件验证服务接口
 * 
 * <AUTHOR>
 */
public interface WenEmailVerificationService {

    /**
     * 发送验证码到指定邮箱
     *
     * @param req 发送验证码请求
     * @return 是否发送成功
     */
    boolean sendVerificationCode(SendEmailCodeReq req);

    /**
     * 验证邮箱验证码
     * 
     * @param email 邮箱地址
     * @param code  验证码
     * @param type  验证码类型（login/register）
     * @return 是否验证成功
     */
    boolean verifyCode(String email, String code, String type);

    /**
     * 生成验证码
     * 
     * @return 验证码
     */
    String generateCode();
}
