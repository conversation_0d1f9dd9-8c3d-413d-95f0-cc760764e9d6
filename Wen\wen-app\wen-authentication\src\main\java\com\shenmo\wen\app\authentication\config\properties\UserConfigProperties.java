package com.shenmo.wen.app.authentication.config.properties;

import com.shenmo.wen.common.constant.BucketConstant;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 *
 * <AUTHOR>
 */
@Data
@RefreshScope
@ConfigurationProperties(UserConfigProperties.PREFIX)
public class UserConfigProperties {

    public static final String PREFIX = "user";

    private String avatar = String.format("/%s/avatar.png", BucketConstant.AVATAR);
}
