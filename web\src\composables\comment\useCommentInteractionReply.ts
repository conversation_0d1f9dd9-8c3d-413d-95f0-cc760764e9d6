import { nextTick, type Ref } from 'vue'

import commentApi from '@/api/comment'
import { COMMENT_QUICK_REPLY, COMMENT_SEND } from '@/constants/comment/frequency-key.constants'
import type { ResponseData } from '@/types/api/response-data.types'
import type { Comment } from '@/types/comment/comment.types'
import type { EditorInstance } from '@/types/editor/editor-instance.types'
import { type EditorContent } from '@/types/editor/editor-validation.types'
import {
  validateEditorContent,
  checkLoadingState,
  setLoadingState,
} from '@/utils/editor/editor-validation'
import frequencyLimit from '@/utils/performance/frequency-limit'
import tiptap from '@/utils/tiptap/tiptap'
import message from '@/utils/ui/message'

import type { Editor } from '@tiptap/vue-3'
import type { JSONContent } from '@tiptap/vue-3'

// 定义获取文章ID的函数类型
type GetArticleIdFunction = () => string

// 定义评论输入引用类型
interface CommentMainInputRef {
  sendTiptapEditorRef?: Editor & {
    clearContent: () => void
  }
}

/**
 * 评论回复交互组合式函数返回值类型
 */
export interface UseCommentInteractionReplyReturn {
  /** 清空所有快捷回复内容 */
  clearAllQuickReplyContent: () => (params: { commentList: Comment[] }) => void
  /** 处理评论回复点击 */
  handleCommentReplyClick: (comment: Comment, options: { isLastBreadcrumb: boolean }) => void
  /** 防抖快捷回复评论 */
  debouncedQuickReplyComment: (
    comment: Comment,
    options: { isLastBreadcrumb: boolean; onSuccess?: (commentId: string) => void },
  ) => void
  /** 防抖发送评论 */
  debouncedSendComment: (
    commentMainInputRef: CommentMainInputRef,
    options: { lastBreadcrumbComment: Comment; onSuccess?: (commentId: string) => void },
  ) => void
}

/**
 * 评论回复交互组合式函数
 * 提供评论回复相关的交互功能
 */
export function useCommentInteractionReply(
  quickReplyLoading: Ref<Map<string, boolean>>,
  sendCommentLoading: Ref<boolean>,
  commentInputVisible: Ref<string>,
  commentReply: Ref<JSONContent | undefined>,
  quickReplyTiptapEditorMap: Ref<Map<string, unknown>>,
  getArticleId: GetArticleIdFunction,
): UseCommentInteractionReplyReturn {
  // 清空所有快捷回复框内容
  const clearAllQuickReplyContent = () => {
    // 关闭所有回复框
    commentInputVisible.value = '-1'

    return ({ commentList }: { commentList: Comment[] }) => {
      // 清空所有评论的快捷回复内容
      commentList.forEach((comment) => {
        if (comment.quickCommentReply) {
          comment.quickCommentReply = undefined
        }
        const editor = quickReplyTiptapEditorMap.value.get(comment.id)
        if (editor && (editor as EditorInstance).commands) {
          ;(editor as EditorInstance).commands.clearContent()
        }
      })
    }
  }

  // 处理点击回复按钮
  const handleCommentReplyClick = (
    comment: Comment,
    { isLastBreadcrumb }: { isLastBreadcrumb: boolean },
  ) => {
    // 如果当前回复框已经打开，则关闭
    if (commentInputVisible.value === comment.id) {
      commentInputVisible.value = '-1'
      return
    }

    // 打开回复框
    commentInputVisible.value = comment.id

    // 如果是第三层评论，并且编辑器是空的，则添加@mention
    nextTick(() => {
      if (isLastBreadcrumb && !comment.fixed) {
        const editor = quickReplyTiptapEditorMap.value.get(comment.id)
        if (
          editor &&
          (!comment.quickCommentReply || !comment.quickCommentReply.content?.[0]?.content)
        ) {
          // 创建一个包含@用户名的初始内容
          const mentionContent = {
            type: 'doc',
            content: [
              {
                type: 'paragraph',
                content: [
                  {
                    type: 'mention',
                    attrs: {
                      id: comment.publisher,
                      label: comment.publisher,
                      avatar: comment.publisherAvatar || '',
                    },
                  },
                  {
                    type: 'text',
                    text: ' ',
                  },
                ],
              },
            ],
          }
          ;(editor as EditorInstance).commands.setContent(mentionContent)
        }
      }
    })
  }

  // 快捷回复评论
  const quickReplyComment = (
    comment: Comment,
    {
      isLastBreadcrumb,
      onSuccess,
    }: {
      isLastBreadcrumb: boolean
      onSuccess?: (commentId: string) => void
    },
  ) => {
    // 检查是否正在加载中
    if (!checkLoadingState(quickReplyLoading.value, comment.id)) {
      return
    }

    // 获取编辑器实例
    const editor = quickReplyTiptapEditorMap.value.get(comment.id)

    // 验证编辑器内容
    const contentRef = { value: comment.quickCommentReply as EditorContent }
    const validationResult = validateEditorContent(
      editor as any,
      contentRef,
      '啥也没有可不能发送哦~',
    )

    if (!validationResult.isValid) {
      return
    }

    // 更新内容引用
    comment.quickCommentReply = validationResult.content || contentRef.value

    // 设置loading状态
    setLoadingState(quickReplyLoading.value, true, comment.id)

    const content = tiptap.toJsonString(comment.quickCommentReply!)
    commentApi
      .save({
        content: content,
        articleId: getArticleId(),
        parentCommentId: isLastBreadcrumb && !comment.fixed ? comment.parentCommentId : comment.id,
      })
      .then((res: ResponseData<unknown>) => {
        message.success('发送成功')

        // 获取新评论的ID并调用onSuccess回调
        const commentId = res?.data
        if (commentId && onSuccess) {
          onSuccess(res.data as string)
        }

        // 清空编辑器并重置
        if (editor && (editor as EditorInstance).commands) {
          ;(editor as EditorInstance).commands.clearContent()
        }
        comment.quickCommentReply = undefined
      })
      .finally(() => {
        // 清除loading状态
        quickReplyLoading.value.set(comment.id, false)
      })
  }

  // 使用项目自带的防抖方法为快捷回复添加防抖
  const debouncedQuickReplyComment = (
    comment: Comment,
    options: {
      isLastBreadcrumb: boolean
      onSuccess?: (commentId: string) => void
    },
  ) => {
    frequencyLimit.debounce(
      `${COMMENT_QUICK_REPLY}-${comment.id}`,
      () => {
        quickReplyComment(comment, options)
      },
      300,
    )
  }

  // 发送主评论
  const sendComment = (
    commentMainInputRef: CommentMainInputRef,
    {
      lastBreadcrumbComment,
      onSuccess,
    }: {
      lastBreadcrumbComment: Comment
      onSuccess?: (commentId: string) => void
    },
  ) => {
    // 检查是否正在加载中
    if (!checkLoadingState(sendCommentLoading)) {
      return
    }

    // 获取编辑器实例
    const sendTiptapEditorRef = commentMainInputRef?.sendTiptapEditorRef

    // 验证编辑器内容
    const validationResult = validateEditorContent(
      sendTiptapEditorRef,
      { value: commentReply.value as EditorContent },
      '啥也没有可不能发送哦~',
    )

    if (!validationResult.isValid) {
      return
    }

    // 更新内容引用
    commentReply.value = validationResult.content || commentReply.value

    // 设置loading状态
    setLoadingState(sendCommentLoading, true)

    const content = tiptap.toJsonString(commentReply.value!)
    const comment = lastBreadcrumbComment
    const articleId = getArticleId()
    commentApi
      .save({
        content: content,
        articleId: articleId,
        parentCommentId: comment.id,
      })
      .then((res) => {
        message.success('发送成功')

        // 获取新评论的ID并调用onSuccess回调
        const commentId = res?.data
        if (commentId && onSuccess) {
          onSuccess(String(commentId))
        }

        // 清空编辑器内容
        sendTiptapEditorRef?.clearContent()
        commentReply.value = undefined
      })
      .finally(() => {
        // 清除loading状态
        sendCommentLoading.value = false
      })
  }

  // 使用项目自带的防抖方法为发送评论添加防抖
  const debouncedSendComment = (
    commentMainInputRef: CommentMainInputRef,
    options: {
      lastBreadcrumbComment: Comment
      onSuccess?: (commentId: string) => void
    },
  ) => {
    frequencyLimit.debounce(
      COMMENT_SEND,
      () => {
        sendComment(commentMainInputRef, options)
      },
      300,
    )
  }

  return {
    clearAllQuickReplyContent,
    handleCommentReplyClick,
    debouncedQuickReplyComment,
    debouncedSendComment,
  }
}
