import { ref, type Ref } from 'vue'

import type { CommentDanmakuRef } from '@/types/component/comment-danmaku-ref.types'

/**
 * 首页弹幕组合式函数返回值类型
 */
interface UseHomeDanmakuReturn {
  /** 弹幕循环状态 */
  danmakuLoop: Ref<boolean>
  /** 弹幕暂停状态 */
  danmakuPause: Ref<boolean>
  /** 处理弹幕暂停状态变化 */
  handleDanmakuPauseChange: (newVal: boolean, commentDanmakuRef: CommentDanmakuRef | null) => void
  /** 重置弹幕状态 */
  resetDanmakuState: () => void
  /** 处理弹幕订阅 */
  handleDanmakuSubscription: (
    commentDanmakuRef: Ref<CommentDanmakuRef | null>,
    subscribe: boolean,
  ) => void
  /** 处理弹幕窗口大小调整 */
  handleDanmakuResize: (
    commentDanmakuRef: Ref<CommentDanmakuRef | null>,
    isCardVisible: boolean,
  ) => void
}

/**
 * 首页弹幕管理组合式函数
 * 提供首页弹幕的状态管理、播放控制、订阅管理等功能
 */
export function useHomeDanmaku(): UseHomeDanmakuReturn {
  // 弹幕控制状态
  const danmakuLoop = ref(false)
  const danmakuPause = ref(false)

  /**
   * 处理弹幕暂停状态变化
   * 根据新的暂停状态控制弹幕的播放或暂停
   * @param newVal 新的暂停状态
   * @param commentDanmakuRef 评论弹幕组件引用
   */
  const handleDanmakuPauseChange = (
    newVal: boolean,
    commentDanmakuRef: CommentDanmakuRef | null,
  ): void => {
    if (commentDanmakuRef) {
      if (newVal) {
        commentDanmakuRef.pause()
      } else {
        commentDanmakuRef.play()
      }
    }
  }

  /**
   * 重置弹幕状态到初始值
   * 将循环和暂停状态都设置为 false
   */
  const resetDanmakuState = (): void => {
    danmakuLoop.value = false
    danmakuPause.value = false
  }

  /**
   * 处理弹幕视图的订阅和取消订阅
   * 控制弹幕组件是否接收新的评论数据
   * @param commentDanmakuRef 评论弹幕组件引用
   * @param subscribe 是否订阅弹幕
   */
  const handleDanmakuSubscription = (
    commentDanmakuRef: Ref<CommentDanmakuRef | null>,
    subscribe: boolean,
  ): void => {
    if (commentDanmakuRef.value) {
      if (subscribe) {
        commentDanmakuRef.value.subscribeComment()
      } else {
        commentDanmakuRef.value.unsubscribeComment()
        commentDanmakuRef.value.clearDanmaku()
      }
    }
  }

  /**
   * 处理窗口大小调整时的弹幕重置
   * 当窗口大小改变且在弹幕视图时，重置弹幕容器尺寸
   * @param commentDanmakuRef 评论弹幕组件引用
   * @param isCardVisible 卡片是否可见
   */
  const handleDanmakuResize = (
    commentDanmakuRef: Ref<CommentDanmakuRef | null>,
    isCardVisible: boolean,
  ): void => {
    if (!isCardVisible && commentDanmakuRef.value) {
      commentDanmakuRef.value.resize()
    }
  }

  return {
    // 状态
    danmakuLoop,
    danmakuPause,

    // 方法
    handleDanmakuPauseChange,
    resetDanmakuState,
    handleDanmakuSubscription,
    handleDanmakuResize,
  }
}
