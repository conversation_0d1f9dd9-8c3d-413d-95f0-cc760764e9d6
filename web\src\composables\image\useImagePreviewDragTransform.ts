import type { UseImagePreviewDragStateReturn } from './useImagePreviewDragState'

/**
 * 图片预览拖拽变换管理组合式函数返回值类型
 */
export interface UseImagePreviewDragTransformReturn {
  /** 更新图片变换 */
  updateTransform: () => void
  /** 重置变换状态 */
  resetTransform: () => void
  /** 滚轮缩放处理 */
  handleWheelZoom: (e: WheelEvent) => void
}

/**
 * 图片预览拖拽变换管理组合式函数
 * 提供图片变换和缩放功能
 */
export function useImagePreviewDragTransform(
  imageElement: HTMLImageElement,
  stateReturn: UseImagePreviewDragStateReturn,
): UseImagePreviewDragTransformReturn {
  const { state, performanceVars, options } = stateReturn
  const { minScale, maxScale } = options

  /**
   * 更新图片变换（使用 requestAnimationFrame 优化性能）
   */
  const updateTransform = (): void => {
    if (performanceVars.pendingTransform) return

    performanceVars.pendingTransform = true
    performanceVars.animationFrameId = requestAnimationFrame(() => {
      imageElement.style.transform = `translate3d(${state.translateX}px, ${state.translateY}px, 0) scale(${state.scale})`
      performanceVars.pendingTransform = false
    })
  }

  /**
   * 重置变换状态
   */
  const resetTransform = (): void => {
    // 取消待处理的动画帧
    if (performanceVars.animationFrameId) {
      cancelAnimationFrame(performanceVars.animationFrameId)
      performanceVars.animationFrameId = null
    }

    state.scale = 1
    state.translateX = 0
    state.translateY = 0
    state.isDragging = false
    state.isZooming = false
    stateReturn.scaleVars.isScaling = false
    performanceVars.pendingTransform = false

    imageElement.style.transform = ''
    imageElement.style.cursor = ''
    imageElement.style.willChange = ''
    imageElement.classList.remove('dragging', 'zooming')
  }

  /**
   * 滚轮缩放处理（以鼠标位置为中心）
   */
  const handleWheelZoom = (e: WheelEvent): void => {
    e.preventDefault()

    // 计算缩放因子
    const scaleFactor = e.deltaY > 0 ? 0.9 : 1.1
    const newScale = Math.max(minScale, Math.min(maxScale, state.scale * scaleFactor))

    if (Math.abs(newScale - state.scale) > 0.01) {
      // 获取鼠标相对于图片的位置
      const rect = imageElement.getBoundingClientRect()
      const mouseX = e.clientX - rect.left
      const mouseY = e.clientY - rect.top

      // 计算鼠标在图片坐标系中的位置
      const imageMouseX = (mouseX - rect.width / 2 - state.translateX) / state.scale
      const imageMouseY = (mouseY - rect.height / 2 - state.translateY) / state.scale

      // 更新缩放
      state.scale = newScale

      // 重新计算位置，使鼠标位置保持不变
      state.translateX = mouseX - rect.width / 2 - imageMouseX * state.scale
      state.translateY = mouseY - rect.height / 2 - imageMouseY * state.scale

      updateTransform()
    }
  }

  return {
    updateTransform,
    resetTransform,
    handleWheelZoom,
  }
}
