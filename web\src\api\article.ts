import { type ResponseData } from '@/types/api/response-data.types'
import type { ArticleDetailResponse } from '@/types/article/article-detail-response.types'
import type { ArticleOperationParams } from '@/types/article/article-request.types'
import type { ArticleSaveResponse } from '@/types/article/article-save-response.types'
import type { ArticleSearchParams } from '@/types/article/article-search-params.types'
import type { ArticleSummary } from '@/types/article/article-summary.types'
import type { Article } from '@/types/article/article.types'
import type { ExtendedEditor } from '@/types/tiptap/extended-editor.types'
import api from '@/utils/api/api'
import tiptap from '@/utils/tiptap/tiptap'

/**
 * 文章相关API接口
 * 提供文章的增删改查、搜索、下载等功能
 */
const articleApi = {
  /** API基础路径 */
  URL: '/core/articles',

  /**
   * 搜索文章
   * 根据指定条件搜索文章列表
   * @param params 搜索参数，包含关键词、标签、筛选条件等
   * @param signal 请求取消信号，用于中断请求
   * @returns 返回匹配的文章列表
   */
  search: async (
    params?: ArticleSearchParams,
    signal?: AbortSignal,
  ): Promise<ResponseData<Article[]>> => {
    const response = await api.get<ResponseData<Article[]>>(articleApi.URL, params, {
      signal,
    })
    return response.data
  },

  /**
   * 获取文章标题信息
   * 获取指定文章的标题和基本信息
   * @param id 文章ID
   * @returns 返回文章标题和基本信息
   */
  title: async (id: string): Promise<ResponseData<ArticleSummary>> => {
    const response = await api.get<ResponseData<ArticleSummary>>(
      articleApi.URL + '/' + id + '/title',
    )
    return response.data
  },

  /**
   * 获取文章详细内容
   * 获取指定文章的完整详细信息，包含内容和统计数据
   * @param id 文章ID
   * @returns 返回文章详细信息
   */
  detail: async (id: string): Promise<ResponseData<ArticleDetailResponse>> => {
    const response = await api.get<ResponseData<ArticleDetailResponse>>(articleApi.URL + '/' + id)
    return response.data
  },

  /**
   * 保存新文章
   * 创建一篇新的文章
   * @param params 文章信息，包含标题、内容、标签等
   * @returns 返回保存成功的文章ID和时间
   */
  save: async (params?: ArticleOperationParams): Promise<ResponseData<ArticleSaveResponse>> => {
    const response = await api.post<ResponseData<ArticleSaveResponse>>(
      articleApi.URL,
      params as Record<string, string | number | boolean | null | undefined>,
    )
    return response.data
  },

  /**
   * 编辑文章
   * 更新已存在的文章信息
   * @param params 文章更新信息，必须包含文章ID
   * @returns 返回更新操作结果
   */
  edit: async (params?: ArticleOperationParams): Promise<ResponseData<ArticleSaveResponse>> => {
    const response = await api.put<ResponseData<ArticleSaveResponse>>(
      articleApi.URL + '/' + params?.id,
      params as Record<string, string | number | boolean | null | undefined>,
    )
    return response.data
  },

  /**
   * 切换文章发布范围
   * 在公开和私有之间切换文章的发布范围
   * @param id 文章ID
   * @returns 返回切换操作结果
   */
  togglePublishedScope: async (id: string): Promise<ResponseData<void>> => {
    const response = await api.patch<ResponseData<void>>(`${articleApi.URL}/${id}/published-scope`)
    return response.data
  },

  /**
   * 获取热门标签
   * 获取使用频率最高的文章标签列表
   * @param limit 返回标签数量限制，默认为5个
   * @returns 返回热门标签列表
   */
  getHotTags: async (
    limit: number = 5,
  ): Promise<ResponseData<Array<{ name: string; count: number }>>> => {
    const response = await api.get<ResponseData<Array<{ name: string; count: number }>>>(
      `${articleApi.URL}/hot-tags`,
      {
        limit,
      },
    )
    return response.data
  },

  /**
   * 下载文章为Markdown文件
   * 将指定文章导出为Markdown格式并自动下载
   * @param id 文章ID
   * @param editor 编辑器实例，用于内容转换
   */
  md: async (id: string, editor: ExtendedEditor): Promise<void> => {
    try {
      // 下载接口返回的是字节流，不是 ResponseData 格式
      const response = await api.get<string>(articleApi.URL + '/' + id + '/file')
      if (response && response.data) {
        const data = response.data
        const dataList: string[] = data.split(/\r?\n/)
        const json = dataList.pop() || ''
        editor.setContent(tiptap.toJsonObject(json))
        const md = editor.getMarkdown()
        const blob = new Blob([dataList.join('\n') + '\n' + md], { type: 'text/markdown' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // 从响应头获取文件名
        const contentDisposition = response.headers['content-disposition']
        let filename = decodeURIComponent(
          contentDisposition?.split('filename=')[1] || `article-${id}`,
        )
        filename = filename + '.md'

        document.body.appendChild(link)
        link.download = filename
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      }
    } catch (error) {
      console.error('下载文章失败:', error)
      throw error
    }
  },

  /**
   * 删除文章
   * 永久删除指定的文章
   * @param id 文章ID
   * @returns 返回删除操作结果
   */
  delete: async (id: string): Promise<ResponseData<void>> => {
    const response = await api.del<ResponseData<void>>(`${articleApi.URL}/${id}`)
    return response.data
  },
}

export default articleApi
