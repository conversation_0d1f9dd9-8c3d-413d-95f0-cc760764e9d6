<!--
  激活码输入弹框组件
  
  功能说明：
  - 提供激活码输入界面
  - 支持激活码格式验证（XXXX-XXXX-XXXX-XXXX-XXXX-XXXX）
  - 自动调用激活接口
  - 激活成功后跳转到特权页面
-->
<template>
  <NModal v-model:show="visible" preset="dialog" title="输入激活码" :mask-closable="false">
    <div class="activation-code-modal">
      <div class="activation-code-input">
        <NInput
          v-model:value="activationCode"
          placeholder="请输入激活码（XXXX-XXXX-XXXX-XXXX-XXXX-XXXX）"
          :maxlength="29"
          :loading="isActivating"
          @input="handleCodeInput"
          @keyup.enter="handleActivate"
        />
        <div class="code-format-hint">格式：XXXX-XXXX-XXXX-XXXX-XXXX-XXXX（6组，每组4个字符）</div>
      </div>

      <div class="modal-actions">
        <NButton @click="handleCancel" :disabled="isActivating">取消</NButton>
        <NButton
          type="primary"
          @click="handleActivate"
          :loading="isActivating"
          :disabled="!isCodeValid"
        >
          激活
        </NButton>
      </div>
    </div>
  </NModal>
</template>

<script lang="ts" setup>
import { NModal, NInput, NButton, useMessage } from 'naive-ui'
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

import privilegeApi from '@/api/privilege'

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

const router = useRouter()
const message = useMessage()

// 响应式状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const activationCode = ref('')
const isActivating = ref(false)

// 验证激活码格式
const isCodeValid = computed(() => {
  const codePattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/
  return codePattern.test(activationCode.value)
})

/**
 * 处理激活码输入
 * 自动格式化输入的激活码
 */
const handleCodeInput = (value: string) => {
  // 移除所有非字母数字字符，转换为大写
  const cleanValue = value.replace(/[^A-Z0-9]/g, '').toUpperCase()

  // 自动添加连字符
  let formattedValue = ''
  for (let i = 0; i < cleanValue.length; i++) {
    if (i > 0 && i % 4 === 0) {
      formattedValue += '-'
    }
    formattedValue += cleanValue[i]
  }

  activationCode.value = formattedValue

  // 如果输入完整的24个字符，自动调用激活接口
  if (cleanValue.length === 24) {
    handleActivate()
  }
}

/**
 * 处理激活操作
 */
const handleActivate = async () => {
  if (!isCodeValid.value) {
    message.warning('请输入正确格式的激活码')
    return
  }

  isActivating.value = true

  try {
    const response = await privilegeApi.activate({ code: activationCode.value })

    if (response.success && response.data) {
      message.success('激活成功！')
      visible.value = false
      emit('success')

      // 跳转到特权页面
      await router.push('/privilege')
    } else {
      message.error('激活失败，请检查激活码是否正确')
    }
  } catch (error) {
    console.error('激活失败:', error)
    message.error('激活失败，请稍后重试')
  } finally {
    isActivating.value = false
  }
}

/**
 * 处理取消操作
 */
const handleCancel = () => {
  visible.value = false
  activationCode.value = ''
}

/**
 * 重置组件状态
 */
const reset = () => {
  activationCode.value = ''
  isActivating.value = false
}

// 暴露方法给父组件
defineExpose({
  reset,
})
</script>

<style lang="scss" scoped>
.activation-code-modal {
  padding: 20px 0;

  .activation-code-input {
    margin-bottom: 20px;

    .code-format-hint {
      margin-top: 8px;
      font-size: 12px;
      color: var(--text-color-3);
      text-align: center;
    }
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
